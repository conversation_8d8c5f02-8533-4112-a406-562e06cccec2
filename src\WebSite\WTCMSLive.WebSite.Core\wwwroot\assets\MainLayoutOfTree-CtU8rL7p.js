import{u as U,a as X,r as v,w as L,c as b,o as x,b as w,d as R,F as te,e as ae,f as F,g as O,t as V,l as ne,h as Z,i as P,j as J,k as se,m as oe,n as le,p as Q,q as z,s as re,v as ce}from"./index-sMW2Pm6g.js";import{_ as j}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{_ as ie,M as de,S as ue}from"./ActionButton-DMeJvyLo.js";import{u as ee}from"./devTree-BcT4pWCP.js";import{_ as pe}from"./index-DRy8se-f.js";import{_ as he}from"./index-CkAETRMb.js";import{_ as me,H as ve,L as fe,a as _e}from"./index-51I_ips6.js";import"./styleChecker-LI4Lr2UF.js";import"./index-Bi-LLAnN.js";import"./shallowequal-D09g54zQ.js";import"./tools-DC78Tda0.js";import"./index-BLrmE7-A.js";import"./index-sJyM5xm7.js";import"./ChangeLanguage.vue_vue_type_style_index_0_scoped_f1388662_lang-l0sNRNKZ.js";const ge={class:"side-navigation"},ye={__name:"SideNavigation",props:{currentSideNav:{type:Array,default:[]}},emits:["item-click"],setup(E,{emit:u}){const f=E,h=U();X();const a=v([]),_=v(!1),o=u,K=()=>{if(!f.currentSideNav.length)return;const d=f.currentSideNav.find(m=>m.path===h.path);if(d)return d.path;const g=f.currentSideNav.find(m=>{const S=m.path.replace(/\/:[^/]+/g,"/[^/]+");return new RegExp(`^${S}$`).test(h.path)});return(g==null?void 0:g.path)||""},M=()=>{const d=K();d&&(a.value=[d])},C=d=>{a.value=[d.path],o("item-click",d)};return L(()=>h.path,M,{immediate:!0}),L(()=>f.currentSideNav,M,{deep:!0}),(d,g)=>{const m=ie,S=de;return x(),b("div",ge,[w(S,{selectedKeys:a.value,"onUpdate:selectedKeys":g[0]||(g[0]=y=>a.value=y),mode:"horizontal",collapsed:_.value},{default:R(()=>[(x(!0),b(te,null,ae(E.currentSideNav,y=>(x(),F(m,{key:y.path,title:d.$t(y.meta.title),onClick:H=>C(y)},{default:R(()=>[O(V(d.$t(y.meta.title)),1)]),_:2},1032,["title","onClick"]))),128))]),_:1},8,["selectedKeys","collapsed"])])}}},Ne=j(ye,[["__scopeId","data-v-e4e3b23e"]]),Te={class:"treeContainer"},Ce={class:"treeContent"},De={key:0},ke={style:{color:"#f50"}},we={key:1},xe={__name:"index",props:{treeDatas:{type:Array,default:[]}},emits:["node-click"],setup(E,{emit:u}){const f=U(),h=ee(),a=v([]),_=v([]),o=v(""),K=v(!0),M=u,C=v(),d=E,g=()=>{if(d.treeDatas&&d.treeDatas.length)if(a.value=d.treeDatas,f.params&&f.params.id){const l=h.getNodeById(f.params.id);let n=h.findAncestorsWithNodes(f.params.id);n&&n.length&&(_.value=n.map(D=>D.key)),l&&(C.value=[l.key])}else _.value=["0"]};L(()=>d.treeDatas,l=>{a.value=l,g()},{deep:!0});function m(l,n){if(!n)return l;const D=$=>{const e=[];for(const t of $){const s={...t};t.children&&t.children.length>0?s.children=D(t.children):s.children=[];const k=t.title&&t.title.toLowerCase().includes(n.toLowerCase()),N=s.children&&s.children.length>0;(k||N)&&e.push(s)}return e};return D(l)}L(()=>o.value,l=>{console.log("searchValue",l),console.log("props.treeDatas",d.treeDatas),a.value=m(d.treeDatas,l),console.log("dataList.value",a.value)},{deep:!0}),L(()=>f.path,()=>{g()},{immediate:!0});const S=(l,n,D)=>{for(let $=0;$<D.length;$++){const e=D[$];e.title.indexOf(l)>-1?n.push(e.key):e.children&&e.children.length&&(n=S(l,n,e.children))}return n},y=(l,n)=>{M("node-click",n),C.value=[n.node.key]},H=l=>{_.value=l},A=ne.debounce(l=>{const n=[];S(l,n,a.value),_.value=n,o.value=l,l||(_.value=[])},300);return L(o,A),Z(()=>{g()}),(l,n)=>{const D=pe,$=he;return x(),b("div",Te,[w(D,{value:o.value,"onUpdate:value":n[0]||(n[0]=e=>o.value=e),style:{"margin-top":"8px"},placeholder:"搜索",class:"search"},null,8,["value"]),P("div",Ce,[w($,{"expanded-keys":_.value,selectedKeys:C.value,"onUpdate:selectedKeys":n[1]||(n[1]=e=>C.value=e),"auto-expand-parent":K.value,"tree-data":a.value,onExpand:H,onSelect:y},{title:R(({title:e})=>[e.indexOf(o.value)>-1?(x(),b("span",De,[O(V(e.substring(0,e.indexOf(o.value)))+" ",1),P("span",ke,V(o.value),1),O(" "+V(e.substring(e.indexOf(o.value)+o.value.length)),1)])):(x(),b("span",we,V(e),1))]),_:1},8,["expanded-keys","selectedKeys","auto-expand-parent","tree-data"])])])}}},Se=j(xe,[["__scopeId","data-v-6ffba97f"]]),$e={class:"devTreeContainer"},Re={key:0,class:"rightContentTop"},Me={key:1,class:"footer"},Ie={class:"copyright"},Le={__name:"MainLayoutOfTree",setup(E){const u=ee(),f=v(!1),h=X(),a=U(),_=v(!1),o=v([]),K=v([]),M=v([]),C=v(null),d=v(window.localStorage.getItem("version")||""),g=new Date().getFullYear(),m=v(window.localStorage.getItem("templateManagement")==="true"),S=J(()=>h.getRoutes().filter(e=>{var t;return(t=e.meta)==null?void 0:t.parent})),y=J(()=>{var e;return!(o.value.length&&o.value.length==1&&((e=o.value[0].meta)!=null&&e.hideMenu))}),H=(e,t)=>{var p,T,B,c;e||(e="/monitor");let s=t?t.node.key:(p=a.params)==null?void 0:p.id,k=t?t.node.type:(T=a.meta)==null?void 0:T.selectNodeType;const N=t?t.node:u.getNodeById(s);if(N.type!==k){let i=N.value||u.getDevTreeCurrentNode();l(i)}const r=S.value.find(i=>i.path===e);if(r&&((B=r.children)!=null&&B.length)){let i=[],q=r.path;(c=r.meta)!=null&&c.isDiffrentChildren?r.children.map(I=>{var W,Y,G;!((W=I.meta)!=null&&W.templateViewHidden&&m.value)&&((Y=I.meta)!=null&&Y.selectNodeType)&&((G=I.meta)==null?void 0:G.selectNodeType)==k&&i.push({...I,path:`${q}/${I.path}`})}):r.children.map(I=>{i.push({...I,path:`${q}/${I.path}`})}),o.value=i,i.length>0&&s&&h.push({name:i[0].name,params:{id:s}})}else h.push(e),o.value=[]},A=e=>{var s;let t=(s=a.params)==null?void 0:s.id;t&&h.push({name:e.name,params:{id:t}})},l=e=>{var N,r;C.value=e.node;let t=(N=a.meta)==null?void 0:N.selectNodeType,s=e.node.type;const k=a;if((r=a.meta)!=null&&r.isDiffrentChildren&&t&&t!==s&&a.matched.length>1&&a.matched[0].children.length>0){let p=a.matched[0].path,T=[];if(a.matched[0].children.find(c=>c.meta.selectNodeType&&c.meta.selectNodeType==s)?a.matched[0].children.map(c=>{var i;!((i=c.meta)!=null&&i.templateViewHidden&&m.value)&&c.meta.selectNodeType==e.node.type&&T.push({...c,path:`${p}/${c.path}`})}):S.value.map(c=>{if(c.children&&c.children.length&&T.length<1)for(let i=0;i<c.children.length;i++)c.children[i].meta.selectNodeType==s&&T.push({...c.children[i],path:`${c.path}/${c.children[i].path}`})}),T&&T.length>0)o.value=T;else{H(!1,e);return}}u.setDevTreeCurrentNode(e.node),t&&t===e.node.type?h.push({name:k.name,params:{id:e.node.key}}):o.value.length>0&&h.push({name:o.value[0].name,params:{id:e.node.key}})},n=()=>{var k,N;const t=a.matched;let s=null;for(let r=t.length-1;r>=0;r--){const p=t[r];if((k=p.meta)!=null&&k.parent){s=p;break}}if(s){let r=[];(N=s.meta)!=null&&N.isDiffrentChildren&&t.length>1?s.children.map(p=>{var T,B,c,i;!((T=p.meta)!=null&&T.templateViewHidden&&m.value)&&((B=p.meta)!=null&&B.selectNodeType)&&((c=p.meta)==null?void 0:c.selectNodeType)==((i=t[1].meta)==null?void 0:i.selectNodeType)&&r.push({...p,path:`${s.path}/${p.path}`})}):s.children.map(p=>{r.push({...p,path:`${s.path}/${p.path}`})}),o.value=r}else o.value=[]};se(async()=>{try{f.value=!0,await u.getDevTreeDatas(m.value?"template":""),u.treeDatas.length||oe.error("获取设备树数据失败"),$(),_.value=!0,K.value=a.matched.map(e=>({path:e.path,name:e.meta.title||e.name}))}catch(e){console.error("获取设备树数据失败:",e),_.value=!0}finally{f.value=!1}}),Z(()=>{h.isReady?h.isReady().then(()=>{n()}):n()}),L(()=>a.path,()=>{K.value=a.matched.map(e=>({path:e.path,name:e.meta.title||e.name}))},{immediate:!0}),L(()=>u.treeDatas,()=>{M.value=[...u.treeDatas],C.value=u.getDevTreeCurrentNode()});const D=async e=>{e=="enter"?(m.value=!0,await u.getDevTreeDatas("template"),await h.push({name:"model",params:{id:"0"}})):(m.value=!1,await u.getDevTreeDatas(),ce(a,h)),n()},$=()=>{u.treeDatas&&u.treeDatas.length&&(M.value=[...u.treeDatas],C.value=u.getDevTreeCurrentNode())};return(e,t)=>{const s=fe,k=le("router-view"),N=_e,r=me,p=ue;return x(),F(r,null,{default:R(()=>[w(ve,{onMenuSelect:H,onChangeView:D,currentTreeNode:C.value},null,8,["currentTreeNode"]),w(p,{spinning:f.value,size:"large"},{default:R(()=>[w(r,{class:"centerContainer"},{default:R(()=>[w(s,{width:"260"},{default:R(()=>[P("div",$e,[w(Se,{treeDatas:M.value,onNodeClick:l},null,8,["treeDatas"])])]),_:1}),w(r,{class:Q(["rightContent",m.value?"templateBorder":""])},{default:R(()=>[y.value?(x(),b("div",Re,[w(Ne,{currentSideNav:o.value,onItemClick:A},null,8,["currentSideNav"])])):z("",!0),P("div",{class:Q(y.value?"rightContentContent":null)},[_.value?(x(),F(N,{key:0,class:"content"},{default:R(()=>[w(k)]),_:1})):z("",!0)],2),_.value?(x(),b("div",Me,[P("div",Ie,[O(" © "+V(re(g))+" V"+V(d.value)+" ",1),t[0]||(t[0]=P("span",null,"配置网站",-1)),t[1]||(t[1]=O(" All Rights Reserved ",-1))])])):z("",!0)]),_:1},8,["class"])]),_:1})]),_:1},8,["spinning"])]),_:1})}}},We=j(Le,[["__scopeId","data-v-11fcf8c5"]]);export{We as default};
