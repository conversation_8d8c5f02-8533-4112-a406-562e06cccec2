import{_ as T,H as j,bc as Ce,j as m,as as Se,bh as M,r as be,x as Ie,b as g,I as d,cu as q,cq as x,w as xe,N as u,b6 as te}from"./index-sMW2Pm6g.js";import{R as ae,n as re}from"./styleChecker-LI4Lr2UF.js";const ot={BACKSPACE:8,TAB:9,ENTER:13,SHIFT:16,CTRL:17,ALT:18,CAPS_LOCK:20,ESC:27,SPACE:32,PAGE_UP:33,PAGE_DOWN:34,LEFT:37,UP:38,RIGHT:39,DOWN:40,N:78,P:80,META:91,WIN_KEY_RIGHT:92,CONTEXT_MENU:93,F1:112,F2:113,F3:114,F4:115,F5:116,F6:117,F7:118,F8:119,F9:120,F10:121,F11:122,F12:123,SEMICOLON:186,EQUALS:187,WIN_KEY:224},Re=`accept acceptcharset accesskey action allowfullscreen allowtransparency
alt async autocomplete autofocus autoplay capture cellpadding cellspacing challenge
charset checked classid classname colspan cols content contenteditable contextmenu
controls coords crossorigin data datetime default defer dir disabled download draggable
enctype form formaction formenctype formmethod formnovalidate formtarget frameborder
headers height hidden high href hreflang htmlfor for httpequiv icon id inputmode integrity
is keyparams keytype kind label lang list loop low manifest marginheight marginwidth max maxlength media
mediagroup method min minlength multiple muted name novalidate nonce open
optimum pattern placeholder poster preload radiogroup readonly rel required
reversed role rowspan rows sandbox scope scoped scrolling seamless selected
shape size sizes span spellcheck src srcdoc srclang srcset start step style
summary tabindex target title type usemap value width wmode wrap`,Ee=`onCopy onCut onPaste onCompositionend onCompositionstart onCompositionupdate onKeydown
    onKeypress onKeyup onFocus onBlur onChange onInput onSubmit onClick onContextmenu onDoubleclick onDblclick
    onDrag onDragend onDragenter onDragexit onDragleave onDragover onDragstart onDrop onMousedown
    onMouseenter onMouseleave onMousemove onMouseout onMouseover onMouseup onSelect onTouchcancel
    onTouchend onTouchmove onTouchstart onTouchstartPassive onTouchmovePassive onScroll onWheel onAbort onCanplay onCanplaythrough
    onDurationchange onEmptied onEncrypted onEnded onError onLoadeddata onLoadedmetadata
    onLoadstart onPause onPlay onPlaying onProgress onRatechange onSeeked onSeeking onStalled onSuspend onTimeupdate onVolumechange onWaiting onLoad onError`,ne=`${Re} ${Ee}`.split(/[\s\n]+/),Ke="aria-",Pe="data-";function oe(e,o){return e.indexOf(o)===0}function at(e){let o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,a;o===!1?a={aria:!0,data:!0,attr:!0}:o===!0?a={aria:!0}:a=T({},o);const t={};return Object.keys(e).forEach(n=>{(a.aria&&(n==="role"||oe(n,Ke))||a.data&&oe(n,Pe)||a.attr&&(ne.includes(n)||ne.includes(n.toLowerCase())))&&(t[n]=e[n])}),t}const se=Symbol("OverflowContextProviderKey"),H=j({compatConfig:{MODE:3},name:"OverflowContextProvider",inheritAttrs:!1,props:{value:{type:Object}},setup(e,o){let{slots:a}=o;return Se(se,m(()=>e.value)),()=>{var t;return(t=a.default)===null||t===void 0?void 0:t.call(a)}}}),Me=()=>Ce(se,m(()=>null));var Fe=function(e,o){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,t=Object.getOwnPropertySymbols(e);n<t.length;n++)o.indexOf(t[n])<0&&Object.prototype.propertyIsEnumerable.call(e,t[n])&&(a[t[n]]=e[t[n]]);return a};const P=void 0,z=j({compatConfig:{MODE:3},name:"Item",props:{prefixCls:String,item:M.any,renderItem:Function,responsive:Boolean,itemKey:{type:[String,Number]},registerSize:Function,display:Boolean,order:Number,component:M.any,invalidate:Boolean},setup(e,o){let{slots:a,expose:t}=o;const n=m(()=>e.responsive&&!e.display),i=be();t({itemNodeRef:i});function O(l){e.registerSize(e.itemKey,l)}return Ie(()=>{O(null)}),()=>{var l;const{prefixCls:v,invalidate:h,item:y,renderItem:p,responsive:w,registerSize:A,itemKey:R,display:U,order:E,component:L="div"}=e,b=Fe(e,["prefixCls","invalidate","item","renderItem","responsive","registerSize","itemKey","display","order","component"]),W=(l=a.default)===null||l===void 0?void 0:l.call(a),k=p&&y!==P?p(y):W;let S;h||(S={opacity:n.value?0:1,height:n.value?0:P,overflowY:n.value?"hidden":P,order:w?E:P,pointerEvents:n.value?"none":P,position:n.value?"absolute":P});const K={};return n.value&&(K["aria-hidden"]=!0),g(ae,{disabled:!w,onResize:F=>{let{offsetWidth:X}=F;O(X)}},{default:()=>g(L,d(d(d({class:q(!h&&v),style:S},K),b),{},{ref:i}),{default:()=>[k]})})}}});var G=function(e,o){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,t=Object.getOwnPropertySymbols(e);n<t.length;n++)o.indexOf(t[n])<0&&Object.prototype.propertyIsEnumerable.call(e,t[n])&&(a[t[n]]=e[t[n]]);return a};const Ne=j({compatConfig:{MODE:3},name:"RawItem",inheritAttrs:!1,props:{component:M.any,title:M.any,id:String,onMouseenter:{type:Function},onMouseleave:{type:Function},onClick:{type:Function},onKeydown:{type:Function},onFocus:{type:Function},role:String,tabindex:Number},setup(e,o){let{slots:a,attrs:t}=o;const n=Me();return()=>{var i;if(!n.value){const{component:p="div"}=e,w=G(e,["component"]);return g(p,d(d({},w),t),{default:()=>[(i=a.default)===null||i===void 0?void 0:i.call(a)]})}const O=n.value,{className:l}=O,v=G(O,["className"]),{class:h}=t,y=G(t,["class"]);return g(H,{value:null},{default:()=>[g(z,d(d(d({class:q(l,h)},v),y),e),a)]})}}});var _e=function(e,o){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,t=Object.getOwnPropertySymbols(e);n<t.length;n++)o.indexOf(t[n])<0&&Object.prototype.propertyIsEnumerable.call(e,t[n])&&(a[t[n]]=e[t[n]]);return a};const ie="responsive",le="invalidate";function De(e){return`+ ${e.length} ...`}const Te=()=>({id:String,prefixCls:String,data:Array,itemKey:[String,Number,Function],itemWidth:{type:Number,default:10},renderItem:Function,renderRawItem:Function,maxCount:[Number,String],renderRest:Function,renderRawRest:Function,suffix:M.any,component:String,itemComponent:M.any,onVisibleChange:Function,ssr:String,onMousedown:Function,role:String}),B=j({name:"Overflow",inheritAttrs:!1,props:Te(),emits:["visibleChange"],setup(e,o){let{attrs:a,emit:t,slots:n}=o;const i=m(()=>e.ssr==="full"),O=x(null),l=m(()=>O.value||0),v=x(new Map),h=x(0),y=x(0),p=x(0),w=x(null),A=x(null),R=m(()=>A.value===null&&i.value?Number.MAX_SAFE_INTEGER:A.value||0),U=x(!1),E=m(()=>`${e.prefixCls}-item`),L=m(()=>Math.max(h.value,y.value)),b=m(()=>!!(e.data.length&&e.maxCount===ie)),W=m(()=>e.maxCount===le),k=m(()=>b.value||typeof e.maxCount=="number"&&e.data.length>e.maxCount),S=m(()=>{let r=e.data;return b.value?O.value===null&&i.value?r=e.data:r=e.data.slice(0,Math.min(e.data.length,l.value/e.itemWidth)):typeof e.maxCount=="number"&&(r=e.data.slice(0,e.maxCount)),r}),K=m(()=>b.value?e.data.slice(R.value+1):e.data.slice(S.value.length)),F=(r,s)=>{var c;return typeof e.itemKey=="function"?e.itemKey(r):(c=e.itemKey&&(r==null?void 0:r[e.itemKey]))!==null&&c!==void 0?c:s},X=m(()=>e.renderItem||(r=>r)),$=(r,s)=>{A.value=r,s||(U.value=r<e.data.length-1,t("visibleChange",r))},ue=(r,s)=>{O.value=s.clientWidth},Q=(r,s)=>{const c=new Map(v.value);s===null?c.delete(r):c.set(r,s),v.value=c},ce=(r,s)=>{h.value=y.value,y.value=s},de=(r,s)=>{p.value=s},Y=r=>v.value.get(F(S.value[r],r));return xe([l,v,y,p,()=>e.itemKey,S],()=>{if(l.value&&L.value&&S.value){let r=p.value;const s=S.value.length,c=s-1;if(!s){$(0),w.value=null;return}for(let C=0;C<s;C+=1){const N=Y(C);if(N===void 0){$(C-1,!0);break}if(r+=N,c===0&&r<=l.value||C===c-1&&r+Y(c)<=l.value){$(c),w.value=null;break}else if(r+L.value>l.value){$(C-1),w.value=r-N-p.value+y.value;break}}e.suffix&&Y(0)+p.value>l.value&&(w.value=null)}}),()=>{const r=U.value&&!!K.value.length,{itemComponent:s,renderRawItem:c,renderRawRest:C,renderRest:N,prefixCls:fe="rc-overflow",suffix:J,component:me="div",id:ve,onMousedown:ye}=e,{class:pe,style:ge}=a,Oe=_e(a,["class","style"]);let Z={};w.value!==null&&b.value&&(Z={position:"absolute",left:`${w.value}px`,top:0});const _={prefixCls:E.value,responsive:b.value,component:s,invalidate:W.value},he=c?(f,I)=>{const D=F(f,I);return g(H,{key:D,value:T(T({},_),{order:I,item:f,itemKey:D,registerSize:Q,display:I<=R.value})},{default:()=>[c(f,I)]})}:(f,I)=>{const D=F(f,I);return g(z,d(d({},_),{},{order:I,key:D,item:f,renderItem:X.value,itemKey:D,registerSize:Q,display:I<=R.value}),null)};let V=()=>null;const ee={order:r?R.value:Number.MAX_SAFE_INTEGER,className:`${E.value} ${E.value}-rest`,registerSize:ce,display:r};if(C)C&&(V=()=>g(H,{value:T(T({},_),ee)},{default:()=>[C(K.value)]}));else{const f=N||De;V=()=>g(z,d(d({},_),ee),{default:()=>typeof f=="function"?f(K.value):f})}const we=()=>{var f;return g(me,d({id:ve,class:q(!W.value&&fe,pe),style:ge,onMousedown:ye,role:e.role},Oe),{default:()=>[S.value.map(he),k.value?V():null,J&&g(z,d(d({},_),{},{order:R.value,class:`${E.value}-suffix`,registerSize:de,display:!0,style:Z}),{default:()=>J}),(f=n.default)===null||f===void 0?void 0:f.call(n)]})};return g(ae,{disabled:!b.value,onResize:ue},{default:we})}}});B.Item=Ne;B.RESPONSIVE=ie;B.INVALIDATE=le;const Ae=new u("antMoveDownIn",{"0%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),Le=new u("antMoveDownOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0}}),We=new u("antMoveLeftIn",{"0%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),$e=new u("antMoveLeftOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),ze=new u("antMoveRightIn",{"0%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),je=new u("antMoveRightOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),Ue=new u("antMoveUpIn",{"0%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),ke=new u("antMoveUpOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0}}),Xe={"move-up":{inKeyframes:Ue,outKeyframes:ke},"move-down":{inKeyframes:Ae,outKeyframes:Le},"move-left":{inKeyframes:We,outKeyframes:$e},"move-right":{inKeyframes:ze,outKeyframes:je}},rt=(e,o)=>{const{antCls:a}=e,t=`${a}-${o}`,{inKeyframes:n,outKeyframes:i}=Xe[o];return[re(t,n,i,e.motionDurationMid),{[`
        ${t}-enter,
        ${t}-appear
      `]:{opacity:0,animationTimingFunction:e.motionEaseOutCirc},[`${t}-leave`]:{animationTimingFunction:e.motionEaseInOutCirc}}]},Ye=new u("antSlideUpIn",{"0%":{transform:"scaleY(0.8)",transformOrigin:"0% 0%",opacity:0},"100%":{transform:"scaleY(1)",transformOrigin:"0% 0%",opacity:1}}),Ve=new u("antSlideUpOut",{"0%":{transform:"scaleY(1)",transformOrigin:"0% 0%",opacity:1},"100%":{transform:"scaleY(0.8)",transformOrigin:"0% 0%",opacity:0}}),Ge=new u("antSlideDownIn",{"0%":{transform:"scaleY(0.8)",transformOrigin:"100% 100%",opacity:0},"100%":{transform:"scaleY(1)",transformOrigin:"100% 100%",opacity:1}}),He=new u("antSlideDownOut",{"0%":{transform:"scaleY(1)",transformOrigin:"100% 100%",opacity:1},"100%":{transform:"scaleY(0.8)",transformOrigin:"100% 100%",opacity:0}}),qe=new u("antSlideLeftIn",{"0%":{transform:"scaleX(0.8)",transformOrigin:"0% 0%",opacity:0},"100%":{transform:"scaleX(1)",transformOrigin:"0% 0%",opacity:1}}),Be=new u("antSlideLeftOut",{"0%":{transform:"scaleX(1)",transformOrigin:"0% 0%",opacity:1},"100%":{transform:"scaleX(0.8)",transformOrigin:"0% 0%",opacity:0}}),Qe=new u("antSlideRightIn",{"0%":{transform:"scaleX(0.8)",transformOrigin:"100% 0%",opacity:0},"100%":{transform:"scaleX(1)",transformOrigin:"100% 0%",opacity:1}}),Je=new u("antSlideRightOut",{"0%":{transform:"scaleX(1)",transformOrigin:"100% 0%",opacity:1},"100%":{transform:"scaleX(0.8)",transformOrigin:"100% 0%",opacity:0}}),Ze={"slide-up":{inKeyframes:Ye,outKeyframes:Ve},"slide-down":{inKeyframes:Ge,outKeyframes:He},"slide-left":{inKeyframes:qe,outKeyframes:Be},"slide-right":{inKeyframes:Qe,outKeyframes:Je}},st=(e,o)=>{const{antCls:a}=e,t=`${a}-${o}`,{inKeyframes:n,outKeyframes:i}=Ze[o];return[re(t,n,i,e.motionDurationMid),{[`
      ${t}-enter,
      ${t}-appear
    `]:{transform:"scale(0)",transformOrigin:"0% 0%",opacity:0,animationTimingFunction:e.motionEaseOutQuint},[`${t}-leave`]:{animationTimingFunction:e.motionEaseInQuint}}]};function et(e,o,a,t){let n;if(n!==void 0)return!!n;if(e===o)return!0;if(typeof e!="object"||!e||typeof o!="object"||!o)return!1;const i=Object.keys(e),O=Object.keys(o);if(i.length!==O.length)return!1;const l=Object.prototype.hasOwnProperty.bind(o);for(let v=0;v<i.length;v++){const h=i[v];if(!l(h))return!1;const y=e[h],p=o[h];if(n=void 0,n===!1||n===void 0&&y!==p)return!1}return!0}function it(e,o){return et(te(e),te(o))}export{ot as K,B as O,rt as a,Ve as b,Ge as c,Ye as d,it as e,st as i,at as p,He as s};
