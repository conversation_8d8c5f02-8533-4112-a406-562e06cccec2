import{T as a,d_ as s}from"./index-sMW2Pm6g.js";import{b as d}from"./tools-DC78Tda0.js";const f=a("devTree",{state:()=>({treeDatas:[],devTreeCurrentNode:{},templateDeviceList:[],templateDevicoptions:[]}),actions:{async getDevTreeDatas(t){let i=!1;t?i=t==="template":i=window.localStorage.getItem("templateManagement")==="true";let r=[{key:"0",title:"设备树",type:"root",children:[],disabled:!0}],e=[];i?(r[0].title="设备模板树",e=await s({parkID:"HN999"})):e=await s(),e&&e.length>0&&(r[0].children=e,window.localStorage.setItem("homeNodeId",JSON.stringify(e[0].id))),this.devTreeCurrentNode=e[0],this.treeDatas=r},async getTemplateParkList(){let t=await s({parkID:"HN999"});if(this.templateDeviceList=t,t&&t.length>0&&t[0].children&&t[0].children.length>0){let i=d(t[0].children,{label:"title",value:"id"},{nother:!0});this.templateDevicoptions=i}},setDevTreeCurrentNode(t){this.devTreeCurrentNode=t},getDevTreeCurrentNode(){return this.devTreeCurrentNode},getNodeById(t,i=this.treeDatas){for(const r of i){if(r.key===t)return r;if(r.children&&r.children.length){const e=this.getNodeById(t,r.children);if(e)return e}}},findAncestorsWithNodes(t,i=this.treeDatas){function r(e,o){if(o.push(e),e.key===t)return[...o];if(e.children)for(const l of e.children){const n=r(l,o);if(n)return n}return o.pop(),null}for(const e of i){const o=r(e,[]);if(o)return o}return[]}}});export{f as u};
