import{_ as a}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{R as n}from"./index-BKrPWEEO.js";import{B as c}from"./index-Bi-LLAnN.js";import{f as p,d as o,o as _,b as m,g as i}from"./index-sMW2Pm6g.js";const u={name:"Exception500",methods:{toHome(){this.$router.push({path:"/"})}}};function f(l,t,d,x,h,e){const r=c,s=n;return _(),p(s,{status:"500",title:"500","sub-title":"Sorry, the server is reporting an error."},{extra:o(()=>[m(r,{type:"primary",onClick:e.toHome},{default:o(()=>t[0]||(t[0]=[i(" Back Home ",-1)])),_:1,__:[0]},8,["onClick"])]),_:1})}const y=a(u,[["render",f]]);export{y as default};
