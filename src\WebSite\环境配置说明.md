# ASP.NET Core 环境配置说明

## 📁 配置文件说明

### 配置文件加载顺序
1. `appsettings.json` - 基础配置（总是加载）
2. `appsettings.{Environment}.json` - 环境特定配置（覆盖基础配置）

### 可用的配置文件
- `appsettings.json` - 默认配置
- `appsettings.Development.json` - 开发环境配置
- `appsettings.Production.json` - 生产环境配置
- `appsettings.Staging.json` - 预发布环境配置

## 🔧 环境变量配置方法

### 方法1：使用脚本设置（推荐）
```bash
# 运行环境变量设置脚本（需要管理员权限）
set_environment.bat
```

### 方法2：手动设置系统环境变量
1. 右键"此电脑" → "属性"
2. 点击"高级系统设置"
3. 点击"环境变量"
4. 在"系统变量"中点击"新建"
5. 变量名：`ASPNETCORE_ENVIRONMENT`
6. 变量值：`Production`（或其他环境名）

### 方法3：命令行设置
```cmd
# 设置系统级环境变量（需要管理员权限）
setx ASPNETCORE_ENVIRONMENT "Production" /M

# 设置用户级环境变量
setx ASPNETCORE_ENVIRONMENT "Production"
```

## 🚀 部署步骤

### 1. 发布应用程序
```bash
publish_service.bat
```

### 2. 设置环境变量
```bash
# 以管理员身份运行
set_environment.bat
# 选择 "2. Production (生产环境)"
```

### 3. 安装服务
```bash
# 以管理员身份运行
install_service.bat
```

### 4. 验证配置
```bash
# 管理服务
manage_service.bat
# 选择 "1. 查看服务状态"
```

## 📝 配置文件差异说明

### appsettings.json（基础配置）
- 包含所有基本配置
- 适用于开发环境

### appsettings.Production.json（生产环境配置）
- 启用事件日志记录
- 优化日志级别
- 生产环境特定设置

## 🔍 验证环境配置

### 检查当前环境
```cmd
# 查看环境变量
echo %ASPNETCORE_ENVIRONMENT%

# 或使用注册表查询
reg query "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v ASPNETCORE_ENVIRONMENT
```

### 查看应用程序日志
1. 打开"事件查看器"
2. 导航到"Windows 日志" → "应用程序"
3. 查找来源为 "WRD_CMSConfigureWeb_Service" 的日志

## ⚠️ 重要注意事项

1. **环境变量更改后需要重启服务**
2. **系统级环境变量需要管理员权限**
3. **服务会自动使用 Production 环境**（已在代码中配置）
4. **配置文件路径必须正确**
5. **数据库连接字符串需要根据实际环境调整**

## 🛠️ 故障排除

### 配置文件未生效
1. 检查环境变量是否正确设置
2. 重启服务
3. 检查配置文件是否存在于正确路径

### 服务启动失败
1. 查看Windows事件日志
2. 检查配置文件语法
3. 验证数据库连接字符串
4. 确保所有依赖文件存在
