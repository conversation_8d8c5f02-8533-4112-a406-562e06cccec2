import{r as D,u as be,Y as pe,j as he,w as fe,f as B,d as L,o as C,i as k,q as me,c as x,b as q,F as Z,e as ee,p as ye,t as te,g as ae,m as f,as as le}from"./index-sMW2Pm6g.js";import{u as ve,C as De,W as Q}from"./table-DF7YFrUK.js";import{O as Ie}from"./index-5GdjZhcp.js";import{W as Ce}from"./index-LWsVge70.js";import{g as d,f as $}from"./tools-DC78Tda0.js";import{u as ge}from"./collectionUnitConfig-gv0q7fV_.js";import{u as Ae}from"./devTree-BcT4pWCP.js";import{_ as Re}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{S as Te}from"./ActionButton-DMeJvyLo.js";import{D as _e,a as Le}from"./index-V7t8PYAK.js";import{B as ke}from"./index-Bi-LLAnN.js";import{M as we}from"./index-sJyM5xm7.js";import"./styleChecker-LI4Lr2UF.js";import"./index-DRy8se-f.js";import"./shallowequal-D09g54zQ.js";import"./index-CkAETRMb.js";import"./index-BLrmE7-A.js";import"./index-ljzR8VKX.js";const i=320,w=(e={size:"large"})=>[{align:"center",title:"通道编号",dataIndex:"channelNumber",isrequired:!0,selectOptions:[],inputType:"select",disabled:e&&e.edit,columnWidth:e&&e.size=="small"?50:160,formItemWidth:i},{title:"测量位置",dataIndex:e&&e.edit?"measLocVibName":"measLocVibID",align:"center",isrequired:!0,selectOptions:[],inputType:"select",formItemWidth:i,columnWidth:e&&e.size=="small"?160:200,disabled:e&&e.edit,...e&&e.isform?{}:{customRender:({record:_})=>_.measLocVibName||""}},{align:"center",title:e&&e.dauType==0?"灵敏度系数(mv/(m/s^2))":"灵敏度系数",dataIndex:"coeff_a",formItemWidth:312,columnWidth:e&&e.size=="small"?50:160,validateRules:d({type:"number",title:"灵敏度系数",required:!0})},{align:"center",title:"最低偏执电压(V)",dataIndex:"minBiasVolt",formItemWidth:i,columnWidth:e&&e.size=="small"?50:160,validateRules:d({type:"positiveNumber",title:"最低偏执电压",required:!0})},{align:"center",title:"最高偏执电压(V)",dataIndex:"maxBiasVolt",formItemWidth:i,columnWidth:e&&e.size=="small"?50:160,validateRules:d({type:"positiveNumber",title:"最高偏执电压",required:!0})}],T=e=>{let n=[{align:"center",title:"采集单元名称",dataIndex:"dauName",isrequired:!0,formItemWidth:i},{align:"center",title:"IP地址",dataIndex:"ip",formItemWidth:i,validateRules:d({type:"ip",title:"IP地址",required:!0})},{align:"center",title:"端口",dataIndex:"port",formItemWidth:i,validateRules:d({type:"port",title:"端口",required:!0})},{align:"center",title:"采集间隔(分钟)",dataIndex:"dataAcquisitionInterval",formItemWidth:i,validateRules:d({type:"integer",title:"采集间隔",required:!0})},{align:"center",title:"状态",dataIndex:"isAvailable",inputType:"checkbox",selectOptions:[{label:"启用",value:!0}],formItemWidth:i}],_=[...w({...e})],z=[{align:"center",title:"通道编号",dataIndex:"channelNumber",isrequired:!0,selectOptions:[],inputType:"select",disabled:e&&e.edit,formItemWidth:i},{title:"测量位置",dataIndex:e&&e.edit?"measLocVibName":"measLoc_ProcessId",align:"center",isrequired:!0,selectOptions:[],inputType:"select",formItemWidth:i,disabled:e&&e.edit,...e&&e.isform?{}:{customRender:({record:t})=>t.measLocVibName||""}},{align:"center",title:"转换系数A",dataIndex:"coeff_a",formItemWidth:i,validateRules:d({type:"number",title:"转换系数A",required:!0})},{align:"center",title:"转换系数B",dataIndex:"coeff_b",formItemWidth:i,validateRules:d({type:"number",title:"转换系数B",required:!0})}],K=[{align:"center",title:"通道编号",dataIndex:"channelNumber",isrequired:!0,selectOptions:[],inputType:"select",formItemWidth:i},{title:"测量位置",dataIndex:e&&e.edit?"measLocRotSpdName":"measLocRotSpdID",align:"center",isrequired:!0,disabled:e&&e.edit,selectOptions:[],inputType:"select",formItemWidth:i,...e&&e.isform?{}:{customRender:({record:t})=>t.measLocRotSpdName||""}}],N=[...w({size:"small",...e}),{align:"center",title:"物理量类型",inputType:"select",selectOptions:[],formItemWidth:i,dataIndex:"physicalQuantityType",isrequired:!0},{align:"center",title:"应变转应力系数A",dataIndex:"s2S_Coeff_a",formItemWidth:i,validateRules:d({type:"number",title:"应变转应力系数A",required:!0})},{align:"center",title:"应变转应力系数B",dataIndex:"s2S_Coeff_b",formItemWidth:i,validateRules:d({type:"number",title:"应变转应力系数B",required:!0})},{align:"center",title:"寄存器地址",dataIndex:"registerAddress",formItemWidth:i,isrequired:!0}];[...w({size:"small",...e}),d({type:"number",title:"应变转应力系数A",required:!0}),d({type:"number",title:"应变转应力系数B",required:!0}),d({type:"number",title:"初始安装间距",required:!0})];let g=[...w({size:"small",...e}),{align:"center",title:"分配器号",dataIndex:"dispatcherID",formItemWidth:i,isdisplay:!(e&&e.isForm),columnHidden:e&&e.isForm,...e&&e.isform?{}:{customRender:({record:t})=>t.ultrasonicChannelConfig?t.ultrasonicChannelConfig.dispatcherID:""}},{align:"center",title:"分配器号通道",dataIndex:"dispatcherChannelID",formItemWidth:i,isdisplay:!(e&&e.isForm),columnHidden:e&&e.isForm,validateRules:d({type:"number",title:"预紧力系数",required:!0}),...e&&e.isform?{}:{customRender:({record:t})=>t.ultrasonicChannelConfig?t.ultrasonicChannelConfig.dispatcherChannelID:""}},{align:"center",title:"测量基准",dataIndex:"standardContent",formItemWidth:i,isdisplay:!(e&&e.isForm),columnHidden:e&&e.isForm,validateRules:d({type:"number",title:"预紧力系数",required:!0}),...e&&e.isform?{}:{customRender:({record:t})=>t.ultrasonicChannelConfig&&t.ultrasonicChannelConfig.standardFilePath?"已上传":"未上传"}},{align:"center",title:"预紧力系数",dataIndex:"preloadCalCoeffs",formItemWidth:i,validateRules:d({type:"number",title:"预紧力系数",required:!0}),...e&&e.isform?{}:{customRender:({record:t})=>t.ultrasonicChannelConfig?t.ultrasonicChannelConfig.preloadCalCoeffs:""}},{align:"center",title:"温度系数",dataIndex:"tempCalibCoeff",formItemWidth:i,validateRules:d({type:"number",title:"温度系数",required:!0}),...e&&e.isform?{}:{customRender:({record:t})=>t.ultrasonicChannelConfig?t.ultrasonicChannelConfig.tempCalibCoeff:""}}],S=[...w({size:"small",...e}),{align:"center",title:"测量基准",dataIndex:"upperLimitFreqency",formItemWidth:i,isdisplay:!(e&&e.isForm),columnHidden:e&&e.isForm,...e&&e.isform?{}:{customRender:({record:t})=>t.ultrasonicChannelConfig&&t.ultrasonicChannelConfig.standardFilePath?"已上传":"未上传"}},{align:"center",title:"预紧力系数",dataIndex:"preloadCalCoeffs",formItemWidth:i,validateRules:d({type:"number",title:"预紧力系数",required:!0}),...e&&e.isform?{}:{customRender:({record:t})=>t.ultrasonicChannelConfig?t.ultrasonicChannelConfig.preloadCalCoeffs:""}},{align:"center",title:"温度系数",dataIndex:"tempCalibCoeff",formItemWidth:i,validateRules:d({type:"number",title:"温度系数",required:!0}),...e&&e.isform?{}:{customRender:({record:t})=>t.ultrasonicChannelConfig?t.ultrasonicChannelConfig.tempCalibCoeff:""}}],y=[...w({size:"small",...e}),{align:"center",title:"物理量类型",inputType:"select",selectOptions:[],dataIndex:"physicalQuantityType",formItemWidth:i,isrequired:!0},{align:"center",title:"应变转应力系数A",dataIndex:"s2S_Coeff_a",formItemWidth:i,validateRules:d({type:"number",title:"应变转应力系数A",required:!0})},{align:"center",title:"应变转应力系数B",dataIndex:"s2S_Coeff_b",formItemWidth:i,validateRules:d({type:"number",title:"应变转应力系数B",required:!0})},{align:"center",title:"初始安装间距(L0)",dataIndex:"coeff_L0",formItemWidth:i,validateRules:d({type:"positiveNumber",title:"初始安装间距",required:!0})}],v=[{align:"center",title:"通道编号",dataIndex:"channelNumber",isrequired:!0,selectOptions:[],inputType:"select",formItemWidth:i},{title:"测量位置",dataIndex:e&&e.edit?"measLocName":"measLoc_ProcessId",align:"center",isrequired:!0,disabled:e&&e.edit,selectOptions:[],inputType:"select",formItemWidth:i,...e&&e.isform?{}:{customRender:({record:t})=>t.measLocName||""}},{align:"center",title:"转换系数A",dataIndex:"powerCoeff_a",formItemWidth:i,validateRules:d({type:"number",title:"转换系数A",required:!0}),...e&&e.isform?{}:{customRender:({record:t})=>t.coeff_a}},{align:"center",title:"转换系数B",dataIndex:"powerCoeff_b",formItemWidth:i,validateRules:d({type:"number",title:"转换系数B",required:!0}),...e&&e.isform?{}:{customRender:({record:t})=>t.coeff_b}}],h=[];if(e&&(e.dauType||e.dauType==0))switch(e.dauType){case 0:h=[...w({...e})];break;case 6:h=_;break;case 1:h=g;break;case 2:h=N;break;case 3:h=y;break;case 7:h=S;break;default:h=_;break}else h=_;return[n,h,z,K,v]},xe={class:"btnGroups"},qe={class:"clearfix"},Ne=["onClick"],Se={class:"border"},Ue={class:"tableItems"},We={class:"tableItems"},Pe={class:"tableItems"},Be={key:2,class:"nodata"},Ke={key:2},Oe={__name:"index",setup(e){const n=ge(),_=Ae(),z=ve(),K=be(),N=D(!1),g=D(""),S=D(!1),y=D(""),v=D({}),h=D([]),t=D({}),R=D(""),O=D(),p=D(K.params.id),H=D([]),J=D({}),a=pe({noCopyUpKeys:["channelNumber","MeasLocVibID","measLoc_ProcessId"],removeDuplicateKeys:["channelNumber","MeasLocVibID","measLoc_ProcessId"],dauList:[],table1Columns:[],table2Columns:T()[2],table3Columns:T()[3],table4Columns:T()[4],table1Data:[],table2Data:[],table3Data:[],table4Data:[],batchApplyKey:"",bathApplyResponse1:[],bathApplyResponse2:[],bathApplyResponse3:[],bathApplyResponse4:[]}),se=he(()=>R.value==="batchAdd"?"1200px":"600px"),ne=async s=>{O.value&&await z.fetchDevTreedDevicelist({windParkID:O.value,useTobath:!0})},ie=()=>{let s=_.findAncestorsWithNodes(p.value);s&&s.length&&s.length>1&&(O.value=s[s.length-2].id)},Y=async s=>{if(N.value=!0,a.dauList=await n.fetchGetDAUList({WindTurbineID:p.value,WindParkId:O.value}),N.value=!1,n.dAUList&&n.dAUList.length){if(s&&s=="edit"&&JSON.stringify(t.value)!=="{}"){let l=n.dAUList.find(r=>r.dauID==t.value.dauID);t.value=l}else t.value=n.dAUList[0],a.table1Columns=T({dauType:t.value.dauType})[1],U(),W(),V(),P();M(t.value)}else t.value={},M({}),a.table1Columns=T()[1],a.table1Data=[],a.table2Data=[],a.table3Data=[],a.table4Data=[]};fe(()=>K.params.id,async s=>{s&&(n.reset(),p.value=s,ie(),await ne(),Y())},{immediate:!0});const M=s=>{J.value=s,H.value=[{label:"采集单元名称",value:s.dauName},{label:"IP地址",value:s.ip},{label:"端口",value:s.port},{label:"采集间隔(分钟)",value:s.dataAcquisitionInterval},{label:"状态",value:s.isAvailable?"开启":"禁用"}]},oe=async()=>{(!n.channelPhysicalQuantityType||n.channelPhysicalQuantityType.length<1)&&await n.fetchGetChannelPhysicalQuantityType()},G=s=>{const{title:l,operateType:r,tableKey:b}=s;h.value=T({isForm:!0,dauType:t.value.dauType})[b];let c=l.substring(0,l.length-2);g.value=r=="add"?"添加"+c:"批量添加"+c,R.value=r,y.value=b,j(b),X()},F=s=>{const{tableKey:l,rowData:r}=s;let b=T({isForm:!0,edit:!0,dauType:t.value.dauType})[l];h.value=[...b],y.value=l,R.value="edit",l=="0"?(g.value="编辑采集单元",v.value={...r,isAvailable:r.isAvailable?[!0]:[!1]}):l=="2"?(g.value="编辑电流电压通道",v.value={...r,measLoc_ProcessId:r.measLocVibID}):l=="4"?(g.value="编辑工况通道",v.value={...r,powerCoeff_a:r.coeff_a,powerCoeff_b:r.coeff_b}):(v.value={...r},j(l),l=="1"?g.value="编辑振动通道":l=="3"&&(g.value="编辑转速通道")),X()},E=async s=>{const{tableKey:l,selectedkeys:r,record:b}=s;y.value=l;let c=[];if(b)l=="1"||l=="2"?c.push({ChannelNumber:b.channelNumber,MeasLocationID:b.measLocVibID}):l=="3"?c.push({ChannelNumber:b.channelNumber,MeasLocationID:b.measLocRotSpdID}):l=="4"&&c.push({ChannelNumber:b.channelNumber,MeasLocationID:b.measLoc_ProcessId});else for(let u=0;u<r.length;u++){let I=r[u].split("&&");c.push({MeasLocationID:I[1],ChannelNumber:I[0]})}let m={sourceData:{WindTurbineID:p.value,DauID:t.value.dauID,Channels:c},targetTurbineIds:a.batchApplyData},o={};l=="1"?o=await n.fetchBatchDeleteVibChannels(m):l=="2"?o=await n.fetchBatchDeleteProcessChannels(m):l=="3"?o=await n.fetchBatchDeleteRotSpeedChannels(m):l=="4"&&(o=await n.fetchBatchDeleteWorkConditionChannels(m)),o&&o.code===1?(l=="1"?(U(),a.bathApplyResponse1=o.batchResults||{}):l=="2"?(W(),a.bathApplyResponse2=o.batchResults||{}):l=="3"?(V(),a.bathApplyResponse3=o.batchResults||{}):l=="4"&&(P(),a.bathApplyResponse4=o.batchResults||{}),f.success("删除成功")):f.error("删除失败:"+o.msg)},j=async s=>{const l=h.value;switch(await n.fetchGetDAUVibChannelNumList({WindTurbineID:p.value,DAUID:t.value.dauID}),l[0].selectOptions=n.channelNumList,s){case"1":await n.fetchGetmeasLocList({WindTurbineID:p.value,DAUID:t.value.dauID}),l[1].selectOptions=n.measLocList,[2,3].indexOf(t.value.dauType)>-1&&oe(),l.length&&l.length>5&&l[5].dataIndex=="physicalQuantityType"&&(l[5].selectOptions=n.channelPhysicalQuantityType),h.value=[...l];break;case"2":await n.fetchGetmeasLocProcessList({WindTurbineID:p.value,DAUID:t.value.dauID}),l[1].selectOptions=n.measLocProcessList,h.value=[...l];break;case"3":await n.fetchGetRotSpdMeasLocList({WindTurbineID:p.value,DAUID:t.value.dauID}),l[1].selectOptions=n.rotSpdMeasLocList,h.value=[...l];break;case"4":await n.fetchGetWordConditionMeasLoc({WindTurbineID:p.value,DAUID:t.value.dauID}),l[1].selectOptions=n.wordConditionMeasLocList,h.value=[...l];break}},ue=async s=>{switch(y.value){case"0":let l={...v.value,...s,isAvailable:s.isAvailable&&s.isAvailable.length?s.isAvailable[0]:!1,windParkID:v.value.windParkID,dataAcquisitionInterval:s.dataAcquisitionInterval?s.dataAcquisitionInterval*1:0};const r=await n.fetchDAUEditDAU(l);r&&r.code===1?(Y("edit"),A(),f.success("提交成功")):f.error("提交失败:"+r.msg);break;case"1":case"2":let b={...v.value,...s,DauID:t.value.dauID,WindTurbineID:p.value},c={};y.value=="1"?c=await n.fetchBatchEditVibChannels({sourceData:b,targetTurbineIds:a.batchApplyData}):y.value=="2"&&(c=await n.fetchBatchEditProcessChannel({sourceData:b,targetTurbineIds:a.batchApplyData})),c&&c.code===1?(y.value=="1"?(U(),a.bathApplyResponse1=c.batchResults||{}):y.value=="2"&&(W(),a.bathApplyResponse2=c.batchResults||{}),A(),f.success("提交成功")):f.error("提交失败:"+c.msg);break;case"3":let m={channels:[s],WindTurbineID:p.value,DAUID:t.value.dauID},o={};R.value=="add"?o=await n.fetchAddRotSpeedChannels({sourceData:m,targetTurbineIds:a.batchApplyData}):o=await n.fetchEditRotSpeedChannel({sourceData:m,targetTurbineIds:a.batchApplyData}),o&&o.code===1?(V(),a.bathApplyResponse3=o.batchResults||{},A(),f.success("提交成功")):f.error("提交失败:"+o.msg);break;case"4":let u={...v.value,...s,WindTurbineID:p.value,DAUID:t.value.dauID};const I=await n.fetchEditWorkConditionChannel({sourceData:u,targetTurbineIds:a.batchApplyData});I&&I.code===1?(P(),a.bathApplyResponse4=I.batchResults||{},A(),f.success("提交成功")):f.error("提交失败:"+I.msg);break}},re=async s=>{switch(y.value){case"1":let l=$(s);if(l&&l.length){let c=l.map((u,I)=>({...u})),m={WindTurbineID:p.value,DAUID:t.value.dauID,Channels:c},o=await n.fetchBatchAddVibChannels({sourceData:m,targetTurbineIds:a.batchApplyData});o&&o.code===1?(a.bathApplyResponse1=o.batchResults||{},U(),f.success("提交成功"),A()):f.error("提交失败:"+o.msg)}break;case"2":let r=$(s);if(r&&r.length){let c=r.map((u,I)=>({...u})),m={WindTurbineID:p.value,DAUID:t.value.dauID,Channels:c},o=await n.fetchBatchAddProcessChannels({sourceData:m,targetTurbineIds:a.batchApplyData});o&&o.code===1?(a.bathApplyResponse2=o.batchResults||{},W(),f.success("提交成功"),A()):f.error("提交失败:"+o.msg)}break;case"4":let b=$(s);if(b&&b.length){let c=b.map((u,I)=>({...u})),m={WindTurbineID:p.value,DAUID:t.value.dauID,Channels:c},o=await n.fetchAddWorkConditionChannels({sourceData:m,targetTurbineIds:a.batchApplyData});o&&o.code===1?(a.bathApplyResponse4=o.batchResults||{},P(),f.success("提交成功"),A()):f.error("提交失败:"+o.msg)}break}},X=()=>{S.value=!0},A=()=>{S.value=!1,v.value={},y.value="",g.value="",R.value="",h.value=[]},ce=s=>{s.dauID!=t.value.dauID&&(t.value=s,a.table1Columns=T({dauType:s.dauType})[1],M(s),U(),W(),V(),P())},U=async()=>{a.table1Data=await n.fetchDAUGetDAUVibList({WindTurbineID:p.value,DAUID:t.value.dauID})},W=async()=>{a.table2Data=await n.fetchGetDAUProcessList({WindTurbineID:p.value,DAUID:t.value.dauID})},V=async()=>{a.table3Data=await n.fetchGetRotSpeedList({WindTurbineID:p.value,DAUID:t.value.dauID})},P=async()=>{a.table4Data=await n.fetchGetworkConditionList({WindTurbineID:p.value,DAUID:t.value.dauID})},de=async s=>{s.type&&s.type=="close"?(a.batchApplyData=[],a.batchApplyKey="",a[`bathApplyResponse${s.key}`]={}):(a.batchApplyData=s.turbines,a.batchApplyKey=s.key)};return le("deviceId",p),le("bathApplySubmit",de),(s,l)=>{const r=ke,b=Le,c=_e,m=we,o=Te;return C(),B(o,{spinning:N.value,size:"large"},{default:L(()=>[l[2]||(l[2]=k("h1",null," 采集单元 ",-1)),k("div",xe,[k("ul",qe,[(C(!0),x(Z,null,ee(a.dauList,u=>(C(),x("li",{key:u.dauID,class:ye({active:t.value.dauID===u.dauID}),onClick:I=>ce(u)},te(u.dauName),11,Ne))),128))])]),t.value&&t.value.dauID?(C(),B(De,{key:0,tableTitle:"采集单元信息",defaultCollapse:!0,batchApply:!1},{rightButtons:L(()=>[q(r,{type:"primary",onClick:l[0]||(l[0]=u=>F({tableKey:"0",rowData:J.value,title:"测量定义"}))},{default:L(()=>l[1]||(l[1]=[ae(" 编辑 ",-1)])),_:1,__:[1]})]),content:L(()=>[k("div",Se,[q(c,{column:5,size:"small"},{default:L(()=>[(C(!0),x(Z,null,ee(H.value,u=>(C(),B(b,{label:u.label,key:u.label},{default:L(()=>[ae(te(u.value),1)]),_:2},1032,["label"]))),128))]),_:1})])]),_:1})):me("",!0),t.value&&t.value.dauID?(C(),x("div",{class:"blockBorder",key:p.value},[k("div",Ue,[q(Q,{"table-key":"1","table-title":"振动通道列表","table-columns":a.table1Columns,borderLight:a.batchApplyKey=="1",bathApplyResponse:a.bathApplyResponse1,"table-operate":["edit","delete","batchAdd","batchDelete"],noPagination:!0,recordKey:u=>`${u.channelNumber}&&${u.measLocVibID}`,onAddRow:G,"table-datas":a.table1Data,onDeleteRow:E,onEditRow:F},null,8,["table-columns","borderLight","bathApplyResponse","recordKey","table-datas"])]),k("div",We,[q(Q,{"table-key":"2","table-title":"电流电压通道列表","table-columns":a.table2Columns,borderLight:a.batchApplyKey=="2",bathApplyResponse:a.bathApplyResponse2,"table-operate":["edit","delete","batchAdd","batchDelete"],noPagination:!0,recordKey:u=>`${u.channelNumber}&&${u.measLocVibID}`,"table-datas":a.table2Data,onAddRow:G,onDeleteRow:E,onEditRow:F},null,8,["table-columns","borderLight","bathApplyResponse","recordKey","table-datas"])]),k("div",Pe,[q(Q,{"table-key":"3","table-title":"转速通道列表","table-columns":a.table3Columns,noPagination:!0,borderLight:a.batchApplyKey=="3",bathApplyResponse:a.bathApplyResponse3,"table-operate":["delete",a.table3Data&&a.table3Data.length?"edit":"add","batchDelete"],recordKey:u=>`${u.channelNumber}&&${u.measLocRotSpdID}`,"table-datas":a.table3Data,onAddRow:G,onDeleteRow:E,onEditRow:F},null,8,["table-columns","borderLight","bathApplyResponse","table-operate","recordKey","table-datas"])])])):(C(),x("div",Be," 请先添加采集单元!")),q(m,{maskClosable:!1,width:se.value,open:S.value,title:g.value,footer:"",destroyOnClose:!0,onCancel:A},{default:L(()=>[R.value==="add"||R.value==="edit"||R.value==="editDevice"?(C(),B(Ie,{key:0,ref:"formModalRef",titleCol:h.value,initFormData:v.value,onSubmit:ue,onCancelForm:A},null,8,["titleCol","initFormData"])):R.value==="batchAdd"?(C(),B(Ce,{key:1,ref:"modalTableFormRef",size:"default","table-key":"0","table-columns":h.value,"table-operate":["copyUp","delete"],"table-datas":[],"noCopyUp-keys":a.noCopyUpKeys,onSubmit:re,removeDuplicateKeys:a.removeDuplicateKeys,"noRepeat-Keys":a.removeDuplicateKeys,onCancel:A},null,8,["table-columns","noCopyUp-keys","removeDuplicateKeys","noRepeat-Keys"])):(C(),x("div",Ke))]),_:1},8,["width","open","title"])]),_:1,__:[2]},8,["spinning"])}}},st=Re(Oe,[["__scopeId","data-v-820faeb0"]]);export{st as default};
