import{u as le,W as G}from"./table-DF7YFrUK.js";import{O as ie}from"./index-5GdjZhcp.js";import{W as ne}from"./index-LWsVge70.js";import{g as A,f as ue}from"./tools-DC78Tda0.js";import{r as b,j as de,u as ce,Y as re,w as be,f as F,d as U,o as T,c as $,b as O,m as d,as as j}from"./index-sMW2Pm6g.js";import{u as pe}from"./configModbus-pT2N9qql.js";import{u as me}from"./collectionUnitConfig-gv0q7fV_.js";import{u as he}from"./devTree-BcT4pWCP.js";import{S as De}from"./ActionButton-DMeJvyLo.js";import{M as ve}from"./index-sJyM5xm7.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./styleChecker-LI4Lr2UF.js";import"./index-Bi-LLAnN.js";import"./index-DRy8se-f.js";import"./shallowequal-D09g54zQ.js";import"./index-CkAETRMb.js";import"./index-BLrmE7-A.js";import"./index-ljzR8VKX.js";const ye={key:2},Ke={__name:"modbus",setup(fe){const l=pe(),L=me(),z=he(),V=le();let u=320,M=[{label:"串口服务器",value:"0",text:"串口服务器"},{label:"串口直连",value:"1",text:"串口直连"}];const _=(e={isForm:!1})=>[{title:"ModbusID",dataIndex:"modbusID",columnWidth:100,formItemWidth:u,validateRules:A({type:"number",title:"设备ID",required:!0})},{title:"Modbus名称",dataIndex:"modbusDeviceName",isrequired:!0,formItemWidth:u},{title:"Modbus类型",dataIndex:"modbusType",columnWidth:150,formItemWidth:u,isrequired:!0,inputType:"select",selectOptions:[],...e&&e.isForm?{}:{customRender:({text:a,record:t})=>{const s=l.modbusDevTypes.find(i=>i.value==t.modbusType);return s?s.label:a}}},{title:"采集单元",dataIndex:"dauID",isrequired:!0,formItemWidth:u,inputType:"select",selectOptions:[],...e&&e.isForm?{}:{customRender:({text:a,record:t})=>{const s=L.dAUOptionList.find(i=>i.value==t.dauID);return s?s.label:a}}},{title:"连接方式",dataIndex:"comType",isrequired:!0,formItemWidth:u,inputType:"radio",hasChangeEvent:!0,selectOptions:M,headerOperations:{filters:M},...e&&e.isForm?{}:{customRender:({text:a,record:t})=>{const s=M.find(i=>i.value==t.comType);return s?s.label:a}}}],S=(e={isForm:!1})=>[{title:"设备名称",dataIndex:"modbusDeviceID",columnWidth:400,formItemWidth:u,isrequired:!0,inputType:"select",selectOptions:[],hasChangeEvent:!0,...e&&e.isForm?{}:{customRender:({text:a,record:t})=>t.modbusDeviceName||""}},{title:"通道编号",dataIndex:"channelNumber",formItemWidth:u,validateRules:A({type:"number",title:"通道编号",required:!0})},{title:"测量位置",dataIndex:"measLocationID",columnWidth:400,formItemWidth:u,isrequired:!0,inputType:"select",selectOptions:[],tableList:[],...e&&e.isForm?{}:{customRender:({text:a,record:t})=>t.measLocationName}}],p=()=>[{title:"串口服务器IP",dataIndex:"comIP",formItemWidth:u,validateRules:A({type:"ip",title:"串口服务器IP",required:!0})},{title:"串口服务器端口",dataIndex:"comPort",formItemWidth:u,isrequired:!0,columnWidth:100,validateRules:A({type:"port",title:"串口服务器端口"})},{title:"串口名称",dataIndex:"portName",isrequired:!0},{title:"波特率",dataIndex:"baudRate",columnWidth:100,formItemWidth:u,validateRules:A({type:"number",title:"波特率",required:!0}),inputType:"selectinput",selectOptions:[{label:"9600",value:9600},{label:"19200",value:19200},{label:"38400",value:38400},{label:"57600",value:57600},{label:"115200",value:115200}]},{title:"数据位",dataIndex:"dataBit",isrequired:!0,formItemWidth:u,columnWidth:100},{title:"校验位",dataIndex:"parity",isrequired:!0,formItemWidth:u,columnWidth:100,inputType:"radio",selectOptions:[{label:"无",value:0},{label:"奇",value:1},{label:"偶",value:2}]},{title:"停止位",dataIndex:"stopBit",isrequired:!0,formItemWidth:u,columnWidth:100}],W=b({}),I=b(!1),y=b(""),c=b(""),H=de(()=>c.value==="batchAdd"?"1200px":"600px"),D=b(""),C=b(!1),q=ce(),g=b({}),r=b([]),n=b(q.params.id),R=b(),o=re({tableColumns:[..._(),{title:"串口服务器IP",dataIndex:"comIP"},{title:"串口服务器端口",dataIndex:"comPort"},{title:"串口名称",dataIndex:"portName"},{title:"波特率",dataIndex:"baudRate"}],tableColumns2:S(),tableData1:[],tableData2:[],batchApplyData:[],batchApplyKey:"",bathApplyResponse1:{},bathApplyResponse2:{}}),Y=async e=>{R.value&&await V.fetchDevTreedDevicelist({windParkID:R.value,useTobath:!0})},J=()=>{let e=z.findAncestorsWithNodes(n.value);e&&e.length&&e.length>1&&(R.value=e[e.length-2].id)},Q=async()=>{await l.fetchGetModbusDevType()},X=async()=>{await L.fetchGetDAUList({WindTurbineID:n.value,WindParkId:R.value})},Z=async e=>{n.value&&await l.fetchGetModbusMeasLocByDeviceID({turbineID:n.value,modbusDeviceID:e})},w=async()=>{I.value=!0;let e=await l.fetchGetModbusDeviceList({turbineID:n.value});e&&(I.value=!1,o.tableData1=e)},x=async()=>{I.value=!0;let e=await l.fetchGetModbusChannelList({turbineID:n.value});e&&(I.value=!1,o.tableData2=e)};be(()=>q.params.id,async e=>{n.value=e,e&&(J(),await Y(),Q(),X(),w(),x())},{immediate:!0});const P=e=>{const{title:a,operateType:t,tableKey:s}=e;switch(c.value=t,D.value=s,s){case"1":y.value="添加Modbus设备";break;case"2":y.value="批量添加Modbus通道";break}K(),E()},K=()=>{switch(D.value){case"1":let e=[..._({isForm:!0}),p()[0],p()[1]];e[2].selectOptions=l.modbusDevTypes,e[3].selectOptions=[{label:"无",value:-1},...L.dAUOptionList],c.value==="edit"&&(e[3].disabled=!0),g.value={comType:"0",dataBit:8,parity:0,stopBit:1},r.value=e;break;case"2":let a=[...S({isForm:!0})];a[0].selectOptions=l.modbusDeviceOptions,r.value=a;break}},N=e=>{const{tableKey:a,rowData:t,operateType:s}=e;switch(c.value=s,D.value=a,K(),a){case"1":y.value="编辑Modbus设备",k({value:t.comType,dataIndex:"comType"});break;case"2":y.value="编辑Modbus通道";break}g.value={...t,dauID:t.dauID&&t.dauID!==""?t.dauID:-1},E()},ee=async e=>{switch(c.value){case"add":await ae(e);break;case"edit":await se(e);break}},te=async e=>{let a={windTurbineID:n.value},t=ue(e,a);const s=await l.fetchAddModbusChannel({sourceData:t,targetTurbineIds:o.batchApplyData});s&&s.code==1?(d.success("提交成功"),x(),o.bathApplyResponse2=s.batchResults||{},m()):d.error("提交失败:"+s.msg)},ae=async e=>{if(D.value==="1"){let a={...e,turbineID:n.value,dauID:e.dauID&&e.dauID!==-1?e.dauID:""};const t=await l.fetchAddModbusDevice({sourceData:a,targetTurbineIds:o.batchApplyData});t&&t.code==1?(w(),o.bathApplyResponse1=t.batchResults||{},m(),d.success("提交成功")):d.error("提交失败:"+t.msg)}},se=async e=>{if(D.value==="1"){let a={...g.value,...e,modbusID:e.modbusID,dauID:e.dauID&&e.dauID!==-1?e.dauID:""};const t=await l.fetchEditModbusDevice({sourceData:a,targetTurbineIds:o.batchApplyData});t&&t.code===1?(w(),o.bathApplyResponse1=t.batchResults||{},m(),d.success("提交成功")):d.error("提交失败:"+t.msg)}else if(D.value==="2"){let a={windTurbineID:n.value,description:"",...e};const t=await l.fetchEditModbusChannel({sourceData:a,targetTurbineIds:o.batchApplyData});t&&t.code===1?(x(),o.bathApplyResponse2=t.batchResults||{},m(),d.success("提交成功")):d.error("提交失败:"+t.msg)}},B=async e=>{const{tableKey:a,selectedkeys:t,record:s}=e;if(D.value=a,a==="1"){let i=[];if(s)i.push({modbusDeviceID:s.modbusDeviceID,turbineID:n.value,modbusID:s.modbusID});else for(let v=0;v<t.length;v++){let f=t[v].split("&&");i.push({turbineID:n.value,modbusDeviceID:f[0],modbusID:f[1]})}const h=await l.fetchBatchDeleteModbusDevice({sourceData:i,targetTurbineIds:o.batchApplyData});h&&h.code==1?(w(),o.bathApplyResponse1=h.batchResults||{},m(),d.success("删除成功")):d.error("删除失败:"+h.msg)}else if(a==="2"){let i=[];if(s)i.push({windTurbineID:n.value,modbusDeviceID:s.modbusDeviceID,channelNumber:s.channelNumber,measLocationID:s.measLocationID});else for(let v=0;v<t.length;v++){let f=t[v].split("&&");i.push({windTurbineID:n.value,modbusDeviceID:f[1],channelNumber:f[0],measLocationID:f[2]})}const h=await l.fetchBatchDeleteModbusChannel({sourceData:i,targetTurbineIds:o.batchApplyData});h&&h.code==1?(x(),o.bathApplyResponse2=h.batchResults||{},d.success("删除成功"),m()):d.error("删除失败:"+h.msg)}},k=async e=>{if(e.dataIndex&&e.value&&e.dataIndex==="comType"&&(e.value=="0"?r.value=[...r.value.slice(0,5),p()[0],p()[1]]:e.value=="1"&&(r.value=[...r.value.slice(0,5),p()[2],p()[3],p()[4],p()[5],p()[6]])),e.dataIndex&&e.value&&e.dataIndex==="modbusDeviceID"){console.log(e);let a=r.value;if(await Z(e.value),c.value=="batchAdd"){if(e.index>=a[2].tableList.length)for(let t=a[2].tableList.length;t<=e.index;t++)a[2].tableList.push({});a[2].tableList[e.index].selectOptions=l.modbusMeasLocoptions,W.value.setTableFieldValue({formDataIndex:`measLocationID[${e.index}]`,tableDataIndex:"measLocationID",index:e.index,value:l.modbusMeasLocoptions&&l.modbusMeasLocoptions.length?l.modbusMeasLocoptions[0].value:""})}else a[2].selectOptions=l.modbusMeasLocoptions,formRef.value.setFieldValue("measLocationID",l.modbusMeasLocoptions&&l.modbusMeasLocoptions.length?l.modbusMeasLocoptions[0].value:"")}},E=()=>{C.value=!0},m=e=>{C.value=!1,r.value=[],g.value={},c.value="",y.value="",D.value=""},oe=async e=>{e.type&&e.type=="close"?(o.batchApplyData=[],o.batchApplyKey="",o[`bathApplyResponse${e.key}`]={}):(o.batchApplyData=e.turbines,o.batchApplyKey=e.key)};return j("deviceId",n),j("bathApplySubmit",oe),(e,a)=>{const t=ve,s=De;return T(),F(s,{spinning:I.value,size:"large"},{default:U(()=>[(T(),$("div",{key:n.value},[O(G,{ref:"table",size:"default","table-key":"1","table-title":"Modbus设备列表","table-columns":o.tableColumns,borderLight:o.batchApplyKey=="1",bathApplyResponse:o.bathApplyResponse1,"table-operate":["edit","delete","add","batchDelete"],noPagination:!0,recordKey:i=>`${i.modbusDeviceID}&&${i.modbusID}`,"table-datas":o.tableData1,onAddRow:P,onDeleteRow:B,onEditRow:N},null,8,["table-columns","borderLight","bathApplyResponse","recordKey","table-datas"]),O(G,{ref:"table",size:"default","table-key":"2","table-title":"通道列表","table-columns":o.tableColumns2,borderLight:o.batchApplyKey=="2",bathApplyResponse:o.bathApplyResponse2,"table-operate":["delete","add","batchDelete","batchAdd"],noPagination:!0,recordKey:i=>`${i.channelNumber}&&${i.modbusDeviceID}&&${i.measLocationID}`,"table-datas":o.tableData2,onAddRow:P,onDeleteRow:B,onEditRow:N},null,8,["table-columns","borderLight","bathApplyResponse","recordKey","table-datas"])])),O(t,{maskClosable:!1,width:H.value,open:C.value,title:y.value,footer:"",destroyOnClose:!0,onCancel:m},{default:U(()=>[c.value==="add"||c.value==="edit"?(T(),F(ie,{key:0,titleCol:r.value,onCancelForm:m,initFormData:g.value,onChange:k,onSubmit:ee},null,8,["titleCol","initFormData"])):c.value==="batchAdd"?(T(),F(ne,{key:1,ref_key:"tableFormRef",ref:W,size:"default","table-key":"0","table-columns":r.value,"table-operate":["copyUp","delete"],"table-datas":[],onSubmit:te,onHangeTableFormChange:k,onCancel:m},null,8,["table-columns"])):(T(),$("div",ye))]),_:1},8,["width","open","title"])]),_:1},8,["spinning"])}}};export{Ke as default};
