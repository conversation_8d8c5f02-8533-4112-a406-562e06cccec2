import{u as ae,W as ne}from"./table-DF7YFrUK.js";import{O as oe}from"./index-5GdjZhcp.js";import{r as i,u as re,j as q,h as le,w as se,x as ue,f as N,d as c,o as y,i as U,b as f,c as V,q as ie,F as de,g as b,p as z,t as J,m as h}from"./index-sMW2Pm6g.js";import{u as ce}from"./jfDeviceManage-CqSCSr_n.js";import{g as pe}from"./tools-DC78Tda0.js";import{s as C,_ as fe,p as ve,o as me}from"./useWebSocket-nvkDFdXf.js";import{u as ge}from"./devTree-BcT4pWCP.js";import{_ as Ie}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{S as be}from"./ActionButton-DMeJvyLo.js";import{B as he}from"./index-Bi-LLAnN.js";import{M as Ce}from"./index-sJyM5xm7.js";import"./styleChecker-LI4Lr2UF.js";import"./index-DRy8se-f.js";import"./shallowequal-D09g54zQ.js";import"./index-CkAETRMb.js";import"./index-BLrmE7-A.js";import"./index-ljzR8VKX.js";const v=320,De={__name:"device",setup(_e){const p=ce(),j=ae(),E=ge(),T=i(!1),M=re(),D=i(""),I=i(""),P=i({}),s=i([]);i([]);const _=i([]),S=i(M.params.id),d=i(!1),w=i([]),$=i(""),k=i(!1),W=i([]),G=q(()=>I.value==="useMeasurementDefinition"?"1200px":"600px"),H=q(()=>I.value==="otherConfig"?"part":"horizontal"),m=(t,a)=>[{title:"机组名称",dataIndex:"windTurbineName",labelInValue:!a,columnWidth:100,inputType:"select",selectOptions:j.deviceOptions,disabled:a,isrequired:!0,formItemWidth:v},{title:"采集器类型",dataIndex:"dauType",hasChangeEvent:!0,columnWidth:140,inputType:"select",isrequired:!0,selectOptions:p.dAUTypeList,isdisplay:!a,formItemWidth:v,headerOperations:{filters:[]}},{title:"DAU IP地址",dataIndex:"ip",columnWidth:150,formItemWidth:v,columnOperate:{type:"ip"},validateRules:pe({type:"ip",title:"IP地址",required:!0})},{title:"服务器地址",dataIndex:"port",columnWidth:70,formItemWidth:v},{title:"在离线状态",dataIndex:"dauOnOffStatus",otherColumn:!0,columnWidth:120,formItemWidth:v,headerOperations:{filters:[{text:"在线",value:2},{text:"离线",value:1}]}}],g=i(m()),O=async()=>{d.value=!0;let t=await p.fetchGetDAUList({windTurbineID:S.value,WindParkId:$.value});s.value=t,d.value=!1,g.value=m()},F=()=>{let t=E.findAncestorsWithNodes(S.value);t&&t.length&&t.length>1&&($.value=t[t.length-2].id)};le(()=>{F(),O()}),se(()=>M.params.id,t=>{p.reset(),S.value=t,F(),O()});const R=async(t,a)=>{d.value=!0;let o=s.value.filter(e=>t.includes(`${e.windTurbineID}&&${e.dauID}`)),n=await p.fetchStartAcquisition(o);d.value=!1,n&&n.code===1?(g.value=[...m(),{title:"采集状态",dataIndex:"operateStatus",otherColumn:!0,columnWidth:120,formItemWidth:v}],s.value=s.value.map(e=>t.includes(`${e.windTurbineID}&&${e.dauID}`)?{...e,operateStatus:n.data[e.ip]}:e)):h.error("操作失败")},Q=async(t,a)=>{d.value=!0;let o=s.value.filter(e=>t.includes(`${e.windTurbineID}&&${e.dauID}`)),n=await p.fetchStopAcquisition(o);d.value=!1,n&&n.code===1?(g.value=[...m(),{title:"停止采集",dataIndex:"operateStatus",otherColumn:!0,columnWidth:120,formItemWidth:v}],s.value=s.value.map(e=>t.includes(`${e.windTurbineID}&&${e.dauID}`)?{...e,operateStatus:n.data[e.ip]}:e)):h.error("操作失败")},X=async(t,a)=>{d.value=!0;let o=s.value.filter(e=>t.includes(`${e.windTurbineID}&&${e.dauID}`)),n=await p.fetchSetMeasureDefinition(o);d.value=!1,n&&n.code===1?(g.value=[...m(),{title:"测量定义下发",dataIndex:"operateStatus",otherColumn:!0,columnWidth:120,formItemWidth:v}],s.value=s.value.map(e=>t.includes(`${e.windTurbineID}&&${e.dauID}`)?{...e,operateStatus:n.data[e.ip]}:e)):h.error("操作失败")},Y=async(t,a)=>{I.value=a,D.value="推送配置",w.value=t,g.value=[...m()],_.value=ve(),L()},Z=async(t,a)=>{I.value=a,D.value="高级参数配置",w.value=t,_.value=me(),g.value=[...m()],L()},L=()=>{T.value=!0},A=t=>{T.value=!1,_.value=[],P.value={},I.value="",D.value=""},K=async t=>{d.value=!0;let o=s.value.filter(e=>w.value.includes(`${e.windTurbineID}&&${e.dauID}`)).map(e=>({...e,...t})),n=null;I.value==="pushConfig"?n=await p.fetchJfSetSFTPConfig(o):I.value==="otherConfig"&&(n=await p.fetchJfSetAdvancedParameters(o)),d.value=!1,n?(h.success("操作成功"),A()):h.error("操作失败")},ee=async t=>{const a=await C.startConnection("/Hubs/ServerPerformanceHub");k.value=a;let o=[];if(a){C.onReceiveMessage("ProgressMonitoringStarted",e=>{console.log("进度启动结果",e),s.value=t.map(u=>{if(e&&e.progressList&&e.progressList.length){let r=e.progressList.find(l=>l.dauID===u.dauID&&l.windTurbineID===u.windTurbineID);return{...u,process:r.progressPercentage}}return u})}),C.onReceiveMessage("ProgressUpdate",e=>{console.log("进度更新",e),s.value=t.map(u=>{if(e&&e.length){let r=e.find(l=>l.dauID===u.dauID&&l.windTurbineID===u.windTurbineID);return(r.progressPercentage==100||r.progressPercentage>100)&&!o.find(l=>l.dauID===u.dauID&&l.windTurbineID===u.windTurbineID)&&(o.push(u),o.length===t.length&&(h.success("获取录波数据成功！"),B())),{...u,process:Math.trunc(r.progressPercentage)}}return u})}),C.onReceiveMessage("CurrentProgressUpdate",e=>{console.log("当前进度响应",e)});const n={parkID:$.value,daus:t};C.sendMessage("StartProgressMonitoring",n)}else h.error("连接失败")},te=async(t,a)=>{let o=s.value.filter(n=>t.includes(`${n.windTurbineID}&&${n.dauID}`));await p.fetchGetWaveFormData(o),g.value=[...m(),{title:"录波数据进度",dataIndex:"process",otherColumn:!0,columnWidth:120,formItemWidth:v}],ee(o),s.value=s.value.map(n=>t.includes(`${n.windTurbineID}&&${n.dauID}`)?{...n,process:0}:n),W.value=t},B=async()=>{k.value&&(await C.stopConnection(),k.value=!1)};return ue(()=>{B()}),(t,a)=>{const o=he,n=fe,e=Ce,u=be;return y(),N(u,{spinning:d.value,size:"large"},{default:c(()=>[U("div",null,[f(ne,{ref:"table",size:"default","table-key":"0","table-title":"设备列表","table-columns":g.value,recordKey:r=>`${r.windTurbineID}&&${r.dauID}`,selectedkeys:W.value,"table-datas":s.value,noBatchApply:!0,selectedRows:!0},{rightButtons:c(({selectedRowKeys:r})=>[f(o,{type:"primary",onClick:l=>R(r,"startCollection"),disabled:!r.length},{default:c(()=>a[0]||(a[0]=[b(" 启动采集 ",-1)])),_:2,__:[0]},1032,["onClick","disabled"]),f(o,{onClick:l=>Q(r,"stopCollection"),disabled:!r.length},{default:c(()=>a[1]||(a[1]=[b(" 停止采集 ",-1)])),_:2,__:[1]},1032,["onClick","disabled"]),f(o,{type:"primary",onClick:l=>X(r,"useMeasurementDefinition"),disabled:!r.length},{default:c(()=>a[2]||(a[2]=[b(" 测量定义下发 ",-1)])),_:2,__:[2]},1032,["onClick","disabled"]),f(o,{type:"primary",onClick:l=>Y(r,"pushConfig"),disabled:!r.length},{default:c(()=>a[3]||(a[3]=[b(" 推送配置 ",-1)])),_:2,__:[3]},1032,["onClick","disabled"]),f(o,{type:"primary",onClick:l=>te(r,"getObtainWaveform"),disabled:!r.length},{default:c(()=>a[4]||(a[4]=[b(" 获取录波数据 ",-1)])),_:2,__:[4]},1032,["onClick","disabled"]),f(o,{type:"primary",onClick:l=>Z(r,"otherConfig"),disabled:!r.length},{default:c(()=>a[5]||(a[5]=[b(" 高级参数配置 ",-1)])),_:2,__:[5]},1032,["onClick","disabled"])]),otherColumn:c(({record:r,text:l,column:x})=>[x.dataIndex==="dauOnOffStatus"?(y(),V(de,{key:0},[U("span",{class:z([l==2?"green":"gray","circle"])},null,2),b(" "+J(l==1?"离线":"在线"),1)],64)):x.dataIndex==="process"&&l!==""&&typeof l=="number"?(y(),N(n,{key:1,type:"circle",size:30,"stroke-color":{"0%":"#108ee9","100%":"#87d068"},percent:l},null,8,["percent"])):x.dataIndex==="operateStatus"?(y(),V("span",{key:2,class:z([l?"greenText":"redtext"])},J(l?"成功":"失败"),3)):ie("",!0)]),_:1},8,["table-columns","recordKey","selectedkeys","table-datas"]),f(e,{maskClosable:!1,width:G.value,open:T.value,title:D.value,footer:"",destroyOnClose:!0,onCancel:A},{default:c(()=>[f(oe,{titleCol:_.value,initFormData:P.value,formlayout:H.value,onSubmit:K},null,8,["titleCol","initFormData","formlayout"])]),_:1},8,["width","open","title"])])]),_:1},8,["spinning"])}}},Ue=Ie(De,[["__scopeId","data-v-0ebfa6ea"]]);export{Ue as default};
