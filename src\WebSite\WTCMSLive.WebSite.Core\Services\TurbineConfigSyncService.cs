using CMSFramework.BusinessEntity;
using Microsoft.Extensions.Logging;
using WTCMSLive.BusinessModel;
using WTCMSLive.WebSite.Models;
using WTCMSLive.WebSite.Core.Models;
using System.Linq;

namespace WTCMSLive.WebSite.Core.Services
{
    /// <summary>
    /// 机组配置同步服务 - 处理版本检查和数据同步
    /// </summary>
    public class TurbineConfigSyncService
    {
        private readonly ILogger<TurbineConfigSyncService> _logger;

        public TurbineConfigSyncService(ILogger<TurbineConfigSyncService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 同步单个机组的配置
        /// </summary>
        /// <param name="turbineId">机组ID</param>
        /// <param name="configDbBytes">配置数据库字节数组</param>
        /// <returns>同步是否成功</returns>
        public async Task<bool> SyncTurbineConfig(byte[] configDbBytes)
        {
            try
            {
                // 将字节数组写入临时文件
                var tempDbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Output", $"wtlivedb_sync_{Guid.NewGuid()}.db");
                
                try
                {
                    await File.WriteAllBytesAsync(tempDbPath, configDbBytes);

                    // 文件解压缩,解压到同路径
                    var unzipPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Output", Guid.NewGuid().ToString());
                    CommonUtility.Unzip(tempDbPath, unzipPath);

                    string dbfilePath = Path.Combine(unzipPath,"wtlivedb.db");
                    var subDbConnectionString = $"Data Source={dbfilePath};";

                    // 执行版本检查和同步
                    return await SyncConfigWithVersionCheck(subDbConnectionString);
                }
                finally
                {
                    // 清理临时文件
                    if (File.Exists(tempDbPath))
                    {
                        try
                        {
                            File.Delete(tempDbPath);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "删除临时文件失败: {TempPath}", tempDbPath);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "同步机组配置失败");
                return false;
            }
        }

        /// <summary>
        /// 从子库同步配置到主库
        /// </summary>
        /// <param name="turbineId">机组ID</param>
        /// <param name="subDbConnectionString">子库连接字符串</param>
        /// <returns>是否成功同步</returns>
        public async Task<bool> SyncConfigWithVersionCheck(string subDbConnectionString)
        {
            try
            {
                WindTurbine? trubine = GetSubDatabaseWindturbineID(subDbConnectionString);
                if (trubine == null)
                {
                    _logger.LogInformation("接收到数据库无机组信息");
                    return false;
                }

                var turbineId = trubine.WindTurbineID;

                _logger.LogInformation("开始同步机组 {TurbineId} 的配置，进行版本检查", turbineId);

                // 1. 从主库获取当前版本，由dau version 修改为 turbien config version
                //int? mainDbVersion = GetMainDatabaseVersion(turbineId);
                int? subDbVersion = int.Parse(trubine.ConfigVersion);
                _logger.LogDebug("子库版本: {MainVersion}", subDbVersion);

                // 如果主库中没有相应的机组记录，则直接添加
                // 如果存在机组记录，则比较版本号
                if(subDbVersion != null)
                {
                    // 2. 从主库获取版本
                    var mainTurbine = DevTreeManagement.GetWindTurbine(turbineId);
                    if (!int.TryParse(mainTurbine?.ConfigVersion, out int mainDbVersion))
                    {
                        mainDbVersion = -1;
                    }
                    _logger.LogDebug("子库版本: {SubVersion}", subDbVersion);

                    // 3. 版本比较
                    if (subDbVersion <= mainDbVersion)
                    {
                        _logger.LogInformation("子库版本 ({SubVersion}) 不大于主库版本 ({MainVersion})，跳过同步",
                            subDbVersion, mainDbVersion);
                        return false;
                    }

                    _logger.LogInformation("子库版本 ({SubVersion}) 大于主库版本 ({MainVersion})，开始同步数据",
                        subDbVersion, mainDbVersion);
                }

                // 4. 从子库导出配置
                var subConfig = ExportConfigFromSubDatabase(turbineId, subDbConnectionString);
                if (subConfig == null)
                {
                    _logger.LogError("从子库导出配置失败: {TurbineId}", turbineId);
                    return false;
                }

                // 5. 导入配置到主库
                bool importSuccess = ImportConfigToMainDatabase(subConfig);
                if (!importSuccess)
                {
                    _logger.LogError("导入配置到主库失败: {TurbineId}", turbineId);
                    return false;
                }

                _logger.LogInformation("成功同步机组 {TurbineId} 的配置到主库", turbineId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "同步机组配置失败");
                return false;
            }
        }

        /// <summary>
        /// 获取主库中的数据库版本
        /// </summary>
        private int? GetMainDatabaseVersion(string turbineId)
        {
            try
            {
                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    var dau = ctx.DAUnits.FirstOrDefault(d => d.WindTurbineID == turbineId);
                    if (dau != null)
                    {
                        // 使用MeasDefVersion作为数据库版本
                        return dau.MeasDefVersion;
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取主库版本失败: {TurbineId}", turbineId);
                return null;
            }
        }

        /// <summary>
        /// 获取子库中的数据库版本
        /// </summary>
        private int GetSubDatabaseVersion(string turbineId, string subDbConnectionString)
        {
            try
            {
                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(subDbConnectionString))
                {
                    var dau = ctx.DAUnits.FirstOrDefault(d => d.WindTurbineID == turbineId);
                    if (dau != null)
                    {
                        return dau.MeasDefVersion;
                    }
                }
                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取子库版本失败: {TurbineId}", turbineId);
                return 0;
            }
        }


        private int GetSubDatabaseVersionByTur(string turbineId, string subDbConnectionString)
        {
            try
            {
                using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(subDbConnectionString))
                {
                    var tur = ctx.DevWindTurbines.FirstOrDefault(d => d.WindTurbineID == turbineId);
                    if (tur != null)
                    {
                        int.TryParse(tur.ConfigVersion, out int resVersion);
                        return resVersion;
                    }
                }
                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取子库版本失败: {TurbineId}", turbineId);
                return 0;
            }
        }

        private WindTurbine? GetSubDatabaseWindturbineID(string subDbConnectionString)
        {
            try
            {
                using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(subDbConnectionString))
                {
                    return ctx.DevWindTurbines.FirstOrDefault();
                }
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        /// <summary>
        /// 从子库导出配置
        /// </summary>
        private TemplateTurbineModel? ExportConfigFromSubDatabase(string turbineId, string subDbConnectionString)
        {
            try
            {
                _logger.LogInformation("开始从子库导出机组 {TurbineId} 的配置", turbineId);

                // 创建一个临时的TurbineExprotManager实例来处理子库导出
                // 由于TurbineConfigExport是静态方法且使用ConfigInfo.DBConnName，我们需要自己实现导出逻辑
                return ExportTurbineConfigFromDatabase(turbineId, subDbConnectionString);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从子库导出配置失败: {TurbineId}", turbineId);
                return null;
            }
        }

        /// <summary>
        /// 从指定数据库导出机组配置 - 完整实现从子库读取配置
        /// </summary>
        private TemplateTurbineModel? ExportTurbineConfigFromDatabase(string turbineId, string connectionString)
        {
            try
            {
                _logger.LogInformation("开始从子库导出机组 {TurbineId} 的配置", turbineId);
                var templateTurbineModel = new TemplateTurbineModel();

                // 1. 读取机组设备树信息
                using (CMSFramework.EF.DevContext devCtx = new CMSFramework.EF.DevContext(connectionString))
                {
                    var turbine = devCtx.DevWindTurbines.Find(turbineId);
                    if (turbine == null)
                    {
                        _logger.LogError("在子库中未找到机组 {TurbineId}", turbineId);
                        return null;
                    }
                    templateTurbineModel.DevWindPark = devCtx.DevWindParks.FirstOrDefault();
                    // 基本信息
                    templateTurbineModel.WindParkID = turbine.WindParkID;
                    templateTurbineModel.WindTurbineID = turbine.WindTurbineID;
                    templateTurbineModel.WindTurbineCode = turbine.WindTurbineCode;
                    templateTurbineModel.WindTurbineModel = turbine.WindTurbineModel;
                    templateTurbineModel.WindTurbineName = turbine.WindTurbineName;
                    templateTurbineModel.OperationalDate = turbine.OperationalDate;
                    templateTurbineModel.MinWorkingRotSpeed = turbine.MinWorkingRotSpeed;
                    templateTurbineModel.ConfigVersion = turbine.ConfigVersion;

                    // 读取相关的设备树数据
                    templateTurbineModel.TurComponentList = devCtx.DevTurComponents
                        .Where(item => item.WindTurbineID == turbineId).ToList();

                    templateTurbineModel.VibMeasLocList = devCtx.DevMeasLocVibrations
                        .Where(item => item.WindTurbineID == turbineId).ToList();

                    templateTurbineModel.DevMeasLocRotSpds = devCtx.DevMeasLocRotSpds
                        .Where(item => item.WindTurbineID == turbineId).ToList();

                    templateTurbineModel.ProcessMeasLocList = devCtx.DevMeasLocProcesses
                        .Where(item => item.WindTurbineID == turbineId).ToList();

                    templateTurbineModel.VoltageCurrentMeasLocList = devCtx.DevMeasLocVoltageCurrents
                        .Where(item => item.WindTurbineID == turbineId).ToList();


                    templateTurbineModel.svmMeasDataList = devCtx.DevMeasLocSVMs
                         .Where(item => item.WindTurbineID == turbineId).ToList();

                    _logger.LogDebug("成功从子库读取设备树信息: {TurbineId}", turbineId);
                }

                // 2. 读取主控信息
                using (CMSFramework.EF.MCSContext mcsCtx = new CMSFramework.EF.MCSContext(connectionString))
                {
                    templateTurbineModel.Mcs = mcsCtx.MCSystems.Find(turbineId);

                    templateTurbineModel.MCSChannelStateParamList = mcsCtx.MCSRegisters
                        .OfType<MCSChannelStateParam>()
                        .Where(item => item.WindTurbineID == turbineId).ToList();

                    templateTurbineModel.MCSChannelValueParamList = mcsCtx.MCSRegisters
                        .OfType<MCSChannelValueParam>()
                        .Where(item => item.WindTurbineID == turbineId).ToList();

                    _logger.LogDebug("成功从子库读取主控信息: {TurbineId}", turbineId);
                }

                // 3. 读取采集单元信息
                using (CMSFramework.EF.DauContext dauCtx = new CMSFramework.EF.DauContext(connectionString))
                {
                    var dauList = dauCtx.DAUnits.Where(obj => obj.WindTurbineID == turbineId).ToList();
                    foreach (var dau in dauList)
                    {
                        dau.RotSpeedChannelList = dauCtx.DAURotSpdChannels
                            .Where(item => item.WindTurbineID == turbineId && item.DauID == dau.DauID).ToList();

                        dau.DAUChannelList = dauCtx.DAUVibChannels
                            .Where(item => item.WindTurbineID == turbineId && item.DauID == dau.DauID).ToList();

                        dau.ProcessChannelList = dauCtx.DAUProcessChannels
                            .Where(item => item.WindTurbineID == turbineId && item.DauID == dau.DauID).ToList();

                        dau.VoltageCurrentList = dauCtx.DauChannelVoltageCurrents
                            .Where(item => item.WindTurbineID == turbineId && item.DauID == dau.DauID).ToList();
                    }
                    templateTurbineModel.WindDAUList = dauList;

                    // 读取其他DAU相关配置
                    templateTurbineModel.modbusDefslist = dauCtx.ModbusUnits
                        .Where(item => item.WindTurbineID == turbineId).ToList();
                    if(templateTurbineModel.modbusDefslist!=null && templateTurbineModel.modbusDefslist.Count > 0)
                    {
                        templateTurbineModel.modbusDefslist.ForEach(modbusdef =>
                        {
                            modbusdef.ModbusChannelList = dauCtx.ModbusChannelList.Where(t => t.WindTurbineID == modbusdef.WindTurbineID && modbusdef.ModbusDeviceID == t.ModbusDeviceID).ToList();
                        });
                    }

                    templateTurbineModel.serialServerslist = dauCtx.SerialServers
                        .Where(item => item.WindTurbineID == turbineId).ToList();

                    templateTurbineModel.serialPorts = dauCtx.SerialPorts
                        .Where(item => item.WindTurbineID == turbineId).ToList();

                    templateTurbineModel.timUnitList = dauCtx.TimCalibrations
                        .Where(item => item.WindTurbineID == turbineId).ToList();

                    templateTurbineModel.oilUnitList = dauCtx.OilUnits
                        .Where(item => item.WindTurbineID == turbineId).ToList();

                    

                    templateTurbineModel.svmuList = dauCtx.SVMUnits
                        .Where(item => item.AssocWindTurbineID == turbineId).ToList();

                   

                    templateTurbineModel.ultrasonicList = dauCtx.UltrasonicChannelConfigs
                        .Where(item => item.WindTurbineID == turbineId).ToList();


                    templateTurbineModel.svmRegisterList = dauCtx.SVMRegisters
                        .Where(item => item.AssocWindTurbineID == turbineId).ToList();


                    templateTurbineModel.dauSftpConfigs = dauCtx.DauSftpConfigs
                        .Where(item=>item.WindTurbineID==turbineId).ToList();

                    templateTurbineModel.dauExtendConfigs = dauCtx.DauExtendConfigs
                        .Where(item => item.WindTurbineID == turbineId).ToList();

                    _logger.LogDebug("成功从子库读取采集单元信息: {TurbineId}", turbineId);
                }

                // 4. 读取测量定义信息
                using (CMSFramework.EF.MeasDef.MDFContext mdfCtx = new CMSFramework.EF.MeasDef.MDFContext(connectionString))
                {
                    // 读取测量定义
                    var measDefinitionList = mdfCtx.MeasDefinitions
                        .Where(item => item.WindTurbineID == turbineId)
                        .ToList()
                        .OrderBy(t => int.Parse(t.MeasDefinitionID))
                        .ToList();

                    // 为每个测量定义读取相关数据
                    foreach (var measDef in measDefinitionList)
                    {
                        // 读取工况定义
                        measDef.ProcessDefList = mdfCtx.MDFWorkConditions
                            .Where(item => item.WindTurbineID == turbineId && item.MeasDefinitionID == measDef.MeasDefinitionID)
                            .ToList();

                        // 读取转速波形定义
                        measDef.RotSpdWaveDefList = mdfCtx.MDFWaveDefRotSpds
                            .Where(item => item.WindTurbineID == turbineId && item.MeasDefinitionID == measDef.MeasDefinitionID)
                            .ToList();

                        // 读取波形定义
                        measDef.WaveDefList = mdfCtx.MDFWaveDefinitions
                            .Where(item => item.WindTurbineID == turbineId && item.MeasDefinitionID == measDef.MeasDefinitionID)
                            .ToList();

                        // 读取测量方案
                        measDef.SolutionList = mdfCtx.MeasSolutions
                            .Where(item => item.WindTurbineID == turbineId && item.MeasDefinitionID == measDef.MeasDefinitionID)
                            .ToList();

                        // 特征值
                        measDef.VibEigenValueConf = mdfCtx.TimeDomainEvConfs.Where(item=>item.WindTurbineID == turbineId 
                        &&item.MeasDefinitionID == measDef.MeasDefinitionID).ToList();
                        measDef.ProcessSuperviseDefList = mdfCtx.ProcessEvConfs.Where(item => item.WindTurbineID == turbineId
                        && item.MeasDefinitionID == measDef.MeasDefinitionID).ToList();

                        // 触发采集
                        
                        var TriggerRulesRes = mdfCtx.TriggerRuleDefs.Where(t => t.WindTurbineID == turbineId && t.MeasDefinitionID == measDef.MeasDefinitionID).ToList();
                        if (TriggerRulesRes.Count > 0)
                        {
                            foreach (var k in TriggerRulesRes)
                            {
                                k.SupervisedVariables = mdfCtx.TriggerProcess.Where(t => t.RuleID == k.RuleID).ToList();
                                k.TriggerTime = mdfCtx.TriggerTimes.FirstOrDefault(t => t.RuleID == k.RuleID);
                                k.ExecuteMdfs = mdfCtx.ExecuteMdfs.Where(t => t.RuleID == k.RuleID).ToList();
                            }
                        }
                        measDef.TriggerRules = TriggerRulesRes;

                    }

                    templateTurbineModel.MeasDefinitionList = measDefinitionList;

                    // 读取测量定义扩展信息
                    templateTurbineModel.MeasDefinition_ExList = mdfCtx.MeasDefinitions_Exs
                        .Where(item => item.WindTurbineID == turbineId).ToList();

                    templateTurbineModel.waveDef_SVMsList = mdfCtx.SVMWaveDefinitions
                         .Where(item => item.WindTurbineID == turbineId).ToList();

   
                    templateTurbineModel.modbusDefs = mdfCtx.ModbusDefs
                         .Where(item => item.WindTurbineID == turbineId).ToList();

                    _logger.LogDebug("成功从子库读取测量定义信息: {TurbineId}", turbineId);
                }

                // 5. 读取报警定义信息,暂无报警
                //using (CMSFramework.EF.MonContext alarmCtx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
                //{
                //    templateTurbineModel.AlarmDefinitions = alarmCtx.AlarmDefinitions
                //        .Where(item => item.WindTurbineID == turbineId).ToList();

                //    _logger.LogDebug("成功从子库读取报警定义信息: {TurbineId}", turbineId);
                //}

                _logger.LogInformation("成功从子库导出机组 {TurbineId} 的完整配置", turbineId);
                return templateTurbineModel;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从子库导出机组配置失败: {TurbineId}", turbineId);
                return null;
            }
        }

        /// <summary>
        /// 导入配置到主库 - 参考TurbineConfigImport方法的完整实现
        /// </summary>
        private bool ImportConfigToMainDatabase(TemplateTurbineModel config)
        {
            try
            {
                _logger.LogInformation("开始导入配置到主库: {TurbineId}", config.WindTurbineID);

                // 使用现有的TurbineConfigImport方法，它已经包含了完整的导入逻辑
                // 包括：设备树信息、采集单元、主控信息、测量定义、报警定义等
                bool result = TurbineExprotManager.TurbineConfigImport(config);

                if (result)
                {
                    _logger.LogInformation("成功导入配置到主库: {TurbineId}", config.WindTurbineID);
                }
                else
                {
                    _logger.LogError("导入配置到主库失败: {TurbineId}", config.WindTurbineID);
                }
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导入配置到主库时发生异常: {TurbineId}", config?.WindTurbineID);
                return false;
            }
        }

        /// <summary>
        /// 批量同步多个机组的配置
        /// </summary>
        /// <param name="turbineConfigs">机组ID和配置数据库字节数组的字典</param>
        /// <returns>同步结果字典</returns>
        public async Task<Dictionary<string, bool>> BatchSyncConfigs(Dictionary<string, byte[]> turbineConfigs)
        {
            var results = new Dictionary<string, bool>();

            foreach (var kvp in turbineConfigs)
            {
                var turbineId = kvp.Key;
                var configDbBytes = kvp.Value;

                try
                {
                    bool success = await SyncTurbineConfig(configDbBytes);
                    results[turbineId] = success;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "同步机组配置失败: {TurbineId}", turbineId);
                    results[turbineId] = false;
                }
            }

            return results;
        }
    }
}
