@echo off
setlocal enabledelayedexpansion

set SERVICE_NAME=WRD_CMSConfigureWeb_Service

echo ========================================
echo Windows 服务卸载脚本
echo ========================================

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 错误: 需要管理员权限运行此脚本
    echo 请右键点击此脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)

REM 检查服务是否存在
echo 检查服务状态...
sc query %SERVICE_NAME% >nul 2>&1
if %errorLevel% neq 0 (
    echo 服务 %SERVICE_NAME% 不存在
    pause
    exit /b 0
)

echo 正在停止服务...
net stop %SERVICE_NAME% >nul 2>&1
if %errorLevel% equ 0 (
    echo 服务已停止
) else (
    echo 服务可能已经停止或停止失败
)

echo 等待服务完全停止...
timeout /t 3 >nul

echo 正在删除服务...
sc delete %SERVICE_NAME%
if %errorLevel% equ 0 (
    echo 服务删除成功！
) else (
    echo 服务删除失败
    pause
    exit /b 1
)

echo ========================================
echo 服务卸载完成！
echo ========================================

pause
