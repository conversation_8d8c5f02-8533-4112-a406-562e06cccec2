import{co as S,Y as b,r as C,j as f,c as I,i as g,b as s,d as u,a as B,u as R,o as x,g as P,t as $,m as c,cz as k,v as D}from"./index-sMW2Pm6g.js";import{u as F}from"./devTree-BcT4pWCP.js";import{_ as z}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{F as U,_ as V}from"./index-ljzR8VKX.js";import{I as q,p as L}from"./index-DRy8se-f.js";import{B as j}from"./index-Bi-LLAnN.js";import"./tools-DC78Tda0.js";import"./styleChecker-LI4Lr2UF.js";const A={class:"login-container"},E={__name:"Login",setup(H){const h=S(),a=F(),_=B(),v=R(),n=b({username:"",password:""}),i=C(!1),w=async()=>{try{i.value=!0;let r=await h.fetchlogin(n);if(r.code==1){c.success("登录成功"),k(),await new Promise(t=>setTimeout(t,0)),await a.getDevTreeDatas();const e=_.getRoutes().filter(t=>{var o;return(o=t.meta)==null?void 0:o.parent});let d="",l="managePark",m=window.localStorage.getItem("role")?JSON.parse(window.localStorage.getItem("role")).moduleIds:[];if((!m||!m.length)&&c.error("暂无权限"),e&&e.length){for(let t=0;t<e.length;t++)if(e[t].children&&e[t].children.length&&a.devTreeCurrentNode&&a.devTreeCurrentNode.id){const o=e[t].children.find(N=>N.meta.selectNodeType===a.devTreeCurrentNode.type);if(o){d=`${e[t].path}/${o.path}`,l=o.name,window.localStorage.setItem("homePath",d);break}}}let p={};a.devTreeCurrentNode&&a.devTreeCurrentNode.id&&(p={login:!0,id:a.devTreeCurrentNode.id,name:l}),D(v,_,p)}else c.error(r.msg)}catch(r){console.error("登录失败:",r),c.error(r.message||"登录失败，请稍后重试")}finally{i.value=!1}},T=f(()=>[{required:!0,message:"账号不能为空!",trigger:["change"]},{pattern:/\S/,message:"账号不能为空字符串!"}]),y=f(()=>[{required:!0,message:"密码不能为空!",trigger:["change"]},{pattern:/\S/,message:"密码不能为空字符串!"}]);return(r,e)=>{const d=q,l=V,m=L,p=j,t=U;return x(),I("div",A,[e[2]||(e[2]=g("div",{class:"logoBox"},[g("div",{class:"header"},[g("span",{class:"title"},"配置网站")])],-1)),s(t,{class:"user-layout-login",model:n,onFinish:w},{default:u(()=>[s(l,{name:"username",rules:T.value},{default:u(()=>[s(d,{size:"large",type:"text",autocomplete:"username",placeholder:r.$t("user.login.username.placeholder"),value:n.username,"onUpdate:value":e[0]||(e[0]=o=>n.username=o)},null,8,["placeholder","value"])]),_:1},8,["rules"]),s(l,{name:"password",rules:y.value},{default:u(()=>[s(m,{size:"large",placeholder:r.$t("user.login.password.placeholder"),autocomplete:"current-password",value:n.password,"onUpdate:value":e[1]||(e[1]=o=>n.password=o)},null,8,["placeholder","value"])]),_:1},8,["rules"]),s(l,{style:{"margin-top":"24px"}},{default:u(()=>[s(p,{type:"primary",htmlType:"submit",class:"login-button",loading:i.value,disabled:i.value},{default:u(()=>[P($(r.$t("user.login.login")),1)]),_:1},8,["loading","disabled"])]),_:1})]),_:1},8,["model"])])}}},X=z(E,[["__scopeId","data-v-5acecf14"]]);export{X as default};
