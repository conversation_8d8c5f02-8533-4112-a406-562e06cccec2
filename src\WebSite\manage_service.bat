@echo off
setlocal enabledelayedexpansion

set SERVICE_NAME=WRD_CMSConfigureWeb_Service

echo ========================================
echo Windows 服务管理脚本
echo ========================================

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 错误: 需要管理员权限运行此脚本
    echo 请右键点击此脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)

:MENU
echo.
echo 请选择操作:
echo 1. 查看服务状态
echo 2. 启动服务
echo 3. 停止服务
echo 4. 重启服务
echo 5. 查看服务日志
echo 6. 退出
echo.
set /p choice=请输入选择 (1-6): 

if "%choice%"=="1" goto STATUS
if "%choice%"=="2" goto START
if "%choice%"=="3" goto STOP
if "%choice%"=="4" goto RESTART
if "%choice%"=="5" goto LOGS
if "%choice%"=="6" goto EXIT
echo 无效选择，请重新输入
goto MENU

:STATUS
echo ========================================
echo 服务状态:
sc query %SERVICE_NAME%
echo ========================================
goto MENU

:START
echo 正在启动服务...
net start %SERVICE_NAME%
if %errorLevel% equ 0 (
    echo 服务启动成功！
) else (
    echo 服务启动失败，请检查事件日志
)
goto MENU

:STOP
echo 正在停止服务...
net stop %SERVICE_NAME%
if %errorLevel% equ 0 (
    echo 服务停止成功！
) else (
    echo 服务停止失败
)
goto MENU

:RESTART
echo 正在重启服务...
net stop %SERVICE_NAME%
timeout /t 3 >nul
net start %SERVICE_NAME%
if %errorLevel% equ 0 (
    echo 服务重启成功！
) else (
    echo 服务重启失败，请检查事件日志
)
goto MENU

:LOGS
echo 正在打开事件查看器...
echo 请查看 Windows 日志 -> 应用程序 中的 %SERVICE_NAME% 相关日志
eventvwr.exe
goto MENU

:EXIT
echo 退出管理脚本
exit /b 0
