@echo off
setlocal enabledelayedexpansion

set PROJECT_PATH=WTCMSLive.WebSite.Core
set PUBLISH_PATH=%~dp0publish

echo ========================================
echo 配置验证脚本
echo ========================================

echo 1. 检查环境变量...
echo ASPNETCORE_ENVIRONMENT = %ASPNETCORE_ENVIRONMENT%

REM 检查系统级环境变量
for /f "tokens=2*" %%a in ('reg query "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v ASPNETCORE_ENVIRONMENT 2^>nul') do (
    echo 系统级环境变量 = %%b
)

echo.
echo 2. 检查配置文件...

REM 检查基础配置文件
if exist "%PROJECT_PATH%\appsettings.json" (
    echo ✓ appsettings.json 存在
) else (
    echo ✗ appsettings.json 不存在
)

REM 检查生产环境配置文件
if exist "%PROJECT_PATH%\appsettings.Production.json" (
    echo ✓ appsettings.Production.json 存在
) else (
    echo ✗ appsettings.Production.json 不存在
)

REM 检查其他环境配置文件
if exist "%PROJECT_PATH%\appsettings.Development.json" (
    echo ✓ appsettings.Development.json 存在
) else (
    echo - appsettings.Development.json 不存在（可选）
)

echo.
echo 3. 检查发布文件...

if exist "%PUBLISH_PATH%" (
    echo ✓ 发布目录存在: %PUBLISH_PATH%
    
    if exist "%PUBLISH_PATH%\%PROJECT_PATH%.exe" (
        echo ✓ 可执行文件存在
    ) else (
        echo ✗ 可执行文件不存在
    )
    
    if exist "%PUBLISH_PATH%\appsettings.json" (
        echo ✓ 发布目录中的 appsettings.json 存在
    ) else (
        echo ✗ 发布目录中的 appsettings.json 不存在
    )
    
    if exist "%PUBLISH_PATH%\appsettings.Production.json" (
        echo ✓ 发布目录中的 appsettings.Production.json 存在
    ) else (
        echo ✗ 发布目录中的 appsettings.Production.json 不存在
    )
) else (
    echo ✗ 发布目录不存在，请先运行 publish_service.bat
)

echo.
echo 4. 检查服务状态...
sc query WRD_CMSConfigureWeb_Service >nul 2>&1
if %errorLevel% equ 0 (
    echo ✓ 服务已安装
    sc query WRD_CMSConfigureWeb_Service | findstr "STATE"
) else (
    echo - 服务未安装
)

echo.
echo 5. 检查端口占用...
netstat -an | findstr ":803" >nul 2>&1
if %errorLevel% equ 0 (
    echo ✓ 端口 803 正在使用中
    netstat -ano | findstr ":803"
) else (
    echo - 端口 803 未被占用
)

echo.
echo 6. 配置建议...

if "%ASPNETCORE_ENVIRONMENT%"=="" (
    echo ⚠️  建议设置 ASPNETCORE_ENVIRONMENT 环境变量
    echo    运行: set_environment.bat
)

if not exist "%PUBLISH_PATH%" (
    echo ⚠️  需要先发布应用程序
    echo    运行: publish_service.bat
)

echo.
echo ========================================
echo 验证完成
echo ========================================

pause
