var ye=Object.defineProperty;var ve=(n,e,t)=>e in n?ye(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t;var U=(n,e,t)=>ve(n,typeof e!="symbol"?e+"":e,t);import{r as te,y as ke,z as j,A as me,B as Ie,C as ne,D as Ee,E as Pe,G as ee,H as z,_ as W,j as p,b as _,F as be,I as $,J as $e,K as Te,L as Re,M as xe,N as De,O as Ae,P as We,Q as Me,R as Oe,S as Ne}from"./index-sMW2Pm6g.js";import{d as He,i as oe}from"./index-Bi-LLAnN.js";import{C as je}from"./index-5GdjZhcp.js";import{T as Le}from"./styleChecker-LI4Lr2UF.js";import{g as K}from"./tools-DC78Tda0.js";const Be=()=>{const n=te(new Map),e=t=>o=>{n.value.set(t,o)};return ke(()=>{n.value=new Map}),[e,n]},Ue=["normal","exception","active","success"],G=()=>({prefixCls:String,type:j(),percent:Number,format:Pe(),status:j(),showInfo:Ee(),strokeWidth:Number,strokeLinecap:j(),strokeColor:ne(),trailColor:String,width:Number,success:Ie(),gapDegree:Number,gapPosition:j(),size:me([String,Number,Array]),steps:Number,successPercent:Number,title:String,progressStatus:j()});function M(n){return!n||n<0?0:n>100?100:n}function V(n){let{success:e,successPercent:t}=n,o=t;return e&&"progress"in e&&(He(!1,"Progress","`success.progress` is deprecated. Please use `success.percent` instead."),o=e.progress),e&&"percent"in e&&(o=e.percent),o}function Fe(n){let{percent:e,success:t,successPercent:o}=n;const r=M(V({success:t,successPercent:o}));return[r,M(M(e)-r)]}function qe(n){let{success:e={},strokeColor:t}=n;const{strokeColor:o}=e;return[o||ee.green,t||null]}const Y=(n,e,t)=>{var o,r,s,i;let a=-1,l=-1;if(e==="step"){const u=t.steps,h=t.strokeWidth;typeof n=="string"||typeof n>"u"?(a=n==="small"?2:14,l=h??8):typeof n=="number"?[a,l]=[n,n]:[a=14,l=8]=n,a*=u}else if(e==="line"){const u=t==null?void 0:t.strokeWidth;typeof n=="string"||typeof n>"u"?l=u||(n==="small"?6:8):typeof n=="number"?[a,l]=[n,n]:[a=-1,l=8]=n}else(e==="circle"||e==="dashboard")&&(typeof n=="string"||typeof n>"u"?[a,l]=n==="small"?[60,60]:[120,120]:typeof n=="number"?[a,l]=[n,n]:(a=(r=(o=n[0])!==null&&o!==void 0?o:n[1])!==null&&r!==void 0?r:120,l=(i=(s=n[0])!==null&&s!==void 0?s:n[1])!==null&&i!==void 0?i:120));return{width:a,height:l}};var ze=function(n,e){var t={};for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&e.indexOf(o)<0&&(t[o]=n[o]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(n);r<o.length;r++)e.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(n,o[r])&&(t[o[r]]=n[o[r]]);return t};const Xe=()=>W(W({},G()),{strokeColor:ne(),direction:j()}),Ke=n=>{let e=[];return Object.keys(n).forEach(t=>{const o=parseFloat(t.replace(/%/g,""));isNaN(o)||e.push({key:o,value:n[t]})}),e=e.sort((t,o)=>t.key-o.key),e.map(t=>{let{key:o,value:r}=t;return`${r} ${o}%`}).join(", ")},Ve=(n,e)=>{const{from:t=ee.blue,to:o=ee.blue,direction:r=e==="rtl"?"to left":"to right"}=n,s=ze(n,["from","to","direction"]);if(Object.keys(s).length!==0){const i=Ke(s);return{backgroundImage:`linear-gradient(${r}, ${i})`}}return{backgroundImage:`linear-gradient(${r}, ${t}, ${o})`}},Je=z({compatConfig:{MODE:3},name:"ProgressLine",inheritAttrs:!1,props:Xe(),setup(n,e){let{slots:t,attrs:o}=e;const r=p(()=>{const{strokeColor:g,direction:b}=n;return g&&typeof g!="string"?Ve(g,b):{backgroundColor:g}}),s=p(()=>n.strokeLinecap==="square"||n.strokeLinecap==="butt"?0:void 0),i=p(()=>n.trailColor?{backgroundColor:n.trailColor}:void 0),a=p(()=>{var g;return(g=n.size)!==null&&g!==void 0?g:[-1,n.strokeWidth||(n.size==="small"?6:8)]}),l=p(()=>Y(a.value,"line",{strokeWidth:n.strokeWidth})),u=p(()=>{const{percent:g}=n;return W({width:`${M(g)}%`,height:`${l.value.height}px`,borderRadius:s.value},r.value)}),h=p(()=>V(n)),w=p(()=>{const{success:g}=n;return{width:`${M(h.value)}%`,height:`${l.value.height}px`,borderRadius:s.value,backgroundColor:g==null?void 0:g.strokeColor}}),m={width:l.value.width<0?"100%":l.value.width,height:`${l.value.height}px`};return()=>{var g;return _(be,null,[_("div",$($({},o),{},{class:[`${n.prefixCls}-outer`,o.class],style:[o.style,m]}),[_("div",{class:`${n.prefixCls}-inner`,style:i.value},[_("div",{class:`${n.prefixCls}-bg`,style:u.value},null),h.value!==void 0?_("div",{class:`${n.prefixCls}-success-bg`,style:w.value},null):null])]),(g=t.default)===null||g===void 0?void 0:g.call(t)])}}}),Ge={percent:0,prefixCls:"vc-progress",strokeColor:"#2db7f5",strokeLinecap:"round",strokeWidth:1,trailColor:"#D9D9D9",trailWidth:1},Ye=n=>{const e=te(null);return $e(()=>{const t=Date.now();let o=!1;n.value.forEach(r=>{const s=(r==null?void 0:r.$el)||r;if(!s)return;o=!0;const i=s.style;i.transitionDuration=".3s, .3s, .3s, .06s",e.value&&t-e.value<100&&(i.transitionDuration="0s, 0s")}),o&&(e.value=Date.now())}),n},Qe={gapDegree:Number,gapPosition:{type:String},percent:{type:[Array,Number]},prefixCls:String,strokeColor:{type:[Object,String,Array]},strokeLinecap:{type:String},strokeWidth:Number,trailColor:String,trailWidth:Number,transition:String};var Ze=function(n,e){var t={};for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&e.indexOf(o)<0&&(t[o]=n[o]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(n);r<o.length;r++)e.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(n,o[r])&&(t[o[r]]=n[o[r]]);return t};let ae=0;function le(n){return+n.replace("%","")}function he(n){return Array.isArray(n)?n:[n]}function ue(n,e,t,o){let r=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0,s=arguments.length>5?arguments[5]:void 0;const i=50-o/2;let a=0,l=-i,u=0,h=-2*i;switch(s){case"left":a=-i,l=0,u=2*i,h=0;break;case"right":a=i,l=0,u=-2*i,h=0;break;case"bottom":l=i,h=2*i;break}const w=`M 50,50 m ${a},${l}
   a ${i},${i} 0 1 1 ${u},${-h}
   a ${i},${i} 0 1 1 ${-u},${h}`,m=Math.PI*2*i,g={stroke:t,strokeDasharray:`${e/100*(m-r)}px ${m}px`,strokeDashoffset:`-${r/2+n/100*(m-r)}px`,transition:"stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s, stroke-width .06s ease .3s, opacity .3s ease 0s"};return{pathString:w,pathStyle:g}}const et=z({compatConfig:{MODE:3},name:"VCCircle",props:oe(Qe,Ge),setup(n){ae+=1;const e=te(ae),t=p(()=>he(n.percent)),o=p(()=>he(n.strokeColor)),[r,s]=Be();Ye(s);const i=()=>{const{prefixCls:a,strokeWidth:l,strokeLinecap:u,gapDegree:h,gapPosition:w}=n;let m=0;return t.value.map((g,b)=>{const f=o.value[b]||o.value[o.value.length-1],P=Object.prototype.toString.call(f)==="[object Object]"?`url(#${a}-gradient-${e.value})`:"",{pathString:E,pathStyle:D}=ue(m,g,f,l,h,w);m+=g;const A={key:b,d:E,stroke:P,"stroke-linecap":u,"stroke-width":l,opacity:g===0?0:1,"fill-opacity":"0",class:`${a}-circle-path`,style:D};return _("path",$({ref:r(b)},A),null)})};return()=>{const{prefixCls:a,strokeWidth:l,trailWidth:u,gapDegree:h,gapPosition:w,trailColor:m,strokeLinecap:g,strokeColor:b}=n,f=Ze(n,["prefixCls","strokeWidth","trailWidth","gapDegree","gapPosition","trailColor","strokeLinecap","strokeColor"]),{pathString:P,pathStyle:E}=ue(0,100,m,l,h,w);delete f.percent;const D=o.value.find(k=>Object.prototype.toString.call(k)==="[object Object]"),A={d:P,stroke:m,"stroke-linecap":g,"stroke-width":u||l,"fill-opacity":"0",class:`${a}-circle-trail`,style:E};return _("svg",$({class:`${a}-circle`,viewBox:"0 0 100 100"},f),[D&&_("defs",null,[_("linearGradient",{id:`${a}-gradient-${e.value}`,x1:"100%",y1:"0%",x2:"0%",y2:"0%"},[Object.keys(D).sort((k,B)=>le(k)-le(B)).map((k,B)=>_("stop",{key:B,offset:k,"stop-color":D[k]},null))])]),_("path",A,null),i().reverse()])}}}),tt=()=>W(W({},G()),{strokeColor:ne()}),nt=3,ot=n=>nt/n*100,rt=z({compatConfig:{MODE:3},name:"ProgressCircle",inheritAttrs:!1,props:oe(tt(),{trailColor:null}),setup(n,e){let{slots:t,attrs:o}=e;const r=p(()=>{var f;return(f=n.width)!==null&&f!==void 0?f:120}),s=p(()=>{var f;return(f=n.size)!==null&&f!==void 0?f:[r.value,r.value]}),i=p(()=>Y(s.value,"circle")),a=p(()=>{if(n.gapDegree||n.gapDegree===0)return n.gapDegree;if(n.type==="dashboard")return 75}),l=p(()=>({width:`${i.value.width}px`,height:`${i.value.height}px`,fontSize:`${i.value.width*.15+6}px`})),u=p(()=>{var f;return(f=n.strokeWidth)!==null&&f!==void 0?f:Math.max(ot(i.value.width),6)}),h=p(()=>n.gapPosition||n.type==="dashboard"&&"bottom"||void 0),w=p(()=>Fe(n)),m=p(()=>Object.prototype.toString.call(n.strokeColor)==="[object Object]"),g=p(()=>qe({success:n.success,strokeColor:n.strokeColor})),b=p(()=>({[`${n.prefixCls}-inner`]:!0,[`${n.prefixCls}-circle-gradient`]:m.value}));return()=>{var f;const P=_(et,{percent:w.value,strokeWidth:u.value,trailWidth:u.value,strokeColor:g.value,strokeLinecap:n.strokeLinecap,trailColor:n.trailColor,prefixCls:n.prefixCls,gapDegree:a.value,gapPosition:h.value},null);return _("div",$($({},o),{},{class:[b.value,o.class],style:[o.style,l.value]}),[i.value.width<=20?_(Le,null,{default:()=>[_("span",null,[P])],title:t.default}):_(be,null,[P,(f=t.default)===null||f===void 0?void 0:f.call(t)])])}}}),st=()=>W(W({},G()),{steps:Number,strokeColor:me(),trailColor:String}),it=z({compatConfig:{MODE:3},name:"Steps",props:st(),setup(n,e){let{slots:t}=e;const o=p(()=>Math.round(n.steps*((n.percent||0)/100))),r=p(()=>{var a;return(a=n.size)!==null&&a!==void 0?a:[n.size==="small"?2:14,n.strokeWidth||8]}),s=p(()=>Y(r.value,"step",{steps:n.steps,strokeWidth:n.strokeWidth||8})),i=p(()=>{const{steps:a,strokeColor:l,trailColor:u,prefixCls:h}=n,w=[];for(let m=0;m<a;m+=1){const g=Array.isArray(l)?l[m]:l,b={[`${h}-steps-item`]:!0,[`${h}-steps-item-active`]:m<=o.value-1};w.push(_("div",{key:m,class:b,style:{backgroundColor:m<=o.value-1?g:u,width:`${s.value.width/a}px`,height:`${s.value.height}px`}},null))}return w});return()=>{var a;return _("div",{class:`${n.prefixCls}-steps-outer`},[i.value,(a=t.default)===null||a===void 0?void 0:a.call(t)])}}}),ct=new De("antProgressActive",{"0%":{transform:"translateX(-100%) scaleX(0)",opacity:.1},"20%":{transform:"translateX(-100%) scaleX(0)",opacity:.5},to:{transform:"translateX(0) scaleX(1)",opacity:0}}),at=n=>{const{componentCls:e,iconCls:t}=n;return{[e]:W(W({},xe(n)),{display:"inline-block","&-rtl":{direction:"rtl"},"&-line":{position:"relative",width:"100%",fontSize:n.fontSize,marginInlineEnd:n.marginXS,marginBottom:n.marginXS},[`${e}-outer`]:{display:"inline-block",width:"100%"},[`&${e}-show-info`]:{[`${e}-outer`]:{marginInlineEnd:`calc(-2em - ${n.marginXS}px)`,paddingInlineEnd:`calc(2em + ${n.paddingXS}px)`}},[`${e}-inner`]:{position:"relative",display:"inline-block",width:"100%",overflow:"hidden",verticalAlign:"middle",backgroundColor:n.progressRemainingColor,borderRadius:n.progressLineRadius},[`${e}-inner:not(${e}-circle-gradient)`]:{[`${e}-circle-path`]:{stroke:n.colorInfo}},[`${e}-success-bg, ${e}-bg`]:{position:"relative",backgroundColor:n.colorInfo,borderRadius:n.progressLineRadius,transition:`all ${n.motionDurationSlow} ${n.motionEaseInOutCirc}`},[`${e}-success-bg`]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,backgroundColor:n.colorSuccess},[`${e}-text`]:{display:"inline-block",width:"2em",marginInlineStart:n.marginXS,color:n.progressInfoTextColor,lineHeight:1,whiteSpace:"nowrap",textAlign:"start",verticalAlign:"middle",wordBreak:"normal",[t]:{fontSize:n.fontSize}},[`&${e}-status-active`]:{[`${e}-bg::before`]:{position:"absolute",inset:0,backgroundColor:n.colorBgContainer,borderRadius:n.progressLineRadius,opacity:0,animationName:ct,animationDuration:n.progressActiveMotionDuration,animationTimingFunction:n.motionEaseOutQuint,animationIterationCount:"infinite",content:'""'}},[`&${e}-status-exception`]:{[`${e}-bg`]:{backgroundColor:n.colorError},[`${e}-text`]:{color:n.colorError}},[`&${e}-status-exception ${e}-inner:not(${e}-circle-gradient)`]:{[`${e}-circle-path`]:{stroke:n.colorError}},[`&${e}-status-success`]:{[`${e}-bg`]:{backgroundColor:n.colorSuccess},[`${e}-text`]:{color:n.colorSuccess}},[`&${e}-status-success ${e}-inner:not(${e}-circle-gradient)`]:{[`${e}-circle-path`]:{stroke:n.colorSuccess}}})}},lt=n=>{const{componentCls:e,iconCls:t}=n;return{[e]:{[`${e}-circle-trail`]:{stroke:n.progressRemainingColor},[`&${e}-circle ${e}-inner`]:{position:"relative",lineHeight:1,backgroundColor:"transparent"},[`&${e}-circle ${e}-text`]:{position:"absolute",insetBlockStart:"50%",insetInlineStart:0,width:"100%",margin:0,padding:0,color:n.colorText,lineHeight:1,whiteSpace:"normal",textAlign:"center",transform:"translateY(-50%)",[t]:{fontSize:`${n.fontSize/n.fontSizeSM}em`}},[`${e}-circle&-status-exception`]:{[`${e}-text`]:{color:n.colorError}},[`${e}-circle&-status-success`]:{[`${e}-text`]:{color:n.colorSuccess}}},[`${e}-inline-circle`]:{lineHeight:1,[`${e}-inner`]:{verticalAlign:"bottom"}}}},ht=n=>{const{componentCls:e}=n;return{[e]:{[`${e}-steps`]:{display:"inline-block","&-outer":{display:"flex",flexDirection:"row",alignItems:"center"},"&-item":{flexShrink:0,minWidth:n.progressStepMinWidth,marginInlineEnd:n.progressStepMarginInlineEnd,backgroundColor:n.progressRemainingColor,transition:`all ${n.motionDurationSlow}`,"&-active":{backgroundColor:n.colorInfo}}}}}},ut=n=>{const{componentCls:e,iconCls:t}=n;return{[e]:{[`${e}-small&-line, ${e}-small&-line ${e}-text ${t}`]:{fontSize:n.fontSizeSM}}}},dt=Te("Progress",n=>{const e=n.marginXXS/2,t=Re(n,{progressLineRadius:100,progressInfoTextColor:n.colorText,progressDefaultColor:n.colorInfo,progressRemainingColor:n.colorFillSecondary,progressStepMarginInlineEnd:e,progressStepMinWidth:e,progressActiveMotionDuration:"2.4s"});return[at(t),lt(t),ht(t),ut(t)]});var gt=function(n,e){var t={};for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&e.indexOf(o)<0&&(t[o]=n[o]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(n);r<o.length;r++)e.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(n,o[r])&&(t[o[r]]=n[o[r]]);return t};const ft=z({compatConfig:{MODE:3},name:"AProgress",inheritAttrs:!1,props:oe(G(),{type:"line",percent:0,showInfo:!0,trailColor:null,size:"default",strokeLinecap:"round"}),slots:Object,setup(n,e){let{slots:t,attrs:o}=e;const{prefixCls:r,direction:s}=Ae("progress",n),[i,a]=dt(r),l=p(()=>Array.isArray(n.strokeColor)?n.strokeColor[0]:n.strokeColor),u=p(()=>{const{percent:b=0}=n,f=V(n);return parseInt(f!==void 0?f.toString():b.toString(),10)}),h=p(()=>{const{status:b}=n;return!Ue.includes(b)&&u.value>=100?"success":b||"normal"}),w=p(()=>{const{type:b,showInfo:f,size:P}=n,E=r.value;return{[E]:!0,[`${E}-inline-circle`]:b==="circle"&&Y(P,"circle").width<=20,[`${E}-${b==="dashboard"&&"circle"||b}`]:!0,[`${E}-status-${h.value}`]:!0,[`${E}-show-info`]:f,[`${E}-${P}`]:P,[`${E}-rtl`]:s.value==="rtl",[a.value]:!0}}),m=p(()=>typeof n.strokeColor=="string"||Array.isArray(n.strokeColor)?n.strokeColor:void 0),g=()=>{const{showInfo:b,format:f,type:P,percent:E,title:D}=n,A=V(n);if(!b)return null;let k;const B=f||(t==null?void 0:t.format)||(Ce=>`${Ce}%`),ce=P==="line";return f||t!=null&&t.format||h.value!=="exception"&&h.value!=="success"?k=B(M(E),M(A)):h.value==="exception"?k=ce?_(We,null,null):_(Me,null,null):h.value==="success"&&(k=ce?_(Oe,null,null):_(je,null,null)),_("span",{class:`${r.value}-text`,title:D===void 0&&typeof k=="string"?k:void 0},[k])};return()=>{const{type:b,steps:f,title:P}=n,{class:E}=o,D=gt(o,["class"]),A=g();let k;return b==="line"?k=f?_(it,$($({},n),{},{strokeColor:m.value,prefixCls:r.value,steps:f}),{default:()=>[A]}):_(Je,$($({},n),{},{strokeColor:l.value,prefixCls:r.value,direction:s.value}),{default:()=>[A]}):(b==="circle"||b==="dashboard")&&(k=_(rt,$($({},n),{},{prefixCls:r.value,strokeColor:l.value,progressStatus:h.value}),{default:()=>[A]})),i(_("div",$($({role:"progressbar"},D),{},{class:[w.value,E],title:P}),[k]))}}}),rn=Ne(ft);let R=320;const sn=()=>[{title:"IP地址",dataIndex:"SftpAddress",formItemWidth:R,validateRules:K({type:"ip",title:"IP地址"})},{title:"端口",dataIndex:"SftpPort",formItemWidth:R,validateRules:K({type:"port",title:"端口号"})},{title:"用户名",dataIndex:"SftpUsername",formItemWidth:R,isrequired:!0},{title:"密码",dataIndex:"SftpPassword",formItemWidth:R,isrequired:!0},{title:"叶片数据推送路径",dataIndex:"bvmPushPath",formItemWidth:R,isrequired:!0},{title:"传动链数据推送路径",dataIndex:"cvmPushPath",formItemWidth:R,isrequired:!0},{title:"塔筒数据推送路径",dataIndex:"tvmPushPath",formItemWidth:R,isrequired:!0}],cn=()=>[{title:"对时设置",formList:[{title:"对时服务器IP",dataIndex:"TimeServerIP",formItemWidth:R,validateRules:K({type:"ip",title:"对时服务器IP",required:!0})},{title:"对时服务器端口",dataIndex:"TimeServerPort",formItemWidth:R,validateRules:K({type:"port",title:"对时服务器端口",required:!0})},{title:"录波天数",dataIndex:"recordedDays",formItemWidth:R},{title:"是否开启录波",dataIndex:"enableRecordedDays",formItemWidth:R,inputType:"radio",selectOptions:[{label:"是",value:!0},{label:"否",value:!1}]}]}];class O extends Error{constructor(e,t){const o=new.target.prototype;super(`${e}: Status code '${t}'`),this.statusCode=t,this.__proto__=o}}class re extends Error{constructor(e="A timeout occurred."){const t=new.target.prototype;super(e),this.__proto__=t}}class x extends Error{constructor(e="An abort occurred."){const t=new.target.prototype;super(e),this.__proto__=t}}class pt extends Error{constructor(e,t){const o=new.target.prototype;super(e),this.transport=t,this.errorType="UnsupportedTransportError",this.__proto__=o}}class _t extends Error{constructor(e,t){const o=new.target.prototype;super(e),this.transport=t,this.errorType="DisabledTransportError",this.__proto__=o}}class mt extends Error{constructor(e,t){const o=new.target.prototype;super(e),this.transport=t,this.errorType="FailedToStartTransportError",this.__proto__=o}}class de extends Error{constructor(e){const t=new.target.prototype;super(e),this.errorType="FailedToNegotiateWithServerError",this.__proto__=t}}class bt extends Error{constructor(e,t){const o=new.target.prototype;super(e),this.innerErrors=t,this.__proto__=o}}class we{constructor(e,t,o){this.statusCode=e,this.statusText=t,this.content=o}}class Q{get(e,t){return this.send({...t,method:"GET",url:e})}post(e,t){return this.send({...t,method:"POST",url:e})}delete(e,t){return this.send({...t,method:"DELETE",url:e})}getCookieString(e){return""}}var c;(function(n){n[n.Trace=0]="Trace",n[n.Debug=1]="Debug",n[n.Information=2]="Information",n[n.Warning=3]="Warning",n[n.Error=4]="Error",n[n.Critical=5]="Critical",n[n.None=6]="None"})(c||(c={}));class F{constructor(){}log(e,t){}}F.instance=new F;const wt="9.0.6";class y{static isRequired(e,t){if(e==null)throw new Error(`The '${t}' argument is required.`)}static isNotEmpty(e,t){if(!e||e.match(/^\s*$/))throw new Error(`The '${t}' argument should not be empty.`)}static isIn(e,t,o){if(!(e in t))throw new Error(`Unknown ${o} value: ${e}.`)}}class C{static get isBrowser(){return!C.isNode&&typeof window=="object"&&typeof window.document=="object"}static get isWebWorker(){return!C.isNode&&typeof self=="object"&&"importScripts"in self}static get isReactNative(){return!C.isNode&&typeof window=="object"&&typeof window.document>"u"}static get isNode(){return typeof process<"u"&&process.release&&process.release.name==="node"}}function q(n,e){let t="";return H(n)?(t=`Binary data of length ${n.byteLength}`,e&&(t+=`. Content: '${St(n)}'`)):typeof n=="string"&&(t=`String data of length ${n.length}`,e&&(t+=`. Content: '${n}'`)),t}function St(n){const e=new Uint8Array(n);let t="";return e.forEach(o=>{const r=o<16?"0":"";t+=`0x${r}${o.toString(16)} `}),t.substr(0,t.length-1)}function H(n){return n&&typeof ArrayBuffer<"u"&&(n instanceof ArrayBuffer||n.constructor&&n.constructor.name==="ArrayBuffer")}async function Se(n,e,t,o,r,s){const i={},[a,l]=L();i[a]=l,n.log(c.Trace,`(${e} transport) sending data. ${q(r,s.logMessageContent)}.`);const u=H(r)?"arraybuffer":"text",h=await t.post(o,{content:r,headers:{...i,...s.headers},responseType:u,timeout:s.timeout,withCredentials:s.withCredentials});n.log(c.Trace,`(${e} transport) request complete. Response status: ${h.statusCode}.`)}function Ct(n){return n===void 0?new J(c.Information):n===null?F.instance:n.log!==void 0?n:new J(n)}class yt{constructor(e,t){this._subject=e,this._observer=t}dispose(){const e=this._subject.observers.indexOf(this._observer);e>-1&&this._subject.observers.splice(e,1),this._subject.observers.length===0&&this._subject.cancelCallback&&this._subject.cancelCallback().catch(t=>{})}}class J{constructor(e){this._minLevel=e,this.out=console}log(e,t){if(e>=this._minLevel){const o=`[${new Date().toISOString()}] ${c[e]}: ${t}`;switch(e){case c.Critical:case c.Error:this.out.error(o);break;case c.Warning:this.out.warn(o);break;case c.Information:this.out.info(o);break;default:this.out.log(o);break}}}}function L(){let n="X-SignalR-User-Agent";return C.isNode&&(n="User-Agent"),[n,vt(wt,kt(),Et(),It())]}function vt(n,e,t,o){let r="Microsoft SignalR/";const s=n.split(".");return r+=`${s[0]}.${s[1]}`,r+=` (${n}; `,e&&e!==""?r+=`${e}; `:r+="Unknown OS; ",r+=`${t}`,o?r+=`; ${o}`:r+="; Unknown Runtime Version",r+=")",r}function kt(){if(C.isNode)switch(process.platform){case"win32":return"Windows NT";case"darwin":return"macOS";case"linux":return"Linux";default:return process.platform}else return""}function It(){if(C.isNode)return process.versions.node}function Et(){return C.isNode?"NodeJS":"Browser"}function Z(n){return n.stack?n.stack:n.message?n.message:`${n}`}function Pt(){if(typeof globalThis<"u")return globalThis;if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("could not find global")}class $t extends Q{constructor(e){if(super(),this._logger=e,typeof fetch>"u"||C.isNode){const t=typeof __webpack_require__=="function"?__non_webpack_require__:require;this._jar=new(t("tough-cookie")).CookieJar,typeof fetch>"u"?this._fetchType=t("node-fetch"):this._fetchType=fetch,this._fetchType=t("fetch-cookie")(this._fetchType,this._jar)}else this._fetchType=fetch.bind(Pt());if(typeof AbortController>"u"){const t=typeof __webpack_require__=="function"?__non_webpack_require__:require;this._abortControllerType=t("abort-controller")}else this._abortControllerType=AbortController}async send(e){if(e.abortSignal&&e.abortSignal.aborted)throw new x;if(!e.method)throw new Error("No method defined.");if(!e.url)throw new Error("No url defined.");const t=new this._abortControllerType;let o;e.abortSignal&&(e.abortSignal.onabort=()=>{t.abort(),o=new x});let r=null;if(e.timeout){const l=e.timeout;r=setTimeout(()=>{t.abort(),this._logger.log(c.Warning,"Timeout from HTTP request."),o=new re},l)}e.content===""&&(e.content=void 0),e.content&&(e.headers=e.headers||{},H(e.content)?e.headers["Content-Type"]="application/octet-stream":e.headers["Content-Type"]="text/plain;charset=UTF-8");let s;try{s=await this._fetchType(e.url,{body:e.content,cache:"no-cache",credentials:e.withCredentials===!0?"include":"same-origin",headers:{"X-Requested-With":"XMLHttpRequest",...e.headers},method:e.method,mode:"cors",redirect:"follow",signal:t.signal})}catch(l){throw o||(this._logger.log(c.Warning,`Error from HTTP request. ${l}.`),l)}finally{r&&clearTimeout(r),e.abortSignal&&(e.abortSignal.onabort=null)}if(!s.ok){const l=await ge(s,"text");throw new O(l||s.statusText,s.status)}const a=await ge(s,e.responseType);return new we(s.status,s.statusText,a)}getCookieString(e){let t="";return C.isNode&&this._jar&&this._jar.getCookies(e,(o,r)=>t=r.join("; ")),t}}function ge(n,e){let t;switch(e){case"arraybuffer":t=n.arrayBuffer();break;case"text":t=n.text();break;case"blob":case"document":case"json":throw new Error(`${e} is not supported.`);default:t=n.text();break}return t}class Tt extends Q{constructor(e){super(),this._logger=e}send(e){return e.abortSignal&&e.abortSignal.aborted?Promise.reject(new x):e.method?e.url?new Promise((t,o)=>{const r=new XMLHttpRequest;r.open(e.method,e.url,!0),r.withCredentials=e.withCredentials===void 0?!0:e.withCredentials,r.setRequestHeader("X-Requested-With","XMLHttpRequest"),e.content===""&&(e.content=void 0),e.content&&(H(e.content)?r.setRequestHeader("Content-Type","application/octet-stream"):r.setRequestHeader("Content-Type","text/plain;charset=UTF-8"));const s=e.headers;s&&Object.keys(s).forEach(i=>{r.setRequestHeader(i,s[i])}),e.responseType&&(r.responseType=e.responseType),e.abortSignal&&(e.abortSignal.onabort=()=>{r.abort(),o(new x)}),e.timeout&&(r.timeout=e.timeout),r.onload=()=>{e.abortSignal&&(e.abortSignal.onabort=null),r.status>=200&&r.status<300?t(new we(r.status,r.statusText,r.response||r.responseText)):o(new O(r.response||r.responseText||r.statusText,r.status))},r.onerror=()=>{this._logger.log(c.Warning,`Error from HTTP request. ${r.status}: ${r.statusText}.`),o(new O(r.statusText,r.status))},r.ontimeout=()=>{this._logger.log(c.Warning,"Timeout from HTTP request."),o(new re)},r.send(e.content)}):Promise.reject(new Error("No url defined.")):Promise.reject(new Error("No method defined."))}}class Rt extends Q{constructor(e){if(super(),typeof fetch<"u"||C.isNode)this._httpClient=new $t(e);else if(typeof XMLHttpRequest<"u")this._httpClient=new Tt(e);else throw new Error("No usable HttpClient found.")}send(e){return e.abortSignal&&e.abortSignal.aborted?Promise.reject(new x):e.method?e.url?this._httpClient.send(e):Promise.reject(new Error("No url defined.")):Promise.reject(new Error("No method defined."))}getCookieString(e){return this._httpClient.getCookieString(e)}}class T{static write(e){return`${e}${T.RecordSeparator}`}static parse(e){if(e[e.length-1]!==T.RecordSeparator)throw new Error("Message is incomplete.");const t=e.split(T.RecordSeparator);return t.pop(),t}}T.RecordSeparatorCode=30;T.RecordSeparator=String.fromCharCode(T.RecordSeparatorCode);class xt{writeHandshakeRequest(e){return T.write(JSON.stringify(e))}parseHandshakeResponse(e){let t,o;if(H(e)){const a=new Uint8Array(e),l=a.indexOf(T.RecordSeparatorCode);if(l===-1)throw new Error("Message is incomplete.");const u=l+1;t=String.fromCharCode.apply(null,Array.prototype.slice.call(a.slice(0,u))),o=a.byteLength>u?a.slice(u).buffer:null}else{const a=e,l=a.indexOf(T.RecordSeparator);if(l===-1)throw new Error("Message is incomplete.");const u=l+1;t=a.substring(0,u),o=a.length>u?a.substring(u):null}const r=T.parse(t),s=JSON.parse(r[0]);if(s.type)throw new Error("Expected a handshake response from the server.");return[o,s]}}var d;(function(n){n[n.Invocation=1]="Invocation",n[n.StreamItem=2]="StreamItem",n[n.Completion=3]="Completion",n[n.StreamInvocation=4]="StreamInvocation",n[n.CancelInvocation=5]="CancelInvocation",n[n.Ping=6]="Ping",n[n.Close=7]="Close",n[n.Ack=8]="Ack",n[n.Sequence=9]="Sequence"})(d||(d={}));class Dt{constructor(){this.observers=[]}next(e){for(const t of this.observers)t.next(e)}error(e){for(const t of this.observers)t.error&&t.error(e)}complete(){for(const e of this.observers)e.complete&&e.complete()}subscribe(e){return this.observers.push(e),new yt(this,e)}}class At{constructor(e,t,o){this._bufferSize=1e5,this._messages=[],this._totalMessageCount=0,this._waitForSequenceMessage=!1,this._nextReceivingSequenceId=1,this._latestReceivedSequenceId=0,this._bufferedByteCount=0,this._reconnectInProgress=!1,this._protocol=e,this._connection=t,this._bufferSize=o}async _send(e){const t=this._protocol.writeMessage(e);let o=Promise.resolve();if(this._isInvocationMessage(e)){this._totalMessageCount++;let r=()=>{},s=()=>{};H(t)?this._bufferedByteCount+=t.byteLength:this._bufferedByteCount+=t.length,this._bufferedByteCount>=this._bufferSize&&(o=new Promise((i,a)=>{r=i,s=a})),this._messages.push(new Wt(t,this._totalMessageCount,r,s))}try{this._reconnectInProgress||await this._connection.send(t)}catch{this._disconnected()}await o}_ack(e){let t=-1;for(let o=0;o<this._messages.length;o++){const r=this._messages[o];if(r._id<=e.sequenceId)t=o,H(r._message)?this._bufferedByteCount-=r._message.byteLength:this._bufferedByteCount-=r._message.length,r._resolver();else if(this._bufferedByteCount<this._bufferSize)r._resolver();else break}t!==-1&&(this._messages=this._messages.slice(t+1))}_shouldProcessMessage(e){if(this._waitForSequenceMessage)return e.type!==d.Sequence?!1:(this._waitForSequenceMessage=!1,!0);if(!this._isInvocationMessage(e))return!0;const t=this._nextReceivingSequenceId;return this._nextReceivingSequenceId++,t<=this._latestReceivedSequenceId?(t===this._latestReceivedSequenceId&&this._ackTimer(),!1):(this._latestReceivedSequenceId=t,this._ackTimer(),!0)}_resetSequence(e){if(e.sequenceId>this._nextReceivingSequenceId){this._connection.stop(new Error("Sequence ID greater than amount of messages we've received."));return}this._nextReceivingSequenceId=e.sequenceId}_disconnected(){this._reconnectInProgress=!0,this._waitForSequenceMessage=!0}async _resend(){const e=this._messages.length!==0?this._messages[0]._id:this._totalMessageCount+1;await this._connection.send(this._protocol.writeMessage({type:d.Sequence,sequenceId:e}));const t=this._messages;for(const o of t)await this._connection.send(o._message);this._reconnectInProgress=!1}_dispose(e){e??(e=new Error("Unable to reconnect to server."));for(const t of this._messages)t._rejector(e)}_isInvocationMessage(e){switch(e.type){case d.Invocation:case d.StreamItem:case d.Completion:case d.StreamInvocation:case d.CancelInvocation:return!0;case d.Close:case d.Sequence:case d.Ping:case d.Ack:return!1}}_ackTimer(){this._ackTimerHandle===void 0&&(this._ackTimerHandle=setTimeout(async()=>{try{this._reconnectInProgress||await this._connection.send(this._protocol.writeMessage({type:d.Ack,sequenceId:this._latestReceivedSequenceId}))}catch{}clearTimeout(this._ackTimerHandle),this._ackTimerHandle=void 0},1e3))}}class Wt{constructor(e,t,o,r){this._message=e,this._id=t,this._resolver=o,this._rejector=r}}const Mt=30*1e3,Ot=15*1e3,Nt=1e5;var S;(function(n){n.Disconnected="Disconnected",n.Connecting="Connecting",n.Connected="Connected",n.Disconnecting="Disconnecting",n.Reconnecting="Reconnecting"})(S||(S={}));class se{static create(e,t,o,r,s,i,a){return new se(e,t,o,r,s,i,a)}constructor(e,t,o,r,s,i,a){this._nextKeepAlive=0,this._freezeEventListener=()=>{this._logger.log(c.Warning,"The page is being frozen, this will likely lead to the connection being closed and messages being lost. For more information see the docs at https://learn.microsoft.com/aspnet/core/signalr/javascript-client#bsleep")},y.isRequired(e,"connection"),y.isRequired(t,"logger"),y.isRequired(o,"protocol"),this.serverTimeoutInMilliseconds=s??Mt,this.keepAliveIntervalInMilliseconds=i??Ot,this._statefulReconnectBufferSize=a??Nt,this._logger=t,this._protocol=o,this.connection=e,this._reconnectPolicy=r,this._handshakeProtocol=new xt,this.connection.onreceive=l=>this._processIncomingData(l),this.connection.onclose=l=>this._connectionClosed(l),this._callbacks={},this._methods={},this._closedCallbacks=[],this._reconnectingCallbacks=[],this._reconnectedCallbacks=[],this._invocationId=0,this._receivedHandshakeResponse=!1,this._connectionState=S.Disconnected,this._connectionStarted=!1,this._cachedPingMessage=this._protocol.writeMessage({type:d.Ping})}get state(){return this._connectionState}get connectionId(){return this.connection&&this.connection.connectionId||null}get baseUrl(){return this.connection.baseUrl||""}set baseUrl(e){if(this._connectionState!==S.Disconnected&&this._connectionState!==S.Reconnecting)throw new Error("The HubConnection must be in the Disconnected or Reconnecting state to change the url.");if(!e)throw new Error("The HubConnection url must be a valid url.");this.connection.baseUrl=e}start(){return this._startPromise=this._startWithStateTransitions(),this._startPromise}async _startWithStateTransitions(){if(this._connectionState!==S.Disconnected)return Promise.reject(new Error("Cannot start a HubConnection that is not in the 'Disconnected' state."));this._connectionState=S.Connecting,this._logger.log(c.Debug,"Starting HubConnection.");try{await this._startInternal(),C.isBrowser&&window.document.addEventListener("freeze",this._freezeEventListener),this._connectionState=S.Connected,this._connectionStarted=!0,this._logger.log(c.Debug,"HubConnection connected successfully.")}catch(e){return this._connectionState=S.Disconnected,this._logger.log(c.Debug,`HubConnection failed to start successfully because of error '${e}'.`),Promise.reject(e)}}async _startInternal(){this._stopDuringStartError=void 0,this._receivedHandshakeResponse=!1;const e=new Promise((t,o)=>{this._handshakeResolver=t,this._handshakeRejecter=o});await this.connection.start(this._protocol.transferFormat);try{let t=this._protocol.version;this.connection.features.reconnect||(t=1);const o={protocol:this._protocol.name,version:t};if(this._logger.log(c.Debug,"Sending handshake request."),await this._sendMessage(this._handshakeProtocol.writeHandshakeRequest(o)),this._logger.log(c.Information,`Using HubProtocol '${this._protocol.name}'.`),this._cleanupTimeout(),this._resetTimeoutPeriod(),this._resetKeepAliveInterval(),await e,this._stopDuringStartError)throw this._stopDuringStartError;(this.connection.features.reconnect||!1)&&(this._messageBuffer=new At(this._protocol,this.connection,this._statefulReconnectBufferSize),this.connection.features.disconnected=this._messageBuffer._disconnected.bind(this._messageBuffer),this.connection.features.resend=()=>{if(this._messageBuffer)return this._messageBuffer._resend()}),this.connection.features.inherentKeepAlive||await this._sendMessage(this._cachedPingMessage)}catch(t){throw this._logger.log(c.Debug,`Hub handshake failed with error '${t}' during start(). Stopping HubConnection.`),this._cleanupTimeout(),this._cleanupPingTimer(),await this.connection.stop(t),t}}async stop(){const e=this._startPromise;this.connection.features.reconnect=!1,this._stopPromise=this._stopInternal(),await this._stopPromise;try{await e}catch{}}_stopInternal(e){if(this._connectionState===S.Disconnected)return this._logger.log(c.Debug,`Call to HubConnection.stop(${e}) ignored because it is already in the disconnected state.`),Promise.resolve();if(this._connectionState===S.Disconnecting)return this._logger.log(c.Debug,`Call to HttpConnection.stop(${e}) ignored because the connection is already in the disconnecting state.`),this._stopPromise;const t=this._connectionState;return this._connectionState=S.Disconnecting,this._logger.log(c.Debug,"Stopping HubConnection."),this._reconnectDelayHandle?(this._logger.log(c.Debug,"Connection stopped during reconnect delay. Done reconnecting."),clearTimeout(this._reconnectDelayHandle),this._reconnectDelayHandle=void 0,this._completeClose(),Promise.resolve()):(t===S.Connected&&this._sendCloseMessage(),this._cleanupTimeout(),this._cleanupPingTimer(),this._stopDuringStartError=e||new x("The connection was stopped before the hub handshake could complete."),this.connection.stop(e))}async _sendCloseMessage(){try{await this._sendWithProtocol(this._createCloseMessage())}catch{}}stream(e,...t){const[o,r]=this._replaceStreamingParams(t),s=this._createStreamInvocation(e,t,r);let i;const a=new Dt;return a.cancelCallback=()=>{const l=this._createCancelInvocation(s.invocationId);return delete this._callbacks[s.invocationId],i.then(()=>this._sendWithProtocol(l))},this._callbacks[s.invocationId]=(l,u)=>{if(u){a.error(u);return}else l&&(l.type===d.Completion?l.error?a.error(new Error(l.error)):a.complete():a.next(l.item))},i=this._sendWithProtocol(s).catch(l=>{a.error(l),delete this._callbacks[s.invocationId]}),this._launchStreams(o,i),a}_sendMessage(e){return this._resetKeepAliveInterval(),this.connection.send(e)}_sendWithProtocol(e){return this._messageBuffer?this._messageBuffer._send(e):this._sendMessage(this._protocol.writeMessage(e))}send(e,...t){const[o,r]=this._replaceStreamingParams(t),s=this._sendWithProtocol(this._createInvocation(e,t,!0,r));return this._launchStreams(o,s),s}invoke(e,...t){const[o,r]=this._replaceStreamingParams(t),s=this._createInvocation(e,t,!1,r);return new Promise((a,l)=>{this._callbacks[s.invocationId]=(h,w)=>{if(w){l(w);return}else h&&(h.type===d.Completion?h.error?l(new Error(h.error)):a(h.result):l(new Error(`Unexpected message type: ${h.type}`)))};const u=this._sendWithProtocol(s).catch(h=>{l(h),delete this._callbacks[s.invocationId]});this._launchStreams(o,u)})}on(e,t){!e||!t||(e=e.toLowerCase(),this._methods[e]||(this._methods[e]=[]),this._methods[e].indexOf(t)===-1&&this._methods[e].push(t))}off(e,t){if(!e)return;e=e.toLowerCase();const o=this._methods[e];if(o)if(t){const r=o.indexOf(t);r!==-1&&(o.splice(r,1),o.length===0&&delete this._methods[e])}else delete this._methods[e]}onclose(e){e&&this._closedCallbacks.push(e)}onreconnecting(e){e&&this._reconnectingCallbacks.push(e)}onreconnected(e){e&&this._reconnectedCallbacks.push(e)}_processIncomingData(e){if(this._cleanupTimeout(),this._receivedHandshakeResponse||(e=this._processHandshakeResponse(e),this._receivedHandshakeResponse=!0),e){const t=this._protocol.parseMessages(e,this._logger);for(const o of t)if(!(this._messageBuffer&&!this._messageBuffer._shouldProcessMessage(o)))switch(o.type){case d.Invocation:this._invokeClientMethod(o).catch(r=>{this._logger.log(c.Error,`Invoke client method threw error: ${Z(r)}`)});break;case d.StreamItem:case d.Completion:{const r=this._callbacks[o.invocationId];if(r){o.type===d.Completion&&delete this._callbacks[o.invocationId];try{r(o)}catch(s){this._logger.log(c.Error,`Stream callback threw error: ${Z(s)}`)}}break}case d.Ping:break;case d.Close:{this._logger.log(c.Information,"Close message received from server.");const r=o.error?new Error("Server returned an error on close: "+o.error):void 0;o.allowReconnect===!0?this.connection.stop(r):this._stopPromise=this._stopInternal(r);break}case d.Ack:this._messageBuffer&&this._messageBuffer._ack(o);break;case d.Sequence:this._messageBuffer&&this._messageBuffer._resetSequence(o);break;default:this._logger.log(c.Warning,`Invalid message type: ${o.type}.`);break}}this._resetTimeoutPeriod()}_processHandshakeResponse(e){let t,o;try{[o,t]=this._handshakeProtocol.parseHandshakeResponse(e)}catch(r){const s="Error parsing handshake response: "+r;this._logger.log(c.Error,s);const i=new Error(s);throw this._handshakeRejecter(i),i}if(t.error){const r="Server returned handshake error: "+t.error;this._logger.log(c.Error,r);const s=new Error(r);throw this._handshakeRejecter(s),s}else this._logger.log(c.Debug,"Server handshake complete.");return this._handshakeResolver(),o}_resetKeepAliveInterval(){this.connection.features.inherentKeepAlive||(this._nextKeepAlive=new Date().getTime()+this.keepAliveIntervalInMilliseconds,this._cleanupPingTimer())}_resetTimeoutPeriod(){if((!this.connection.features||!this.connection.features.inherentKeepAlive)&&(this._timeoutHandle=setTimeout(()=>this.serverTimeout(),this.serverTimeoutInMilliseconds),this._pingServerHandle===void 0)){let e=this._nextKeepAlive-new Date().getTime();e<0&&(e=0),this._pingServerHandle=setTimeout(async()=>{if(this._connectionState===S.Connected)try{await this._sendMessage(this._cachedPingMessage)}catch{this._cleanupPingTimer()}},e)}}serverTimeout(){this.connection.stop(new Error("Server timeout elapsed without receiving a message from the server."))}async _invokeClientMethod(e){const t=e.target.toLowerCase(),o=this._methods[t];if(!o){this._logger.log(c.Warning,`No client method with the name '${t}' found.`),e.invocationId&&(this._logger.log(c.Warning,`No result given for '${t}' method and invocation ID '${e.invocationId}'.`),await this._sendWithProtocol(this._createCompletionMessage(e.invocationId,"Client didn't provide a result.",null)));return}const r=o.slice(),s=!!e.invocationId;let i,a,l;for(const u of r)try{const h=i;i=await u.apply(this,e.arguments),s&&i&&h&&(this._logger.log(c.Error,`Multiple results provided for '${t}'. Sending error to server.`),l=this._createCompletionMessage(e.invocationId,"Client provided multiple results.",null)),a=void 0}catch(h){a=h,this._logger.log(c.Error,`A callback for the method '${t}' threw error '${h}'.`)}l?await this._sendWithProtocol(l):s?(a?l=this._createCompletionMessage(e.invocationId,`${a}`,null):i!==void 0?l=this._createCompletionMessage(e.invocationId,null,i):(this._logger.log(c.Warning,`No result given for '${t}' method and invocation ID '${e.invocationId}'.`),l=this._createCompletionMessage(e.invocationId,"Client didn't provide a result.",null)),await this._sendWithProtocol(l)):i&&this._logger.log(c.Error,`Result given for '${t}' method but server is not expecting a result.`)}_connectionClosed(e){this._logger.log(c.Debug,`HubConnection.connectionClosed(${e}) called while in state ${this._connectionState}.`),this._stopDuringStartError=this._stopDuringStartError||e||new x("The underlying connection was closed before the hub handshake could complete."),this._handshakeResolver&&this._handshakeResolver(),this._cancelCallbacksWithError(e||new Error("Invocation canceled due to the underlying connection being closed.")),this._cleanupTimeout(),this._cleanupPingTimer(),this._connectionState===S.Disconnecting?this._completeClose(e):this._connectionState===S.Connected&&this._reconnectPolicy?this._reconnect(e):this._connectionState===S.Connected&&this._completeClose(e)}_completeClose(e){if(this._connectionStarted){this._connectionState=S.Disconnected,this._connectionStarted=!1,this._messageBuffer&&(this._messageBuffer._dispose(e??new Error("Connection closed.")),this._messageBuffer=void 0),C.isBrowser&&window.document.removeEventListener("freeze",this._freezeEventListener);try{this._closedCallbacks.forEach(t=>t.apply(this,[e]))}catch(t){this._logger.log(c.Error,`An onclose callback called with error '${e}' threw error '${t}'.`)}}}async _reconnect(e){const t=Date.now();let o=0,r=e!==void 0?e:new Error("Attempting to reconnect due to a unknown error."),s=this._getNextRetryDelay(o++,0,r);if(s===null){this._logger.log(c.Debug,"Connection not reconnecting because the IRetryPolicy returned null on the first reconnect attempt."),this._completeClose(e);return}if(this._connectionState=S.Reconnecting,e?this._logger.log(c.Information,`Connection reconnecting because of error '${e}'.`):this._logger.log(c.Information,"Connection reconnecting."),this._reconnectingCallbacks.length!==0){try{this._reconnectingCallbacks.forEach(i=>i.apply(this,[e]))}catch(i){this._logger.log(c.Error,`An onreconnecting callback called with error '${e}' threw error '${i}'.`)}if(this._connectionState!==S.Reconnecting){this._logger.log(c.Debug,"Connection left the reconnecting state in onreconnecting callback. Done reconnecting.");return}}for(;s!==null;){if(this._logger.log(c.Information,`Reconnect attempt number ${o} will start in ${s} ms.`),await new Promise(i=>{this._reconnectDelayHandle=setTimeout(i,s)}),this._reconnectDelayHandle=void 0,this._connectionState!==S.Reconnecting){this._logger.log(c.Debug,"Connection left the reconnecting state during reconnect delay. Done reconnecting.");return}try{if(await this._startInternal(),this._connectionState=S.Connected,this._logger.log(c.Information,"HubConnection reconnected successfully."),this._reconnectedCallbacks.length!==0)try{this._reconnectedCallbacks.forEach(i=>i.apply(this,[this.connection.connectionId]))}catch(i){this._logger.log(c.Error,`An onreconnected callback called with connectionId '${this.connection.connectionId}; threw error '${i}'.`)}return}catch(i){if(this._logger.log(c.Information,`Reconnect attempt failed because of error '${i}'.`),this._connectionState!==S.Reconnecting){this._logger.log(c.Debug,`Connection moved to the '${this._connectionState}' from the reconnecting state during reconnect attempt. Done reconnecting.`),this._connectionState===S.Disconnecting&&this._completeClose();return}r=i instanceof Error?i:new Error(i.toString()),s=this._getNextRetryDelay(o++,Date.now()-t,r)}}this._logger.log(c.Information,`Reconnect retries have been exhausted after ${Date.now()-t} ms and ${o} failed attempts. Connection disconnecting.`),this._completeClose()}_getNextRetryDelay(e,t,o){try{return this._reconnectPolicy.nextRetryDelayInMilliseconds({elapsedMilliseconds:t,previousRetryCount:e,retryReason:o})}catch(r){return this._logger.log(c.Error,`IRetryPolicy.nextRetryDelayInMilliseconds(${e}, ${t}) threw error '${r}'.`),null}}_cancelCallbacksWithError(e){const t=this._callbacks;this._callbacks={},Object.keys(t).forEach(o=>{const r=t[o];try{r(null,e)}catch(s){this._logger.log(c.Error,`Stream 'error' callback called with '${e}' threw error: ${Z(s)}`)}})}_cleanupPingTimer(){this._pingServerHandle&&(clearTimeout(this._pingServerHandle),this._pingServerHandle=void 0)}_cleanupTimeout(){this._timeoutHandle&&clearTimeout(this._timeoutHandle)}_createInvocation(e,t,o,r){if(o)return r.length!==0?{target:e,arguments:t,streamIds:r,type:d.Invocation}:{target:e,arguments:t,type:d.Invocation};{const s=this._invocationId;return this._invocationId++,r.length!==0?{target:e,arguments:t,invocationId:s.toString(),streamIds:r,type:d.Invocation}:{target:e,arguments:t,invocationId:s.toString(),type:d.Invocation}}}_launchStreams(e,t){if(e.length!==0){t||(t=Promise.resolve());for(const o in e)e[o].subscribe({complete:()=>{t=t.then(()=>this._sendWithProtocol(this._createCompletionMessage(o)))},error:r=>{let s;r instanceof Error?s=r.message:r&&r.toString?s=r.toString():s="Unknown error",t=t.then(()=>this._sendWithProtocol(this._createCompletionMessage(o,s)))},next:r=>{t=t.then(()=>this._sendWithProtocol(this._createStreamItemMessage(o,r)))}})}}_replaceStreamingParams(e){const t=[],o=[];for(let r=0;r<e.length;r++){const s=e[r];if(this._isObservable(s)){const i=this._invocationId;this._invocationId++,t[i]=s,o.push(i.toString()),e.splice(r,1)}}return[t,o]}_isObservable(e){return e&&e.subscribe&&typeof e.subscribe=="function"}_createStreamInvocation(e,t,o){const r=this._invocationId;return this._invocationId++,o.length!==0?{target:e,arguments:t,invocationId:r.toString(),streamIds:o,type:d.StreamInvocation}:{target:e,arguments:t,invocationId:r.toString(),type:d.StreamInvocation}}_createCancelInvocation(e){return{invocationId:e,type:d.CancelInvocation}}_createStreamItemMessage(e,t){return{invocationId:e,item:t,type:d.StreamItem}}_createCompletionMessage(e,t,o){return t?{error:t,invocationId:e,type:d.Completion}:{invocationId:e,result:o,type:d.Completion}}_createCloseMessage(){return{type:d.Close}}}const Ht=[0,2e3,1e4,3e4,null];class fe{constructor(e){this._retryDelays=e!==void 0?[...e,null]:Ht}nextRetryDelayInMilliseconds(e){return this._retryDelays[e.previousRetryCount]}}class N{}N.Authorization="Authorization";N.Cookie="Cookie";class jt extends Q{constructor(e,t){super(),this._innerClient=e,this._accessTokenFactory=t}async send(e){let t=!0;this._accessTokenFactory&&(!this._accessToken||e.url&&e.url.indexOf("/negotiate?")>0)&&(t=!1,this._accessToken=await this._accessTokenFactory()),this._setAuthorizationHeader(e);const o=await this._innerClient.send(e);return t&&o.statusCode===401&&this._accessTokenFactory?(this._accessToken=await this._accessTokenFactory(),this._setAuthorizationHeader(e),await this._innerClient.send(e)):o}_setAuthorizationHeader(e){e.headers||(e.headers={}),this._accessToken?e.headers[N.Authorization]=`Bearer ${this._accessToken}`:this._accessTokenFactory&&e.headers[N.Authorization]&&delete e.headers[N.Authorization]}getCookieString(e){return this._innerClient.getCookieString(e)}}var v;(function(n){n[n.None=0]="None",n[n.WebSockets=1]="WebSockets",n[n.ServerSentEvents=2]="ServerSentEvents",n[n.LongPolling=4]="LongPolling"})(v||(v={}));var I;(function(n){n[n.Text=1]="Text",n[n.Binary=2]="Binary"})(I||(I={}));let Lt=class{constructor(){this._isAborted=!1,this.onabort=null}abort(){this._isAborted||(this._isAborted=!0,this.onabort&&this.onabort())}get signal(){return this}get aborted(){return this._isAborted}};class pe{get pollAborted(){return this._pollAbort.aborted}constructor(e,t,o){this._httpClient=e,this._logger=t,this._pollAbort=new Lt,this._options=o,this._running=!1,this.onreceive=null,this.onclose=null}async connect(e,t){if(y.isRequired(e,"url"),y.isRequired(t,"transferFormat"),y.isIn(t,I,"transferFormat"),this._url=e,this._logger.log(c.Trace,"(LongPolling transport) Connecting."),t===I.Binary&&typeof XMLHttpRequest<"u"&&typeof new XMLHttpRequest().responseType!="string")throw new Error("Binary protocols over XmlHttpRequest not implementing advanced features are not supported.");const[o,r]=L(),s={[o]:r,...this._options.headers},i={abortSignal:this._pollAbort.signal,headers:s,timeout:1e5,withCredentials:this._options.withCredentials};t===I.Binary&&(i.responseType="arraybuffer");const a=`${e}&_=${Date.now()}`;this._logger.log(c.Trace,`(LongPolling transport) polling: ${a}.`);const l=await this._httpClient.get(a,i);l.statusCode!==200?(this._logger.log(c.Error,`(LongPolling transport) Unexpected response code: ${l.statusCode}.`),this._closeError=new O(l.statusText||"",l.statusCode),this._running=!1):this._running=!0,this._receiving=this._poll(this._url,i)}async _poll(e,t){try{for(;this._running;)try{const o=`${e}&_=${Date.now()}`;this._logger.log(c.Trace,`(LongPolling transport) polling: ${o}.`);const r=await this._httpClient.get(o,t);r.statusCode===204?(this._logger.log(c.Information,"(LongPolling transport) Poll terminated by server."),this._running=!1):r.statusCode!==200?(this._logger.log(c.Error,`(LongPolling transport) Unexpected response code: ${r.statusCode}.`),this._closeError=new O(r.statusText||"",r.statusCode),this._running=!1):r.content?(this._logger.log(c.Trace,`(LongPolling transport) data received. ${q(r.content,this._options.logMessageContent)}.`),this.onreceive&&this.onreceive(r.content)):this._logger.log(c.Trace,"(LongPolling transport) Poll timed out, reissuing.")}catch(o){this._running?o instanceof re?this._logger.log(c.Trace,"(LongPolling transport) Poll timed out, reissuing."):(this._closeError=o,this._running=!1):this._logger.log(c.Trace,`(LongPolling transport) Poll errored after shutdown: ${o.message}`)}}finally{this._logger.log(c.Trace,"(LongPolling transport) Polling complete."),this.pollAborted||this._raiseOnClose()}}async send(e){return this._running?Se(this._logger,"LongPolling",this._httpClient,this._url,e,this._options):Promise.reject(new Error("Cannot send until the transport is connected"))}async stop(){this._logger.log(c.Trace,"(LongPolling transport) Stopping polling."),this._running=!1,this._pollAbort.abort();try{await this._receiving,this._logger.log(c.Trace,`(LongPolling transport) sending DELETE request to ${this._url}.`);const e={},[t,o]=L();e[t]=o;const r={headers:{...e,...this._options.headers},timeout:this._options.timeout,withCredentials:this._options.withCredentials};let s;try{await this._httpClient.delete(this._url,r)}catch(i){s=i}s?s instanceof O&&(s.statusCode===404?this._logger.log(c.Trace,"(LongPolling transport) A 404 response was returned from sending a DELETE request."):this._logger.log(c.Trace,`(LongPolling transport) Error sending a DELETE request: ${s}`)):this._logger.log(c.Trace,"(LongPolling transport) DELETE request accepted.")}finally{this._logger.log(c.Trace,"(LongPolling transport) Stop finished."),this._raiseOnClose()}}_raiseOnClose(){if(this.onclose){let e="(LongPolling transport) Firing onclose event.";this._closeError&&(e+=" Error: "+this._closeError),this._logger.log(c.Trace,e),this.onclose(this._closeError)}}}class Bt{constructor(e,t,o,r){this._httpClient=e,this._accessToken=t,this._logger=o,this._options=r,this.onreceive=null,this.onclose=null}async connect(e,t){return y.isRequired(e,"url"),y.isRequired(t,"transferFormat"),y.isIn(t,I,"transferFormat"),this._logger.log(c.Trace,"(SSE transport) Connecting."),this._url=e,this._accessToken&&(e+=(e.indexOf("?")<0?"?":"&")+`access_token=${encodeURIComponent(this._accessToken)}`),new Promise((o,r)=>{let s=!1;if(t!==I.Text){r(new Error("The Server-Sent Events transport only supports the 'Text' transfer format"));return}let i;if(C.isBrowser||C.isWebWorker)i=new this._options.EventSource(e,{withCredentials:this._options.withCredentials});else{const a=this._httpClient.getCookieString(e),l={};l.Cookie=a;const[u,h]=L();l[u]=h,i=new this._options.EventSource(e,{withCredentials:this._options.withCredentials,headers:{...l,...this._options.headers}})}try{i.onmessage=a=>{if(this.onreceive)try{this._logger.log(c.Trace,`(SSE transport) data received. ${q(a.data,this._options.logMessageContent)}.`),this.onreceive(a.data)}catch(l){this._close(l);return}},i.onerror=a=>{s?this._close():r(new Error("EventSource failed to connect. The connection could not be found on the server, either the connection ID is not present on the server, or a proxy is refusing/buffering the connection. If you have multiple servers check that sticky sessions are enabled."))},i.onopen=()=>{this._logger.log(c.Information,`SSE connected to ${this._url}`),this._eventSource=i,s=!0,o()}}catch(a){r(a);return}})}async send(e){return this._eventSource?Se(this._logger,"SSE",this._httpClient,this._url,e,this._options):Promise.reject(new Error("Cannot send until the transport is connected"))}stop(){return this._close(),Promise.resolve()}_close(e){this._eventSource&&(this._eventSource.close(),this._eventSource=void 0,this.onclose&&this.onclose(e))}}class Ut{constructor(e,t,o,r,s,i){this._logger=o,this._accessTokenFactory=t,this._logMessageContent=r,this._webSocketConstructor=s,this._httpClient=e,this.onreceive=null,this.onclose=null,this._headers=i}async connect(e,t){y.isRequired(e,"url"),y.isRequired(t,"transferFormat"),y.isIn(t,I,"transferFormat"),this._logger.log(c.Trace,"(WebSockets transport) Connecting.");let o;return this._accessTokenFactory&&(o=await this._accessTokenFactory()),new Promise((r,s)=>{e=e.replace(/^http/,"ws");let i;const a=this._httpClient.getCookieString(e);let l=!1;if(C.isNode||C.isReactNative){const u={},[h,w]=L();u[h]=w,o&&(u[N.Authorization]=`Bearer ${o}`),a&&(u[N.Cookie]=a),i=new this._webSocketConstructor(e,void 0,{headers:{...u,...this._headers}})}else o&&(e+=(e.indexOf("?")<0?"?":"&")+`access_token=${encodeURIComponent(o)}`);i||(i=new this._webSocketConstructor(e)),t===I.Binary&&(i.binaryType="arraybuffer"),i.onopen=u=>{this._logger.log(c.Information,`WebSocket connected to ${e}.`),this._webSocket=i,l=!0,r()},i.onerror=u=>{let h=null;typeof ErrorEvent<"u"&&u instanceof ErrorEvent?h=u.error:h="There was an error with the transport",this._logger.log(c.Information,`(WebSockets transport) ${h}.`)},i.onmessage=u=>{if(this._logger.log(c.Trace,`(WebSockets transport) data received. ${q(u.data,this._logMessageContent)}.`),this.onreceive)try{this.onreceive(u.data)}catch(h){this._close(h);return}},i.onclose=u=>{if(l)this._close(u);else{let h=null;typeof ErrorEvent<"u"&&u instanceof ErrorEvent?h=u.error:h="WebSocket failed to connect. The connection could not be found on the server, either the endpoint may not be a SignalR endpoint, the connection ID is not present on the server, or there is a proxy blocking WebSockets. If you have multiple servers check that sticky sessions are enabled.",s(new Error(h))}}})}send(e){return this._webSocket&&this._webSocket.readyState===this._webSocketConstructor.OPEN?(this._logger.log(c.Trace,`(WebSockets transport) sending data. ${q(e,this._logMessageContent)}.`),this._webSocket.send(e),Promise.resolve()):Promise.reject("WebSocket is not in the OPEN state")}stop(){return this._webSocket&&this._close(void 0),Promise.resolve()}_close(e){this._webSocket&&(this._webSocket.onclose=()=>{},this._webSocket.onmessage=()=>{},this._webSocket.onerror=()=>{},this._webSocket.close(),this._webSocket=void 0),this._logger.log(c.Trace,"(WebSockets transport) socket closed."),this.onclose&&(this._isCloseEvent(e)&&(e.wasClean===!1||e.code!==1e3)?this.onclose(new Error(`WebSocket closed with status code: ${e.code} (${e.reason||"no reason given"}).`)):e instanceof Error?this.onclose(e):this.onclose())}_isCloseEvent(e){return e&&typeof e.wasClean=="boolean"&&typeof e.code=="number"}}const _e=100;class Ft{constructor(e,t={}){if(this._stopPromiseResolver=()=>{},this.features={},this._negotiateVersion=1,y.isRequired(e,"url"),this._logger=Ct(t.logger),this.baseUrl=this._resolveUrl(e),t=t||{},t.logMessageContent=t.logMessageContent===void 0?!1:t.logMessageContent,typeof t.withCredentials=="boolean"||t.withCredentials===void 0)t.withCredentials=t.withCredentials===void 0?!0:t.withCredentials;else throw new Error("withCredentials option was not a 'boolean' or 'undefined' value");t.timeout=t.timeout===void 0?100*1e3:t.timeout;let o=null,r=null;if(C.isNode&&typeof require<"u"){const s=typeof __webpack_require__=="function"?__non_webpack_require__:require;o=s("ws"),r=s("eventsource")}!C.isNode&&typeof WebSocket<"u"&&!t.WebSocket?t.WebSocket=WebSocket:C.isNode&&!t.WebSocket&&o&&(t.WebSocket=o),!C.isNode&&typeof EventSource<"u"&&!t.EventSource?t.EventSource=EventSource:C.isNode&&!t.EventSource&&typeof r<"u"&&(t.EventSource=r),this._httpClient=new jt(t.httpClient||new Rt(this._logger),t.accessTokenFactory),this._connectionState="Disconnected",this._connectionStarted=!1,this._options=t,this.onreceive=null,this.onclose=null}async start(e){if(e=e||I.Binary,y.isIn(e,I,"transferFormat"),this._logger.log(c.Debug,`Starting connection with transfer format '${I[e]}'.`),this._connectionState!=="Disconnected")return Promise.reject(new Error("Cannot start an HttpConnection that is not in the 'Disconnected' state."));if(this._connectionState="Connecting",this._startInternalPromise=this._startInternal(e),await this._startInternalPromise,this._connectionState==="Disconnecting"){const t="Failed to start the HttpConnection before stop() was called.";return this._logger.log(c.Error,t),await this._stopPromise,Promise.reject(new x(t))}else if(this._connectionState!=="Connected"){const t="HttpConnection.startInternal completed gracefully but didn't enter the connection into the connected state!";return this._logger.log(c.Error,t),Promise.reject(new x(t))}this._connectionStarted=!0}send(e){return this._connectionState!=="Connected"?Promise.reject(new Error("Cannot send data if the connection is not in the 'Connected' State.")):(this._sendQueue||(this._sendQueue=new ie(this.transport)),this._sendQueue.send(e))}async stop(e){if(this._connectionState==="Disconnected")return this._logger.log(c.Debug,`Call to HttpConnection.stop(${e}) ignored because the connection is already in the disconnected state.`),Promise.resolve();if(this._connectionState==="Disconnecting")return this._logger.log(c.Debug,`Call to HttpConnection.stop(${e}) ignored because the connection is already in the disconnecting state.`),this._stopPromise;this._connectionState="Disconnecting",this._stopPromise=new Promise(t=>{this._stopPromiseResolver=t}),await this._stopInternal(e),await this._stopPromise}async _stopInternal(e){this._stopError=e;try{await this._startInternalPromise}catch{}if(this.transport){try{await this.transport.stop()}catch(t){this._logger.log(c.Error,`HttpConnection.transport.stop() threw error '${t}'.`),this._stopConnection()}this.transport=void 0}else this._logger.log(c.Debug,"HttpConnection.transport is undefined in HttpConnection.stop() because start() failed.")}async _startInternal(e){let t=this.baseUrl;this._accessTokenFactory=this._options.accessTokenFactory,this._httpClient._accessTokenFactory=this._accessTokenFactory;try{if(this._options.skipNegotiation)if(this._options.transport===v.WebSockets)this.transport=this._constructTransport(v.WebSockets),await this._startTransport(t,e);else throw new Error("Negotiation can only be skipped when using the WebSocket transport directly.");else{let o=null,r=0;do{if(o=await this._getNegotiationResponse(t),this._connectionState==="Disconnecting"||this._connectionState==="Disconnected")throw new x("The connection was stopped during negotiation.");if(o.error)throw new Error(o.error);if(o.ProtocolVersion)throw new Error("Detected a connection attempt to an ASP.NET SignalR Server. This client only supports connecting to an ASP.NET Core SignalR Server. See https://aka.ms/signalr-core-differences for details.");if(o.url&&(t=o.url),o.accessToken){const s=o.accessToken;this._accessTokenFactory=()=>s,this._httpClient._accessToken=s,this._httpClient._accessTokenFactory=void 0}r++}while(o.url&&r<_e);if(r===_e&&o.url)throw new Error("Negotiate redirection limit exceeded.");await this._createTransport(t,this._options.transport,o,e)}this.transport instanceof pe&&(this.features.inherentKeepAlive=!0),this._connectionState==="Connecting"&&(this._logger.log(c.Debug,"The HttpConnection connected successfully."),this._connectionState="Connected")}catch(o){return this._logger.log(c.Error,"Failed to start the connection: "+o),this._connectionState="Disconnected",this.transport=void 0,this._stopPromiseResolver(),Promise.reject(o)}}async _getNegotiationResponse(e){const t={},[o,r]=L();t[o]=r;const s=this._resolveNegotiateUrl(e);this._logger.log(c.Debug,`Sending negotiation request: ${s}.`);try{const i=await this._httpClient.post(s,{content:"",headers:{...t,...this._options.headers},timeout:this._options.timeout,withCredentials:this._options.withCredentials});if(i.statusCode!==200)return Promise.reject(new Error(`Unexpected status code returned from negotiate '${i.statusCode}'`));const a=JSON.parse(i.content);return(!a.negotiateVersion||a.negotiateVersion<1)&&(a.connectionToken=a.connectionId),a.useStatefulReconnect&&this._options._useStatefulReconnect!==!0?Promise.reject(new de("Client didn't negotiate Stateful Reconnect but the server did.")):a}catch(i){let a="Failed to complete negotiation with the server: "+i;return i instanceof O&&i.statusCode===404&&(a=a+" Either this is not a SignalR endpoint or there is a proxy blocking the connection."),this._logger.log(c.Error,a),Promise.reject(new de(a))}}_createConnectUrl(e,t){return t?e+(e.indexOf("?")===-1?"?":"&")+`id=${t}`:e}async _createTransport(e,t,o,r){let s=this._createConnectUrl(e,o.connectionToken);if(this._isITransport(t)){this._logger.log(c.Debug,"Connection was provided an instance of ITransport, using that directly."),this.transport=t,await this._startTransport(s,r),this.connectionId=o.connectionId;return}const i=[],a=o.availableTransports||[];let l=o;for(const u of a){const h=this._resolveTransportOrError(u,t,r,(l==null?void 0:l.useStatefulReconnect)===!0);if(h instanceof Error)i.push(`${u.transport} failed:`),i.push(h);else if(this._isITransport(h)){if(this.transport=h,!l){try{l=await this._getNegotiationResponse(e)}catch(w){return Promise.reject(w)}s=this._createConnectUrl(e,l.connectionToken)}try{await this._startTransport(s,r),this.connectionId=l.connectionId;return}catch(w){if(this._logger.log(c.Error,`Failed to start the transport '${u.transport}': ${w}`),l=void 0,i.push(new mt(`${u.transport} failed: ${w}`,v[u.transport])),this._connectionState!=="Connecting"){const m="Failed to select transport before stop() was called.";return this._logger.log(c.Debug,m),Promise.reject(new x(m))}}}}return i.length>0?Promise.reject(new bt(`Unable to connect to the server with any of the available transports. ${i.join(" ")}`,i)):Promise.reject(new Error("None of the transports supported by the client are supported by the server."))}_constructTransport(e){switch(e){case v.WebSockets:if(!this._options.WebSocket)throw new Error("'WebSocket' is not supported in your environment.");return new Ut(this._httpClient,this._accessTokenFactory,this._logger,this._options.logMessageContent,this._options.WebSocket,this._options.headers||{});case v.ServerSentEvents:if(!this._options.EventSource)throw new Error("'EventSource' is not supported in your environment.");return new Bt(this._httpClient,this._httpClient._accessToken,this._logger,this._options);case v.LongPolling:return new pe(this._httpClient,this._logger,this._options);default:throw new Error(`Unknown transport: ${e}.`)}}_startTransport(e,t){return this.transport.onreceive=this.onreceive,this.features.reconnect?this.transport.onclose=async o=>{let r=!1;if(this.features.reconnect)try{this.features.disconnected(),await this.transport.connect(e,t),await this.features.resend()}catch{r=!0}else{this._stopConnection(o);return}r&&this._stopConnection(o)}:this.transport.onclose=o=>this._stopConnection(o),this.transport.connect(e,t)}_resolveTransportOrError(e,t,o,r){const s=v[e.transport];if(s==null)return this._logger.log(c.Debug,`Skipping transport '${e.transport}' because it is not supported by this client.`),new Error(`Skipping transport '${e.transport}' because it is not supported by this client.`);if(qt(t,s))if(e.transferFormats.map(a=>I[a]).indexOf(o)>=0){if(s===v.WebSockets&&!this._options.WebSocket||s===v.ServerSentEvents&&!this._options.EventSource)return this._logger.log(c.Debug,`Skipping transport '${v[s]}' because it is not supported in your environment.'`),new pt(`'${v[s]}' is not supported in your environment.`,s);this._logger.log(c.Debug,`Selecting transport '${v[s]}'.`);try{return this.features.reconnect=s===v.WebSockets?r:void 0,this._constructTransport(s)}catch(a){return a}}else return this._logger.log(c.Debug,`Skipping transport '${v[s]}' because it does not support the requested transfer format '${I[o]}'.`),new Error(`'${v[s]}' does not support ${I[o]}.`);else return this._logger.log(c.Debug,`Skipping transport '${v[s]}' because it was disabled by the client.`),new _t(`'${v[s]}' is disabled by the client.`,s)}_isITransport(e){return e&&typeof e=="object"&&"connect"in e}_stopConnection(e){if(this._logger.log(c.Debug,`HttpConnection.stopConnection(${e}) called while in state ${this._connectionState}.`),this.transport=void 0,e=this._stopError||e,this._stopError=void 0,this._connectionState==="Disconnected"){this._logger.log(c.Debug,`Call to HttpConnection.stopConnection(${e}) was ignored because the connection is already in the disconnected state.`);return}if(this._connectionState==="Connecting")throw this._logger.log(c.Warning,`Call to HttpConnection.stopConnection(${e}) was ignored because the connection is still in the connecting state.`),new Error(`HttpConnection.stopConnection(${e}) was called while the connection is still in the connecting state.`);if(this._connectionState==="Disconnecting"&&this._stopPromiseResolver(),e?this._logger.log(c.Error,`Connection disconnected with error '${e}'.`):this._logger.log(c.Information,"Connection disconnected."),this._sendQueue&&(this._sendQueue.stop().catch(t=>{this._logger.log(c.Error,`TransportSendQueue.stop() threw error '${t}'.`)}),this._sendQueue=void 0),this.connectionId=void 0,this._connectionState="Disconnected",this._connectionStarted){this._connectionStarted=!1;try{this.onclose&&this.onclose(e)}catch(t){this._logger.log(c.Error,`HttpConnection.onclose(${e}) threw error '${t}'.`)}}}_resolveUrl(e){if(e.lastIndexOf("https://",0)===0||e.lastIndexOf("http://",0)===0)return e;if(!C.isBrowser)throw new Error(`Cannot resolve '${e}'.`);const t=window.document.createElement("a");return t.href=e,this._logger.log(c.Information,`Normalizing '${e}' to '${t.href}'.`),t.href}_resolveNegotiateUrl(e){const t=new URL(e);t.pathname.endsWith("/")?t.pathname+="negotiate":t.pathname+="/negotiate";const o=new URLSearchParams(t.searchParams);return o.has("negotiateVersion")||o.append("negotiateVersion",this._negotiateVersion.toString()),o.has("useStatefulReconnect")?o.get("useStatefulReconnect")==="true"&&(this._options._useStatefulReconnect=!0):this._options._useStatefulReconnect===!0&&o.append("useStatefulReconnect","true"),t.search=o.toString(),t.toString()}}function qt(n,e){return!n||(e&n)!==0}class ie{constructor(e){this._transport=e,this._buffer=[],this._executing=!0,this._sendBufferedData=new X,this._transportResult=new X,this._sendLoopPromise=this._sendLoop()}send(e){return this._bufferData(e),this._transportResult||(this._transportResult=new X),this._transportResult.promise}stop(){return this._executing=!1,this._sendBufferedData.resolve(),this._sendLoopPromise}_bufferData(e){if(this._buffer.length&&typeof this._buffer[0]!=typeof e)throw new Error(`Expected data to be of type ${typeof this._buffer} but was of type ${typeof e}`);this._buffer.push(e),this._sendBufferedData.resolve()}async _sendLoop(){for(;;){if(await this._sendBufferedData.promise,!this._executing){this._transportResult&&this._transportResult.reject("Connection stopped.");break}this._sendBufferedData=new X;const e=this._transportResult;this._transportResult=void 0;const t=typeof this._buffer[0]=="string"?this._buffer.join(""):ie._concatBuffers(this._buffer);this._buffer.length=0;try{await this._transport.send(t),e.resolve()}catch(o){e.reject(o)}}}static _concatBuffers(e){const t=e.map(s=>s.byteLength).reduce((s,i)=>s+i),o=new Uint8Array(t);let r=0;for(const s of e)o.set(new Uint8Array(s),r),r+=s.byteLength;return o.buffer}}class X{constructor(){this.promise=new Promise((e,t)=>[this._resolver,this._rejecter]=[e,t])}resolve(){this._resolver()}reject(e){this._rejecter(e)}}const zt="json";class Xt{constructor(){this.name=zt,this.version=2,this.transferFormat=I.Text}parseMessages(e,t){if(typeof e!="string")throw new Error("Invalid input for JSON hub protocol. Expected a string.");if(!e)return[];t===null&&(t=F.instance);const o=T.parse(e),r=[];for(const s of o){const i=JSON.parse(s);if(typeof i.type!="number")throw new Error("Invalid payload.");switch(i.type){case d.Invocation:this._isInvocationMessage(i);break;case d.StreamItem:this._isStreamItemMessage(i);break;case d.Completion:this._isCompletionMessage(i);break;case d.Ping:break;case d.Close:break;case d.Ack:this._isAckMessage(i);break;case d.Sequence:this._isSequenceMessage(i);break;default:t.log(c.Information,"Unknown message type '"+i.type+"' ignored.");continue}r.push(i)}return r}writeMessage(e){return T.write(JSON.stringify(e))}_isInvocationMessage(e){this._assertNotEmptyString(e.target,"Invalid payload for Invocation message."),e.invocationId!==void 0&&this._assertNotEmptyString(e.invocationId,"Invalid payload for Invocation message.")}_isStreamItemMessage(e){if(this._assertNotEmptyString(e.invocationId,"Invalid payload for StreamItem message."),e.item===void 0)throw new Error("Invalid payload for StreamItem message.")}_isCompletionMessage(e){if(e.result&&e.error)throw new Error("Invalid payload for Completion message.");!e.result&&e.error&&this._assertNotEmptyString(e.error,"Invalid payload for Completion message."),this._assertNotEmptyString(e.invocationId,"Invalid payload for Completion message.")}_isAckMessage(e){if(typeof e.sequenceId!="number")throw new Error("Invalid SequenceId for Ack message.")}_isSequenceMessage(e){if(typeof e.sequenceId!="number")throw new Error("Invalid SequenceId for Sequence message.")}_assertNotEmptyString(e,t){if(typeof e!="string"||e==="")throw new Error(t)}}const Kt={trace:c.Trace,debug:c.Debug,info:c.Information,information:c.Information,warn:c.Warning,warning:c.Warning,error:c.Error,critical:c.Critical,none:c.None};function Vt(n){const e=Kt[n.toLowerCase()];if(typeof e<"u")return e;throw new Error(`Unknown log level: ${n}`)}class Jt{configureLogging(e){if(y.isRequired(e,"logging"),Gt(e))this.logger=e;else if(typeof e=="string"){const t=Vt(e);this.logger=new J(t)}else this.logger=new J(e);return this}withUrl(e,t){return y.isRequired(e,"url"),y.isNotEmpty(e,"url"),this.url=e,typeof t=="object"?this.httpConnectionOptions={...this.httpConnectionOptions,...t}:this.httpConnectionOptions={...this.httpConnectionOptions,transport:t},this}withHubProtocol(e){return y.isRequired(e,"protocol"),this.protocol=e,this}withAutomaticReconnect(e){if(this.reconnectPolicy)throw new Error("A reconnectPolicy has already been set.");return e?Array.isArray(e)?this.reconnectPolicy=new fe(e):this.reconnectPolicy=e:this.reconnectPolicy=new fe,this}withServerTimeout(e){return y.isRequired(e,"milliseconds"),this._serverTimeoutInMilliseconds=e,this}withKeepAliveInterval(e){return y.isRequired(e,"milliseconds"),this._keepAliveIntervalInMilliseconds=e,this}withStatefulReconnect(e){return this.httpConnectionOptions===void 0&&(this.httpConnectionOptions={}),this.httpConnectionOptions._useStatefulReconnect=!0,this._statefulReconnectBufferSize=e==null?void 0:e.bufferSize,this}build(){const e=this.httpConnectionOptions||{};if(e.logger===void 0&&(e.logger=this.logger),!this.url)throw new Error("The 'HubConnectionBuilder.withUrl' method must be called before building the connection.");const t=new Ft(this.url,e);return se.create(t,this.logger||F.instance,this.protocol||new Xt,this.reconnectPolicy,this._serverTimeoutInMilliseconds,this._keepAliveIntervalInMilliseconds,this._statefulReconnectBufferSize)}}function Gt(n){return n.log!==void 0}class Yt{constructor(){U(this,"startConnection",async e=>{this.connection&&await this.stopConnection(),this.hubUrl=e,this.connection=new Jt().withUrl(this.hubUrl).withAutomaticReconnect().configureLogging(c.Information).build();try{return await this.connection.start(),this.connection.connectionId}catch{return!1}});U(this,"onReceiveMessage",(e,t)=>{this.connection&&this.connection.on(e,t)});U(this,"sendMessage",async(e,...t)=>{if(this.connection)try{await this.connection.invoke(e,...t)}catch{}});U(this,"stopConnection",async()=>{if(this.connection)try{await this.connection.stop()}catch(e){console.error("关闭连接错误:",e)}});this.connection=null,this.hubUrl=""}}const ln=new Yt;export{rn as _,cn as o,sn as p,ln as s};
