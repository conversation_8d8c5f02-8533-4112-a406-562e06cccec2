import{T as s,ce as o,cf as a,cg as c,ch as n,ci as i,cj as h,ck as f,cl as u,cm as p,cn as y}from"./index-sMW2Pm6g.js";import{a as l}from"./tools-DC78Tda0.js";const S=s("jfDeviceManage",{state:()=>({devicelist:[],locationList:[]}),actions:{reset(){this.$reset()},async fetchGetDAUList(r){try{const e=await y(r);return this.devicelist=e,e}catch(e){throw console.error("获取失败:",e),e}},async fetchGetDAUSFTPDATAPath(r){try{const e=await p(r);let t=l(e,!1);return this.locationList=t,t}catch(e){throw console.error("获取失败:",e),e}},async fetchStartAcquisition(r){try{return await u(r)}catch(e){throw console.error("请求失败:",e),e}},async fetchStopAcquisition(r){try{return await f(r)}catch(e){throw console.error("请求失败:",e),e}},async fetchGetWaveFormData(r){try{return await h(r)}catch(e){throw console.error("请求失败:",e),e}},async fetchSetMeasureDefinition(r){try{return await i(r)}catch(e){throw console.error("请求失败:",e),e}},async fetchGetWaveDBFileList(r){try{return await n(r)}catch(e){throw console.error("请求失败:",e),e}},async fetchGetVibData(r){try{return await c(r)}catch(e){throw console.error("请求失败:",e),e}},async fetchJfSetSFTPConfig(r){try{return await a(r)}catch(e){throw console.error("请求失败:",e),e}},async fetchJfSetAdvancedParameters(r){try{return await o(r)}catch(e){throw console.error("请求失败:",e),e}}}});export{S as u};
