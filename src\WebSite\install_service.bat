@echo off
setlocal enabledelayedexpansion

set SERVICE_NAME=WRD_CMSConfigureWeb_Service
set DISPLAY_NAME="WRD_CMSConfigureWeb_Service"
set DESCRIPTION="WTCMS Live WebSite Core 服务"
set EXECUTABLE=%~dp0publish\WTCMSLive.WebSite.Core.exe

echo ========================================
echo Windows 服务安装脚本
echo ========================================

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 错误: 需要管理员权限运行此脚本
    echo 请右键点击此脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)

REM 检查可执行文件是否存在
if not exist "%EXECUTABLE%" (
    echo 错误: 找不到可执行文件: %EXECUTABLE%
    echo 请确保已经发布应用程序到 publish 目录
    pause
    exit /b 1
)

REM 停止并删除现有服务（如果存在）
echo 检查现有服务...
sc query %SERVICE_NAME% >nul 2>&1
if %errorLevel% equ 0 (
    echo 发现现有服务，正在停止...
    net stop %SERVICE_NAME% >nul 2>&1
    echo 正在删除现有服务...
    sc delete %SERVICE_NAME%
    timeout /t 3 >nul
)

echo 正在安装服务...
sc create %SERVICE_NAME% binPath= "\"%EXECUTABLE%\"" DisplayName= %DISPLAY_NAME% start= auto
if %errorLevel% neq 0 (
    echo 错误: 服务创建失败
    pause
    exit /b 1
)

echo 设置服务描述...
sc description %SERVICE_NAME% %DESCRIPTION%

echo 配置服务恢复选项...
sc failure %SERVICE_NAME% reset= 86400 actions= restart/30000/restart/60000/restart/120000

echo 配置服务为延迟自动启动...
sc config %SERVICE_NAME% start= delayed-auto

echo ========================================
echo 服务安装完成！
echo ========================================
echo 服务名称: %SERVICE_NAME%
echo 显示名称: %DISPLAY_NAME%
echo 可执行文件: %EXECUTABLE%
echo.
echo 管理命令:
echo   启动服务: net start %SERVICE_NAME%
echo   停止服务: net stop %SERVICE_NAME%
echo   查看状态: sc query %SERVICE_NAME%
echo   删除服务: sc delete %SERVICE_NAME%
echo.
echo 是否现在启动服务? (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    echo 正在启动服务...
    net start %SERVICE_NAME%
    if %errorLevel% equ 0 (
        echo 服务启动成功！
    ) else (
        echo 服务启动失败，请检查事件日志获取详细信息
    )
)

pause
