import{w as At,S as nn,x as on,y as ln,u as an,p as nt,z as rn,A as sn,o as Et,T as zt,t as un,a as cn,C as Kt,c as ve,D as dn,E as pn,F as mn,m as vn,l as fn}from"./styleChecker-LI4Lr2UF.js";import{cq as R,h as ot,x as gn,cs as Be,K as Ve,L as Oe,_ as y,M as ke,S as bn,H as G,r as V,d5 as $n,O as De,j as $,cu as ie,b as f,I as M,d7 as yn,C as dt,da as et,F as Pe,bh as K,w as U,dK as hn,D as oe,B as pe,A as Sn,dL as Cn,ct as it,d0 as pt,bc as ge,as as we,cY as ze,bj as mt,dd as Rt,bi as fe,bk as Nt,dA as wn,dx as xn,ba as In,bb as On,dM as vt,cr as lt,d1 as Pn,bg as Mn,dk as ft,dl as gt,dN as Dn,s as _e,dO as _n,N as Lt,d9 as bt,dz as $t,dc as Tn}from"./index-sMW2Pm6g.js";import{S as qe,i as Xe,f as Bn,B as Ke,d as Se,w as Ze,c as An}from"./index-Bi-LLAnN.js";import{i as Re,a as yt,s as En,b as zn,c as Kn,d as Rn,O as Ie,K as Nn,e as xe}from"./shallowequal-D09g54zQ.js";function Ln(){}var jn=1/0,Hn=qe&&1/At(new qe([,-0]))[1]==jn?function(e){return new qe(e)}:Ln,Fn=200;function Vn(e,n,t){var o=-1,i=on,r=e.length,d=!0,l=[],u=l;if(r>=Fn){var m=Hn(e);if(m)return At(m);d=!1,i=ln,u=new nn}else u=l;e:for(;++o<r;){var s=e[o],a=s;if(s=s!==0?s:0,d&&a===a){for(var p=u.length;p--;)if(u[p]===a)continue e;l.push(s)}else i(u,a,t)||(u!==l&&u.push(a),l.push(s))}return l}function Je(e){return e&&e.length?Vn(e):[]}function qo(){const e=R({});let n=null;const t=an();return ot(()=>{n=t.value.subscribe(o=>{e.value=o})}),gn(()=>{t.value.unsubscribe(n)}),e}function Zo(e){const n=R();return Be(()=>{n.value=e()},{flush:"sync"}),n}const kn=e=>{const{componentCls:n,popoverBg:t,popoverColor:o,width:i,fontWeightStrong:r,popoverPadding:d,boxShadowSecondary:l,colorTextHeading:u,borderRadiusLG:m,zIndexPopup:s,marginXS:a,colorBgElevated:p}=e;return[{[n]:y(y({},ke(e)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:s,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--antd-arrow-background-color":p,"&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${n}-content`]:{position:"relative"},[`${n}-inner`]:{backgroundColor:t,backgroundClip:"padding-box",borderRadius:m,boxShadow:l,padding:d},[`${n}-title`]:{minWidth:i,marginBottom:a,color:u,fontWeight:r},[`${n}-inner-content`]:{color:o}})},rn(e,{colorBg:"var(--antd-arrow-background-color)"}),{[`${n}-pure`]:{position:"relative",maxWidth:"none",[`${n}-content`]:{display:"inline-block"}}}]},Xn=e=>{const{componentCls:n}=e;return{[n]:sn.map(t=>{const o=e[`${t}-6`];return{[`&${n}-${t}`]:{"--antd-arrow-background-color":o,[`${n}-inner`]:{backgroundColor:o},[`${n}-arrow`]:{background:"transparent"}}}})}},Wn=e=>{const{componentCls:n,lineWidth:t,lineType:o,colorSplit:i,paddingSM:r,controlHeight:d,fontSize:l,lineHeight:u,padding:m}=e,s=d-Math.round(l*u),a=s/2,p=s/2-t,c=m;return{[n]:{[`${n}-inner`]:{padding:0},[`${n}-title`]:{margin:0,padding:`${a}px ${c}px ${p}px`,borderBottom:`${t}px ${o} ${i}`},[`${n}-inner-content`]:{padding:`${r}px ${c}px`}}}},Gn=Ve("Popover",e=>{const{colorBgElevated:n,colorText:t,wireframe:o}=e,i=Oe(e,{popoverBg:n,popoverColor:t,popoverPadding:12});return[kn(i),Xn(i),o&&Wn(i),nt(i,"zoom-big")]},e=>{let{zIndexPopupBase:n}=e;return{zIndexPopup:n+30,width:177}}),Yn=()=>y(y({},cn()),{content:dt(),title:dt()}),Un=G({compatConfig:{MODE:3},name:"APopover",inheritAttrs:!1,props:Xe(Yn(),y(y({},un()),{trigger:"hover",placement:"top",mouseEnterDelay:.1,mouseLeaveDelay:.1})),setup(e,n){let{expose:t,slots:o,attrs:i}=n;const r=V();$n(e.visible===void 0),t({getPopupDomNode:()=>{var p,c;return(c=(p=r.value)===null||p===void 0?void 0:p.getPopupDomNode)===null||c===void 0?void 0:c.call(p)}});const{prefixCls:d,configProvider:l}=De("popover",e),[u,m]=Gn(d),s=$(()=>l.getPrefixCls()),a=()=>{var p,c;const{title:g=et((p=o.title)===null||p===void 0?void 0:p.call(o)),content:C=et((c=o.content)===null||c===void 0?void 0:c.call(o))}=e,v=!!(Array.isArray(g)?g.length:g),h=!!(Array.isArray(C)?C.length:g);return!v&&!h?null:f(Pe,null,[v&&f("div",{class:`${d.value}-title`},[g]),f("div",{class:`${d.value}-inner-content`},[C])])};return()=>{const p=ie(e.overlayClassName,m.value);return u(f(zt,M(M(M({},Et(e,["title","content"])),i),{},{prefixCls:d.value,ref:r,overlayClassName:p,transitionName:yn(s.value,"zoom-big",e.transitionName),"data-popover-inject":!0}),{title:a,default:o.default}))}}}),Jo=bn(Un),$e={adjustX:1,adjustY:1},ye=[0,0],qn={topLeft:{points:["bl","tl"],overflow:$e,offset:[0,-4],targetOffset:ye},topCenter:{points:["bc","tc"],overflow:$e,offset:[0,-4],targetOffset:ye},topRight:{points:["br","tr"],overflow:$e,offset:[0,-4],targetOffset:ye},bottomLeft:{points:["tl","bl"],overflow:$e,offset:[0,4],targetOffset:ye},bottomCenter:{points:["tc","bc"],overflow:$e,offset:[0,4],targetOffset:ye},bottomRight:{points:["tr","br"],overflow:$e,offset:[0,4],targetOffset:ye}};var Zn=function(e,n){var t={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&n.indexOf(o)<0&&(t[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,o=Object.getOwnPropertySymbols(e);i<o.length;i++)n.indexOf(o[i])<0&&Object.prototype.propertyIsEnumerable.call(e,o[i])&&(t[o[i]]=e[o[i]]);return t};const Jn=G({compatConfig:{MODE:3},props:{minOverlayWidthMatchTrigger:{type:Boolean,default:void 0},arrow:{type:Boolean,default:!1},prefixCls:K.string.def("rc-dropdown"),transitionName:String,overlayClassName:K.string.def(""),openClassName:String,animation:K.any,align:K.object,overlayStyle:{type:Object,default:void 0},placement:K.string.def("bottomLeft"),overlay:K.any,trigger:K.oneOfType([K.string,K.arrayOf(K.string)]).def("hover"),alignPoint:{type:Boolean,default:void 0},showAction:K.array,hideAction:K.array,getPopupContainer:Function,visible:{type:Boolean,default:void 0},defaultVisible:{type:Boolean,default:!1},mouseEnterDelay:K.number.def(.15),mouseLeaveDelay:K.number.def(.1)},emits:["visibleChange","overlayClick"],setup(e,n){let{slots:t,emit:o,expose:i}=n;const r=V(!!e.visible);U(()=>e.visible,c=>{c!==void 0&&(r.value=c)});const d=V();i({triggerRef:d});const l=c=>{e.visible===void 0&&(r.value=!1),o("overlayClick",c)},u=c=>{e.visible===void 0&&(r.value=c),o("visibleChange",c)},m=()=>{var c;const g=(c=t.overlay)===null||c===void 0?void 0:c.call(t),C={prefixCls:`${e.prefixCls}-menu`,onClick:l};return f(Pe,{key:hn},[e.arrow&&f("div",{class:`${e.prefixCls}-arrow`},null),ve(g,C,!1)])},s=$(()=>{const{minOverlayWidthMatchTrigger:c=!e.alignPoint}=e;return c}),a=()=>{var c;const g=(c=t.default)===null||c===void 0?void 0:c.call(t);return r.value&&g?ve(g[0],{class:e.openClassName||`${e.prefixCls}-open`},!1):g},p=$(()=>!e.hideAction&&e.trigger.indexOf("contextmenu")!==-1?["click"]:e.hideAction);return()=>{const{prefixCls:c,arrow:g,showAction:C,overlayStyle:v,trigger:h,placement:w,align:_,getPopupContainer:I,transitionName:S,animation:x,overlayClassName:z}=e,T=Zn(e,["prefixCls","arrow","showAction","overlayStyle","trigger","placement","align","getPopupContainer","transitionName","animation","overlayClassName"]);return f(Kt,M(M({},T),{},{prefixCls:c,ref:d,popupClassName:ie(z,{[`${c}-show-arrow`]:g}),popupStyle:v,builtinPlacements:qn,action:h,showAction:C,hideAction:p.value||[],popupPlacement:w,popupAlign:_,popupTransitionName:S,popupAnimation:x,popupVisible:r.value,stretch:s.value?"minWidth":"",onPopupVisibleChange:u,getPopupContainer:I}),{popup:m,default:a})}}}),jt=()=>({arrow:Sn([Boolean,Object]),trigger:{type:[Array,String]},menu:pe(),overlay:K.any,visible:oe(),open:oe(),disabled:oe(),danger:oe(),autofocus:oe(),align:pe(),getPopupContainer:Function,prefixCls:String,transitionName:String,placement:String,overlayClassName:String,overlayStyle:pe(),forceRender:oe(),mouseEnterDelay:Number,mouseLeaveDelay:Number,openClassName:String,minOverlayWidthMatchTrigger:oe(),destroyPopupOnHide:oe(),onVisibleChange:{type:Function},"onUpdate:visible":{type:Function},onOpenChange:{type:Function},"onUpdate:open":{type:Function}}),Qe=Bn(),Qn=()=>y(y({},jt()),{type:Qe.type,size:String,htmlType:Qe.htmlType,href:String,disabled:oe(),prefixCls:String,icon:K.any,title:String,loading:Qe.loading,onClick:Cn()});var eo={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"ellipsis",theme:"outlined"};function ht(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?Object(arguments[n]):{},o=Object.keys(t);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(t).filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable}))),o.forEach(function(i){to(e,i,t[i])})}return e}function to(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}var We=function(n,t){var o=ht({},n,t.attrs);return f(it,ht({},o,{icon:eo}),null)};We.displayName="EllipsisOutlined";We.inheritAttrs=!1;const no=e=>{const{componentCls:n,antCls:t,paddingXS:o,opacityLoading:i}=e;return{[`${n}-button`]:{whiteSpace:"nowrap",[`&${t}-btn-group > ${t}-btn`]:{[`&-loading, &-loading + ${t}-btn`]:{cursor:"default",pointerEvents:"none",opacity:i},[`&:last-child:not(:first-child):not(${t}-btn-icon-only)`]:{paddingInline:o}}}}},oo=e=>{const{componentCls:n,menuCls:t,colorError:o,colorTextLightSolid:i}=e,r=`${t}-item`;return{[`${n}, ${n}-menu-submenu`]:{[`${t} ${r}`]:{[`&${r}-danger:not(${r}-disabled)`]:{color:o,"&:hover":{color:i,backgroundColor:o}}}}}},io=e=>{const{componentCls:n,menuCls:t,zIndexPopup:o,dropdownArrowDistance:i,dropdownArrowOffset:r,sizePopupArrow:d,antCls:l,iconCls:u,motionDurationMid:m,dropdownPaddingVertical:s,fontSize:a,dropdownEdgeChildPadding:p,colorTextDisabled:c,fontSizeIcon:g,controlPaddingHorizontal:C,colorBgElevated:v,boxShadowPopoverArrow:h}=e;return[{[n]:y(y({},ke(e)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:o,display:"block","&::before":{position:"absolute",insetBlock:-i+d/2,zIndex:-9999,opacity:1e-4,content:'""'},[`${n}-wrap`]:{position:"relative",[`${l}-btn > ${u}-down`]:{fontSize:g},[`${u}-down::before`]:{transition:`transform ${m}`}},[`${n}-wrap-open`]:{[`${u}-down::before`]:{transform:"rotate(180deg)"}},"\n        &-hidden,\n        &-menu-hidden,\n        &-menu-submenu-hidden\n      ":{display:"none"},[`
        &-show-arrow${n}-placement-topLeft,
        &-show-arrow${n}-placement-top,
        &-show-arrow${n}-placement-topRight
      `]:{paddingBottom:i},[`
        &-show-arrow${n}-placement-bottomLeft,
        &-show-arrow${n}-placement-bottom,
        &-show-arrow${n}-placement-bottomRight
      `]:{paddingTop:i},[`${n}-arrow`]:y({position:"absolute",zIndex:1,display:"block"},pn(d,e.borderRadiusXS,e.borderRadiusOuter,v,h)),[`
        &-placement-top > ${n}-arrow,
        &-placement-topLeft > ${n}-arrow,
        &-placement-topRight > ${n}-arrow
      `]:{bottom:i,transform:"translateY(100%) rotate(180deg)"},[`&-placement-top > ${n}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(100%) rotate(180deg)"},[`&-placement-topLeft > ${n}-arrow`]:{left:{_skip_check_:!0,value:r}},[`&-placement-topRight > ${n}-arrow`]:{right:{_skip_check_:!0,value:r}},[`
          &-placement-bottom > ${n}-arrow,
          &-placement-bottomLeft > ${n}-arrow,
          &-placement-bottomRight > ${n}-arrow
        `]:{top:i,transform:"translateY(-100%)"},[`&-placement-bottom > ${n}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateY(-100%) translateX(-50%)"},[`&-placement-bottomLeft > ${n}-arrow`]:{left:{_skip_check_:!0,value:r}},[`&-placement-bottomRight > ${n}-arrow`]:{right:{_skip_check_:!0,value:r}},[`&${l}-slide-down-enter${l}-slide-down-enter-active${n}-placement-bottomLeft,
          &${l}-slide-down-appear${l}-slide-down-appear-active${n}-placement-bottomLeft,
          &${l}-slide-down-enter${l}-slide-down-enter-active${n}-placement-bottom,
          &${l}-slide-down-appear${l}-slide-down-appear-active${n}-placement-bottom,
          &${l}-slide-down-enter${l}-slide-down-enter-active${n}-placement-bottomRight,
          &${l}-slide-down-appear${l}-slide-down-appear-active${n}-placement-bottomRight`]:{animationName:Rn},[`&${l}-slide-up-enter${l}-slide-up-enter-active${n}-placement-topLeft,
          &${l}-slide-up-appear${l}-slide-up-appear-active${n}-placement-topLeft,
          &${l}-slide-up-enter${l}-slide-up-enter-active${n}-placement-top,
          &${l}-slide-up-appear${l}-slide-up-appear-active${n}-placement-top,
          &${l}-slide-up-enter${l}-slide-up-enter-active${n}-placement-topRight,
          &${l}-slide-up-appear${l}-slide-up-appear-active${n}-placement-topRight`]:{animationName:Kn},[`&${l}-slide-down-leave${l}-slide-down-leave-active${n}-placement-bottomLeft,
          &${l}-slide-down-leave${l}-slide-down-leave-active${n}-placement-bottom,
          &${l}-slide-down-leave${l}-slide-down-leave-active${n}-placement-bottomRight`]:{animationName:zn},[`&${l}-slide-up-leave${l}-slide-up-leave-active${n}-placement-topLeft,
          &${l}-slide-up-leave${l}-slide-up-leave-active${n}-placement-top,
          &${l}-slide-up-leave${l}-slide-up-leave-active${n}-placement-topRight`]:{animationName:En}})},{[`${n} ${t}`]:{position:"relative",margin:0},[`${t}-submenu-popup`]:{position:"absolute",zIndex:o,background:"transparent",boxShadow:"none",transformOrigin:"0 0","ul,li":{listStyle:"none"},ul:{marginInline:"0.3em"}},[`${n}, ${n}-menu-submenu`]:{[t]:y(y({padding:p,listStyleType:"none",backgroundColor:v,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary},pt(e)),{[`${t}-item-group-title`]:{padding:`${s}px ${C}px`,color:e.colorTextDescription,transition:`all ${m}`},[`${t}-item`]:{position:"relative",display:"flex",alignItems:"center",borderRadius:e.borderRadiusSM},[`${t}-item-icon`]:{minWidth:a,marginInlineEnd:e.marginXS,fontSize:e.fontSizeSM},[`${t}-title-content`]:{flex:"auto","> a":{color:"inherit",transition:`all ${m}`,"&:hover":{color:"inherit"},"&::after":{position:"absolute",inset:0,content:'""'}}},[`${t}-item, ${t}-submenu-title`]:y(y({clear:"both",margin:0,padding:`${s}px ${C}px`,color:e.colorText,fontWeight:"normal",fontSize:a,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${m}`,"&:hover, &-active":{backgroundColor:e.controlItemBgHover}},pt(e)),{"&-selected":{color:e.colorPrimary,backgroundColor:e.controlItemBgActive,"&:hover, &-active":{backgroundColor:e.controlItemBgActiveHover}},"&-disabled":{color:c,cursor:"not-allowed","&:hover":{color:c,backgroundColor:v,cursor:"not-allowed"},a:{pointerEvents:"none"}},"&-divider":{height:1,margin:`${e.marginXXS}px 0`,overflow:"hidden",lineHeight:0,backgroundColor:e.colorSplit},[`${n}-menu-submenu-expand-icon`]:{position:"absolute",insetInlineEnd:e.paddingXS,[`${n}-menu-submenu-arrow-icon`]:{marginInlineEnd:"0 !important",color:e.colorTextDescription,fontSize:g,fontStyle:"normal"}}}),[`${t}-item-group-list`]:{margin:`0 ${e.marginXS}px`,padding:0,listStyle:"none"},[`${t}-submenu-title`]:{paddingInlineEnd:C+e.fontSizeSM},[`${t}-submenu-vertical`]:{position:"relative"},[`${t}-submenu${t}-submenu-disabled ${n}-menu-submenu-title`]:{[`&, ${n}-menu-submenu-arrow-icon`]:{color:c,backgroundColor:v,cursor:"not-allowed"}},[`${t}-submenu-selected ${n}-menu-submenu-title`]:{color:e.colorPrimary}})}},[Re(e,"slide-up"),Re(e,"slide-down"),yt(e,"move-up"),yt(e,"move-down"),nt(e,"zoom-big")]]},Ht=Ve("Dropdown",(e,n)=>{let{rootPrefixCls:t}=n;const{marginXXS:o,sizePopupArrow:i,controlHeight:r,fontSize:d,lineHeight:l,paddingXXS:u,componentCls:m,borderRadiusOuter:s,borderRadiusLG:a}=e,p=(r-d*l)/2,{dropdownArrowOffset:c}=dn({sizePopupArrow:i,contentRadius:a,borderRadiusOuter:s}),g=Oe(e,{menuCls:`${m}-menu`,rootPrefixCls:t,dropdownArrowDistance:i/2+o,dropdownArrowOffset:c,dropdownPaddingVertical:p,dropdownEdgeChildPadding:u});return[io(g),no(g),oo(g)]},e=>({zIndexPopup:e.zIndexPopupBase+50}));var lo=function(e,n){var t={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&n.indexOf(o)<0&&(t[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,o=Object.getOwnPropertySymbols(e);i<o.length;i++)n.indexOf(o[i])<0&&Object.prototype.propertyIsEnumerable.call(e,o[i])&&(t[o[i]]=e[o[i]]);return t};const ao=Ke.Group,Ne=G({compatConfig:{MODE:3},name:"ADropdownButton",inheritAttrs:!1,__ANT_BUTTON:!0,props:Xe(Qn(),{trigger:"hover",placement:"bottomRight",type:"default"}),slots:Object,setup(e,n){let{slots:t,attrs:o,emit:i}=n;const r=p=>{i("update:visible",p),i("visibleChange",p),i("update:open",p),i("openChange",p)},{prefixCls:d,direction:l,getPopupContainer:u}=De("dropdown",e),m=$(()=>`${d.value}-button`),[s,a]=Ht(d);return()=>{var p,c;const g=y(y({},e),o),{type:C="default",disabled:v,danger:h,loading:w,htmlType:_,class:I="",overlay:S=(p=t.overlay)===null||p===void 0?void 0:p.call(t),trigger:x,align:z,open:T,visible:j,onVisibleChange:B,placement:k=l.value==="rtl"?"bottomLeft":"bottomRight",href:X,title:q,icon:ae=((c=t.icon)===null||c===void 0?void 0:c.call(t))||f(We,null,null),mouseEnterDelay:re,mouseLeaveDelay:ee,overlayClassName:te,overlayStyle:se,destroyPopupOnHide:Y,onClick:de,"onUpdate:open":P}=g,N=lo(g,["type","disabled","danger","loading","htmlType","class","overlay","trigger","align","open","visible","onVisibleChange","placement","href","title","icon","mouseEnterDelay","mouseLeaveDelay","overlayClassName","overlayStyle","destroyPopupOnHide","onClick","onUpdate:open"]),W={align:z,disabled:v,trigger:v?[]:x,placement:k,getPopupContainer:u==null?void 0:u.value,onOpenChange:r,mouseEnterDelay:re,mouseLeaveDelay:ee,open:T??j,overlayClassName:te,overlayStyle:se,destroyPopupOnHide:Y},Z=f(Ke,{danger:h,type:C,disabled:v,loading:w,onClick:de,htmlType:_,href:X,title:q},{default:t.default}),J=f(Ke,{danger:h,type:C,icon:ae},null);return s(f(ao,M(M({},N),{},{class:ie(m.value,I,a.value)}),{default:()=>[t.leftButton?t.leftButton({button:Z}):Z,f(he,W,{default:()=>[t.rightButton?t.rightButton({button:J}):J],overlay:()=>S})]}))}}});var ro={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"}}]},name:"right",theme:"outlined"};function St(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?Object(arguments[n]):{},o=Object.keys(t);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(t).filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable}))),o.forEach(function(i){so(e,i,t[i])})}return e}function so(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}var Le=function(n,t){var o=St({},n,t.attrs);return f(it,St({},o,{icon:ro}),null)};Le.displayName="RightOutlined";Le.inheritAttrs=!1;const Ft=Symbol("OverrideContextKey"),Vt=()=>ge(Ft,void 0),uo=e=>{var n,t,o;const{prefixCls:i,mode:r,selectable:d,validator:l,onClick:u,expandIcon:m}=Vt()||{};we(Ft,{prefixCls:$(()=>{var s,a;return(a=(s=e.prefixCls)===null||s===void 0?void 0:s.value)!==null&&a!==void 0?a:i==null?void 0:i.value}),mode:$(()=>{var s,a;return(a=(s=e.mode)===null||s===void 0?void 0:s.value)!==null&&a!==void 0?a:r==null?void 0:r.value}),selectable:$(()=>{var s,a;return(a=(s=e.selectable)===null||s===void 0?void 0:s.value)!==null&&a!==void 0?a:d==null?void 0:d.value}),validator:(n=e.validator)!==null&&n!==void 0?n:l,onClick:(t=e.onClick)!==null&&t!==void 0?t:u,expandIcon:(o=e.expandIcon)!==null&&o!==void 0?o:m==null?void 0:m.value})},he=G({compatConfig:{MODE:3},name:"ADropdown",inheritAttrs:!1,props:Xe(jt(),{mouseEnterDelay:.15,mouseLeaveDelay:.1,placement:"bottomLeft",trigger:"hover"}),slots:Object,setup(e,n){let{slots:t,attrs:o,emit:i}=n;const{prefixCls:r,rootPrefixCls:d,direction:l,getPopupContainer:u}=De("dropdown",e),[m,s]=Ht(r),a=$(()=>{const{placement:v="",transitionName:h}=e;return h!==void 0?h:v.includes("top")?`${d.value}-slide-down`:`${d.value}-slide-up`});uo({prefixCls:$(()=>`${r.value}-menu`),expandIcon:$(()=>f("span",{class:`${r.value}-menu-submenu-arrow`},[f(Le,{class:`${r.value}-menu-submenu-arrow-icon`},null)])),mode:$(()=>"vertical"),selectable:$(()=>!1),onClick:()=>{},validator:v=>{let{mode:h}=v}});const p=()=>{var v,h,w;const _=e.overlay||((v=t.overlay)===null||v===void 0?void 0:v.call(t)),I=Array.isArray(_)?_[0]:_;if(!I)return null;const S=I.props||{};Se(!S.mode||S.mode==="vertical","Dropdown",`mode="${S.mode}" is not supported for Dropdown's Menu.`);const{selectable:x=!1,expandIcon:z=(w=(h=I.children)===null||h===void 0?void 0:h.expandIcon)===null||w===void 0?void 0:w.call(h)}=S,T=typeof z<"u"&&ze(z)?z:f("span",{class:`${r.value}-menu-submenu-arrow`},[f(Le,{class:`${r.value}-menu-submenu-arrow-icon`},null)]);return ze(I)?ve(I,{mode:"vertical",selectable:x,expandIcon:()=>T}):I},c=$(()=>{const v=e.placement;if(!v)return l.value==="rtl"?"bottomRight":"bottomLeft";if(v.includes("Center")){const h=v.slice(0,v.indexOf("Center"));return Se(!v.includes("Center"),"Dropdown",`You are using '${v}' placement in Dropdown, which is deprecated. Try to use '${h}' instead.`),h}return v}),g=$(()=>typeof e.visible=="boolean"?e.visible:e.open),C=v=>{i("update:visible",v),i("visibleChange",v),i("update:open",v),i("openChange",v)};return()=>{var v,h;const{arrow:w,trigger:_,disabled:I,overlayClassName:S}=e,x=(v=t.default)===null||v===void 0?void 0:v.call(t)[0],z=ve(x,y({class:ie((h=x==null?void 0:x.props)===null||h===void 0?void 0:h.class,{[`${r.value}-rtl`]:l.value==="rtl"},`${r.value}-trigger`)},I?{disabled:I}:{})),T=ie(S,s.value,{[`${r.value}-rtl`]:l.value==="rtl"}),j=I?[]:_;let B;j&&j.includes("contextmenu")&&(B=!0);const k=mn({arrowPointAtCenter:typeof w=="object"&&w.pointAtCenter,autoAdjustOverflow:!0}),X=Et(y(y(y({},e),o),{visible:g.value,builtinPlacements:k,overlayClassName:T,arrow:!!w,alignPoint:B,prefixCls:r.value,getPopupContainer:u==null?void 0:u.value,transitionName:a.value,trigger:j,onVisibleChange:C,placement:c.value}),["overlay","onUpdate:visible"]);return m(f(Jn,X,{default:()=>[z],overlay:p}))}}});he.Button=Ne;const kt=Symbol("menuContextKey"),Xt=e=>{we(kt,e)},le=()=>ge(kt),Wt=Symbol("ForceRenderKey"),co=e=>{we(Wt,e)},Gt=()=>ge(Wt,!1),Yt=Symbol("menuFirstLevelContextKey"),Ut=e=>{we(Yt,e)},po=()=>ge(Yt,!0),je=G({compatConfig:{MODE:3},name:"MenuContextProvider",inheritAttrs:!1,props:{mode:{type:String,default:void 0},overflowDisabled:{type:Boolean,default:void 0}},setup(e,n){let{slots:t}=n;const o=le(),i=y({},o);return e.mode!==void 0&&(i.mode=mt(e,"mode")),e.overflowDisabled!==void 0&&(i.overflowDisabled=mt(e,"overflowDisabled")),Xt(i),()=>{var r;return(r=t.default)===null||r===void 0?void 0:r.call(t)}}}),mo=Symbol("siderCollapsed"),Qo=Symbol("siderHookProvider"),Te="$$__vc-menu-more__key",qt=Symbol("KeyPathContext"),at=()=>ge(qt,{parentEventKeys:$(()=>[]),parentKeys:$(()=>[]),parentInfo:{}}),vo=(e,n,t)=>{const{parentEventKeys:o,parentKeys:i}=at(),r=$(()=>[...o.value,e]),d=$(()=>[...i.value,n]);return we(qt,{parentEventKeys:r,parentKeys:d,parentInfo:t}),d},Zt=Symbol("measure"),Ct=G({compatConfig:{MODE:3},setup(e,n){let{slots:t}=n;return we(Zt,!0),()=>{var o;return(o=t.default)===null||o===void 0?void 0:o.call(t)}}}),rt=()=>ge(Zt,!1);function Jt(e){const{mode:n,rtl:t,inlineIndent:o}=le();return $(()=>n.value!=="inline"?null:t.value?{paddingRight:`${e.value*o.value}px`}:{paddingLeft:`${e.value*o.value}px`})}let fo=0;const go=()=>({id:String,role:String,disabled:Boolean,danger:Boolean,title:{type:[String,Boolean],default:void 0},icon:K.any,onMouseenter:Function,onMouseleave:Function,onClick:Function,onKeydown:Function,onFocus:Function,originItemValue:pe()}),Me=G({compatConfig:{MODE:3},name:"AMenuItem",inheritAttrs:!1,props:go(),slots:Object,setup(e,n){let{slots:t,emit:o,attrs:i}=n;const r=Rt(),d=rt(),l=typeof r.vnode.key=="symbol"?String(r.vnode.key):r.vnode.key;Se(typeof r.vnode.key!="symbol","MenuItem",`MenuItem \`:key="${String(l)}"\` not support Symbol type`);const u=`menu_item_${++fo}_$$_${l}`,{parentEventKeys:m,parentKeys:s}=at(),{prefixCls:a,activeKeys:p,disabled:c,changeActiveKeys:g,rtl:C,inlineCollapsed:v,siderCollapsed:h,onItemClick:w,selectedKeys:_,registerMenuInfo:I,unRegisterMenuInfo:S}=le(),x=po(),z=R(!1),T=$(()=>[...s.value,l]);I(u,{eventKey:u,key:l,parentEventKeys:m,parentKeys:s,isLeaf:!0}),fe(()=>{S(u)}),U(p,()=>{z.value=!!p.value.find(P=>P===l)},{immediate:!0});const B=$(()=>c.value||e.disabled),k=$(()=>_.value.includes(l)),X=$(()=>{const P=`${a.value}-item`;return{[`${P}`]:!0,[`${P}-danger`]:e.danger,[`${P}-active`]:z.value,[`${P}-selected`]:k.value,[`${P}-disabled`]:B.value}}),q=P=>({key:l,eventKey:u,keyPath:T.value,eventKeyPath:[...m.value,u],domEvent:P,item:y(y({},e),i)}),ae=P=>{if(B.value)return;const N=q(P);o("click",P),w(N)},re=P=>{B.value||(g(T.value),o("mouseenter",P))},ee=P=>{B.value||(g([]),o("mouseleave",P))},te=P=>{if(o("keydown",P),P.which===Nn.ENTER){const N=q(P);o("click",P),w(N)}},se=P=>{g(T.value),o("focus",P)},Y=(P,N)=>{const W=f("span",{class:`${a.value}-title-content`},[N]);return(!P||ze(N)&&N.type==="span")&&N&&v.value&&x&&typeof N=="string"?f("div",{class:`${a.value}-inline-collapsed-noicon`},[N.charAt(0)]):W},de=Jt($(()=>T.value.length));return()=>{var P,N,W,Z,J;if(d)return null;const ne=(P=e.title)!==null&&P!==void 0?P:(N=t.title)===null||N===void 0?void 0:N.call(t),b=Nt((W=t.default)===null||W===void 0?void 0:W.call(t)),O=b.length;let A=ne;typeof ne>"u"?A=x&&O?b:"":ne===!1&&(A="");const L={title:A};!h.value&&!v.value&&(L.title=null,L.open=!1);const H={};e.role==="option"&&(H["aria-selected"]=k.value);const E=(Z=e.icon)!==null&&Z!==void 0?Z:(J=t.icon)===null||J===void 0?void 0:J.call(t,e);return f(zt,M(M({},L),{},{placement:C.value?"left":"right",overlayClassName:`${a.value}-inline-collapsed-tooltip`}),{default:()=>[f(Ie.Item,M(M(M({component:"li"},i),{},{id:e.id,style:y(y({},i.style||{}),de.value),class:[X.value,{[`${i.class}`]:!!i.class,[`${a.value}-item-only-child`]:(E?O+1:O)===1}],role:e.role||"menuitem",tabindex:e.disabled?null:-1,"data-menu-id":l,"aria-disabled":e.disabled},H),{},{onMouseenter:re,onMouseleave:ee,onClick:ae,onKeydown:te,onFocus:se,title:typeof ne=="string"?ne:void 0}),{default:()=>[ve(typeof E=="function"?E(e.originItemValue):E,{class:`${a.value}-item-icon`},!1),Y(E,b)]})]})}}}),ce={adjustX:1,adjustY:1},bo={topLeft:{points:["bl","tl"],overflow:ce,offset:[0,-7]},bottomLeft:{points:["tl","bl"],overflow:ce,offset:[0,7]},leftTop:{points:["tr","tl"],overflow:ce,offset:[-4,0]},rightTop:{points:["tl","tr"],overflow:ce,offset:[4,0]}},$o={topLeft:{points:["bl","tl"],overflow:ce,offset:[0,-7]},bottomLeft:{points:["tl","bl"],overflow:ce,offset:[0,7]},rightTop:{points:["tr","tl"],overflow:ce,offset:[-4,0]},leftTop:{points:["tl","tr"],overflow:ce,offset:[4,0]}},yo={horizontal:"bottomLeft",vertical:"rightTop","vertical-left":"rightTop","vertical-right":"leftTop"},wt=G({compatConfig:{MODE:3},name:"PopupTrigger",inheritAttrs:!1,props:{prefixCls:String,mode:String,visible:Boolean,popupClassName:String,popupOffset:Array,disabled:Boolean,onVisibleChange:Function},slots:Object,emits:["visibleChange"],setup(e,n){let{slots:t,emit:o}=n;const i=R(!1),{getPopupContainer:r,rtl:d,subMenuOpenDelay:l,subMenuCloseDelay:u,builtinPlacements:m,triggerSubMenuAction:s,forceSubMenuRender:a,motion:p,defaultMotions:c,rootClassName:g}=le(),C=Gt(),v=$(()=>d.value?y(y({},$o),m.value):y(y({},bo),m.value)),h=$(()=>yo[e.mode]),w=R();U(()=>e.visible,S=>{Ze.cancel(w.value),w.value=Ze(()=>{i.value=S})},{immediate:!0}),fe(()=>{Ze.cancel(w.value)});const _=S=>{o("visibleChange",S)},I=$(()=>{var S,x;const z=p.value||((S=c.value)===null||S===void 0?void 0:S[e.mode])||((x=c.value)===null||x===void 0?void 0:x.other),T=typeof z=="function"?z():z;return T?wn(T.name,{css:!0}):void 0});return()=>{const{prefixCls:S,popupClassName:x,mode:z,popupOffset:T,disabled:j}=e;return f(Kt,{prefixCls:S,popupClassName:ie(`${S}-popup`,{[`${S}-rtl`]:d.value},x,g.value),stretch:z==="horizontal"?"minWidth":null,getPopupContainer:r.value,builtinPlacements:v.value,popupPlacement:h.value,popupVisible:i.value,popupAlign:T&&{offset:T},action:j?[]:[s.value],mouseEnterDelay:l.value,mouseLeaveDelay:u.value,onPopupVisibleChange:_,forceRender:C||a.value,popupAnimation:I.value},{popup:t.popup,default:t.default})}}}),st=(e,n)=>{let{slots:t,attrs:o}=n;var i;const{prefixCls:r,mode:d}=le();return f("ul",M(M({},o),{},{class:ie(r.value,`${r.value}-sub`,`${r.value}-${d.value==="inline"?"inline":"vertical"}`),"data-menu-list":!0}),[(i=t.default)===null||i===void 0?void 0:i.call(t)])};st.displayName="SubMenuList";const ho=G({compatConfig:{MODE:3},name:"InlineSubMenuList",inheritAttrs:!1,props:{id:String,open:Boolean,keyPath:Array},setup(e,n){let{slots:t}=n;const o=$(()=>"inline"),{motion:i,mode:r,defaultMotions:d}=le(),l=$(()=>r.value===o.value),u=V(!l.value),m=$(()=>l.value?e.open:!1);U(r,()=>{l.value&&(u.value=!1)},{flush:"post"});const s=$(()=>{var a,p;const c=i.value||((a=d.value)===null||a===void 0?void 0:a[o.value])||((p=d.value)===null||p===void 0?void 0:p.other),g=typeof c=="function"?c():c;return y(y({},g),{appear:e.keyPath.length<=1})});return()=>{var a;return u.value?null:f(je,{mode:o.value},{default:()=>[f(xn,s.value,{default:()=>[In(f(st,{id:e.id},{default:()=>[(a=t.default)===null||a===void 0?void 0:a.call(t)]}),[[On,m.value]])]})]})}}});let xt=0;const So=()=>({icon:K.any,title:K.any,disabled:Boolean,level:Number,popupClassName:String,popupOffset:Array,internalPopupClose:Boolean,eventKey:String,expandIcon:Function,theme:String,onMouseenter:Function,onMouseleave:Function,onTitleClick:Function,originItemValue:pe()}),Ce=G({compatConfig:{MODE:3},name:"ASubMenu",inheritAttrs:!1,props:So(),slots:Object,setup(e,n){let{slots:t,attrs:o,emit:i}=n;var r,d;Ut(!1);const l=rt(),u=Rt(),m=typeof u.vnode.key=="symbol"?String(u.vnode.key):u.vnode.key;Se(typeof u.vnode.key!="symbol","SubMenu",`SubMenu \`:key="${String(m)}"\` not support Symbol type`);const s=vt(m)?m:`sub_menu_${++xt}_$$_not_set_key`,a=(r=e.eventKey)!==null&&r!==void 0?r:vt(m)?`sub_menu_${++xt}_$$_${m}`:s,{parentEventKeys:p,parentInfo:c,parentKeys:g}=at(),C=$(()=>[...g.value,s]),v=R([]),h={eventKey:a,key:s,parentEventKeys:p,childrenEventKeys:v,parentKeys:g};(d=c.childrenEventKeys)===null||d===void 0||d.value.push(a),fe(()=>{var D;c.childrenEventKeys&&(c.childrenEventKeys.value=(D=c.childrenEventKeys)===null||D===void 0?void 0:D.value.filter(F=>F!=a))}),vo(a,s,h);const{prefixCls:w,activeKeys:_,disabled:I,changeActiveKeys:S,mode:x,inlineCollapsed:z,openKeys:T,overflowDisabled:j,onOpenChange:B,registerMenuInfo:k,unRegisterMenuInfo:X,selectedSubMenuKeys:q,expandIcon:ae,theme:re}=le(),ee=m!=null,te=!l&&(Gt()||!ee);co(te),(l&&ee||!l&&!ee||te)&&(k(a,h),fe(()=>{X(a)}));const se=$(()=>`${w.value}-submenu`),Y=$(()=>I.value||e.disabled),de=R(),P=R(),N=$(()=>T.value.includes(s)),W=$(()=>!j.value&&N.value),Z=$(()=>q.value.includes(s)),J=R(!1);U(_,()=>{J.value=!!_.value.find(D=>D===s)},{immediate:!0});const ne=D=>{Y.value||(i("titleClick",D,s),x.value==="inline"&&B(s,!N.value))},b=D=>{Y.value||(S(C.value),i("mouseenter",D))},O=D=>{Y.value||(S([]),i("mouseleave",D))},A=Jt($(()=>C.value.length)),L=D=>{x.value!=="inline"&&B(s,D)},H=()=>{S(C.value)},E=a&&`${a}-popup`,Q=$(()=>ie(w.value,`${w.value}-${e.theme||re.value}`,e.popupClassName)),be=(D,F)=>{if(!F)return z.value&&!g.value.length&&D&&typeof D=="string"?f("div",{class:`${w.value}-inline-collapsed-noicon`},[D.charAt(0)]):f("span",{class:`${w.value}-title-content`},[D]);const ue=ze(D)&&D.type==="span";return f(Pe,null,[ve(typeof F=="function"?F(e.originItemValue):F,{class:`${w.value}-item-icon`},!1),ue?D:f("span",{class:`${w.value}-title-content`},[D])])},Ge=$(()=>x.value!=="inline"&&C.value.length>1?"vertical":x.value),en=$(()=>x.value==="horizontal"?"vertical":x.value),tn=$(()=>Ge.value==="horizontal"?"vertical":Ge.value),ut=()=>{var D,F;const ue=se.value,Ye=(D=e.icon)!==null&&D!==void 0?D:(F=t.icon)===null||F===void 0?void 0:F.call(t,e),ct=e.expandIcon||t.expandIcon||ae.value,Ue=be(lt(t,e,"title"),Ye);return f("div",{style:A.value,class:`${ue}-title`,tabindex:Y.value?null:-1,ref:de,title:typeof Ue=="string"?Ue:null,"data-menu-id":s,"aria-expanded":W.value,"aria-haspopup":!0,"aria-controls":E,"aria-disabled":Y.value,onClick:ne,onFocus:H},[Ue,x.value!=="horizontal"&&ct?ct(y(y({},e),{isOpen:W.value})):f("i",{class:`${ue}-arrow`},null)])};return()=>{var D;if(l)return ee?(D=t.default)===null||D===void 0?void 0:D.call(t):null;const F=se.value;let ue=()=>null;if(!j.value&&x.value!=="inline"){const Ye=x.value==="horizontal"?[0,8]:[10,0];ue=()=>f(wt,{mode:Ge.value,prefixCls:F,visible:!e.internalPopupClose&&W.value,popupClassName:Q.value,popupOffset:e.popupOffset||Ye,disabled:Y.value,onVisibleChange:L},{default:()=>[ut()],popup:()=>f(je,{mode:tn.value},{default:()=>[f(st,{id:E,ref:P},{default:t.default})]})})}else ue=()=>f(wt,null,{default:ut});return f(je,{mode:en.value},{default:()=>[f(Ie.Item,M(M({component:"li"},o),{},{role:"none",class:ie(F,`${F}-${x.value}`,o.class,{[`${F}-open`]:W.value,[`${F}-active`]:J.value,[`${F}-selected`]:Z.value,[`${F}-disabled`]:Y.value}),onMouseenter:b,onMouseleave:O,"data-submenu-id":s}),{default:()=>f(Pe,null,[ue(),!j.value&&f(ho,{id:E,open:W.value,keyPath:C.value},{default:t.default})])})]})}}}),Co=()=>({title:K.any,originItemValue:pe()}),He=G({compatConfig:{MODE:3},name:"AMenuItemGroup",inheritAttrs:!1,props:Co(),slots:Object,setup(e,n){let{slots:t,attrs:o}=n;const{prefixCls:i}=le(),r=$(()=>`${i.value}-item-group`),d=rt();return()=>{var l,u;return d?(l=t.default)===null||l===void 0?void 0:l.call(t):f("li",M(M({},o),{},{onClick:m=>m.stopPropagation(),class:r.value}),[f("div",{title:typeof e.title=="string"?e.title:void 0,class:`${r.value}-title`},[lt(t,e,"title")]),f("ul",{class:`${r.value}-list`},[(u=t.default)===null||u===void 0?void 0:u.call(t)])])}}}),wo=()=>({prefixCls:String,dashed:Boolean}),Fe=G({compatConfig:{MODE:3},name:"AMenuDivider",props:wo(),setup(e){const{prefixCls:n}=le(),t=$(()=>({[`${n.value}-item-divider`]:!0,[`${n.value}-item-divider-dashed`]:!!e.dashed}));return()=>f("li",{class:t.value},null)}});var xo=function(e,n){var t={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&n.indexOf(o)<0&&(t[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,o=Object.getOwnPropertySymbols(e);i<o.length;i++)n.indexOf(o[i])<0&&Object.prototype.propertyIsEnumerable.call(e,o[i])&&(t[o[i]]=e[o[i]]);return t};function tt(e,n,t){return(e||[]).map((o,i)=>{if(o&&typeof o=="object"){const r=o,{label:d,children:l,key:u,type:m}=r,s=xo(r,["label","children","key","type"]),a=u??`tmp-${i}`,p=t?t.parentKeys.slice():[],c=[],g={eventKey:a,key:a,parentEventKeys:V(p),parentKeys:V(p),childrenEventKeys:V(c),isLeaf:!1};if(l||m==="group"){if(m==="group"){const v=tt(l,n,t);return f(He,M(M({key:a},s),{},{title:d,originItemValue:o}),{default:()=>[v]})}n.set(a,g),t&&t.childrenEventKeys.push(a);const C=tt(l,n,{childrenEventKeys:c,parentKeys:[].concat(p,a)});return f(Ce,M(M({key:a},s),{},{title:d,originItemValue:o}),{default:()=>[C]})}return m==="divider"?f(Fe,M({key:a},s),null):(g.isLeaf=!0,n.set(a,g),f(Me,M(M({key:a},s),{},{originItemValue:o}),{default:()=>[d]}))}return null}).filter(o=>o)}function Io(e){const n=R([]),t=R(!1),o=R(new Map);return U(()=>e.items,()=>{const i=new Map;t.value=!1,e.items?(t.value=!0,n.value=tt(e.items,i)):n.value=void 0,o.value=i},{immediate:!0,deep:!0}),{itemsNodes:n,store:o,hasItmes:t}}const Oo=e=>{const{componentCls:n,motionDurationSlow:t,menuHorizontalHeight:o,colorSplit:i,lineWidth:r,lineType:d,menuItemPaddingInline:l}=e;return{[`${n}-horizontal`]:{lineHeight:`${o}px`,border:0,borderBottom:`${r}px ${d} ${i}`,boxShadow:"none","&::after":{display:"block",clear:"both",height:0,content:'"\\20"'},[`${n}-item, ${n}-submenu`]:{position:"relative",display:"inline-block",verticalAlign:"bottom",paddingInline:l},[`> ${n}-item:hover,
        > ${n}-item-active,
        > ${n}-submenu ${n}-submenu-title:hover`]:{backgroundColor:"transparent"},[`${n}-item, ${n}-submenu-title`]:{transition:[`border-color ${t}`,`background ${t}`].join(",")},[`${n}-submenu-arrow`]:{display:"none"}}}},Po=e=>{let{componentCls:n,menuArrowOffset:t}=e;return{[`${n}-rtl`]:{direction:"rtl"},[`${n}-submenu-rtl`]:{transformOrigin:"100% 0"},[`${n}-rtl${n}-vertical,
    ${n}-submenu-rtl ${n}-vertical`]:{[`${n}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateY(-${t})`},"&::after":{transform:`rotate(45deg) translateY(${t})`}}}}},It=e=>y({},Pn(e)),Ot=(e,n)=>{const{componentCls:t,colorItemText:o,colorItemTextSelected:i,colorGroupTitle:r,colorItemBg:d,colorSubItemBg:l,colorItemBgSelected:u,colorActiveBarHeight:m,colorActiveBarWidth:s,colorActiveBarBorderSize:a,motionDurationSlow:p,motionEaseInOut:c,motionEaseOut:g,menuItemPaddingInline:C,motionDurationMid:v,colorItemTextHover:h,lineType:w,colorSplit:_,colorItemTextDisabled:I,colorDangerItemText:S,colorDangerItemTextHover:x,colorDangerItemTextSelected:z,colorDangerItemBgActive:T,colorDangerItemBgSelected:j,colorItemBgHover:B,menuSubMenuBg:k,colorItemTextSelectedHorizontal:X,colorItemBgSelectedHorizontal:q}=e;return{[`${t}-${n}`]:{color:o,background:d,[`&${t}-root:focus-visible`]:y({},It(e)),[`${t}-item-group-title`]:{color:r},[`${t}-submenu-selected`]:{[`> ${t}-submenu-title`]:{color:i}},[`${t}-item-disabled, ${t}-submenu-disabled`]:{color:`${I} !important`},[`${t}-item:hover, ${t}-submenu-title:hover`]:{[`&:not(${t}-item-selected):not(${t}-submenu-selected)`]:{color:h}},[`&:not(${t}-horizontal)`]:{[`${t}-item:not(${t}-item-selected)`]:{"&:hover":{backgroundColor:B},"&:active":{backgroundColor:u}},[`${t}-submenu-title`]:{"&:hover":{backgroundColor:B},"&:active":{backgroundColor:u}}},[`${t}-item-danger`]:{color:S,[`&${t}-item:hover`]:{[`&:not(${t}-item-selected):not(${t}-submenu-selected)`]:{color:x}},[`&${t}-item:active`]:{background:T}},[`${t}-item a`]:{"&, &:hover":{color:"inherit"}},[`${t}-item-selected`]:{color:i,[`&${t}-item-danger`]:{color:z},"a, a:hover":{color:"inherit"}},[`& ${t}-item-selected`]:{backgroundColor:u,[`&${t}-item-danger`]:{backgroundColor:j}},[`${t}-item, ${t}-submenu-title`]:{[`&:not(${t}-item-disabled):focus-visible`]:y({},It(e))},[`&${t}-submenu > ${t}`]:{backgroundColor:k},[`&${t}-popup > ${t}`]:{backgroundColor:d},[`&${t}-horizontal`]:y(y({},n==="dark"?{borderBottom:0}:{}),{[`> ${t}-item, > ${t}-submenu`]:{top:a,marginTop:-a,marginBottom:0,borderRadius:0,"&::after":{position:"absolute",insetInline:C,bottom:0,borderBottom:`${m}px solid transparent`,transition:`border-color ${p} ${c}`,content:'""'},"&:hover, &-active, &-open":{"&::after":{borderBottomWidth:m,borderBottomColor:X}},"&-selected":{color:X,backgroundColor:q,"&::after":{borderBottomWidth:m,borderBottomColor:X}}}}),[`&${t}-root`]:{[`&${t}-inline, &${t}-vertical`]:{borderInlineEnd:`${a}px ${w} ${_}`}},[`&${t}-inline`]:{[`${t}-sub${t}-inline`]:{background:l},[`${t}-item, ${t}-submenu-title`]:a&&s?{width:`calc(100% + ${a}px)`}:{},[`${t}-item`]:{position:"relative","&::after":{position:"absolute",insetBlock:0,insetInlineEnd:0,borderInlineEnd:`${s}px solid ${i}`,transform:"scaleY(0.0001)",opacity:0,transition:[`transform ${v} ${g}`,`opacity ${v} ${g}`].join(","),content:'""'},[`&${t}-item-danger`]:{"&::after":{borderInlineEndColor:z}}},[`${t}-selected, ${t}-item-selected`]:{"&::after":{transform:"scaleY(1)",opacity:1,transition:[`transform ${v} ${c}`,`opacity ${v} ${c}`].join(",")}}}}}},Pt=e=>{const{componentCls:n,menuItemHeight:t,itemMarginInline:o,padding:i,menuArrowSize:r,marginXS:d,marginXXS:l}=e,u=i+r+d;return{[`${n}-item`]:{position:"relative"},[`${n}-item, ${n}-submenu-title`]:{height:t,lineHeight:`${t}px`,paddingInline:i,overflow:"hidden",textOverflow:"ellipsis",marginInline:o,marginBlock:l,width:`calc(100% - ${o*2}px)`},[`${n}-submenu`]:{paddingBottom:.02},[`> ${n}-item,
            > ${n}-submenu > ${n}-submenu-title`]:{height:t,lineHeight:`${t}px`},[`${n}-item-group-list ${n}-submenu-title,
            ${n}-submenu-title`]:{paddingInlineEnd:u}}},Mo=e=>{const{componentCls:n,iconCls:t,menuItemHeight:o,colorTextLightSolid:i,dropdownWidth:r,controlHeightLG:d,motionDurationMid:l,motionEaseOut:u,paddingXL:m,fontSizeSM:s,fontSizeLG:a,motionDurationSlow:p,paddingXS:c,boxShadowSecondary:g}=e,C={height:o,lineHeight:`${o}px`,listStylePosition:"inside",listStyleType:"disc"};return[{[n]:{"&-inline, &-vertical":y({[`&${n}-root`]:{boxShadow:"none"}},Pt(e))},[`${n}-submenu-popup`]:{[`${n}-vertical`]:y(y({},Pt(e)),{boxShadow:g})}},{[`${n}-submenu-popup ${n}-vertical${n}-sub`]:{minWidth:r,maxHeight:`calc(100vh - ${d*2.5}px)`,padding:"0",overflow:"hidden",borderInlineEnd:0,"&:not([class*='-active'])":{overflowX:"hidden",overflowY:"auto"}}},{[`${n}-inline`]:{width:"100%",[`&${n}-root`]:{[`${n}-item, ${n}-submenu-title`]:{display:"flex",alignItems:"center",transition:[`border-color ${p}`,`background ${p}`,`padding ${l} ${u}`].join(","),[`> ${n}-title-content`]:{flex:"auto",minWidth:0,overflow:"hidden",textOverflow:"ellipsis"},"> *":{flex:"none"}}},[`${n}-sub${n}-inline`]:{padding:0,border:0,borderRadius:0,boxShadow:"none",[`& > ${n}-submenu > ${n}-submenu-title`]:C,[`& ${n}-item-group-title`]:{paddingInlineStart:m}},[`${n}-item`]:C}},{[`${n}-inline-collapsed`]:{width:o*2,[`&${n}-root`]:{[`${n}-item, ${n}-submenu ${n}-submenu-title`]:{[`> ${n}-inline-collapsed-noicon`]:{fontSize:a,textAlign:"center"}}},[`> ${n}-item,
          > ${n}-item-group > ${n}-item-group-list > ${n}-item,
          > ${n}-item-group > ${n}-item-group-list > ${n}-submenu > ${n}-submenu-title,
          > ${n}-submenu > ${n}-submenu-title`]:{insetInlineStart:0,paddingInline:`calc(50% - ${s}px)`,textOverflow:"clip",[`
            ${n}-submenu-arrow,
            ${n}-submenu-expand-icon
          `]:{opacity:0},[`${n}-item-icon, ${t}`]:{margin:0,fontSize:a,lineHeight:`${o}px`,"+ span":{display:"inline-block",opacity:0}}},[`${n}-item-icon, ${t}`]:{display:"inline-block"},"&-tooltip":{pointerEvents:"none",[`${n}-item-icon, ${t}`]:{display:"none"},"a, a:hover":{color:i}},[`${n}-item-group-title`]:y(y({},Mn),{paddingInline:c})}}]},Mt=e=>{const{componentCls:n,fontSize:t,motionDurationSlow:o,motionDurationMid:i,motionEaseInOut:r,motionEaseOut:d,iconCls:l,controlHeightSM:u}=e;return{[`${n}-item, ${n}-submenu-title`]:{position:"relative",display:"block",margin:0,whiteSpace:"nowrap",cursor:"pointer",transition:[`border-color ${o}`,`background ${o}`,`padding ${o} ${r}`].join(","),[`${n}-item-icon, ${l}`]:{minWidth:t,fontSize:t,transition:[`font-size ${i} ${d}`,`margin ${o} ${r}`,`color ${o}`].join(","),"+ span":{marginInlineStart:u-t,opacity:1,transition:[`opacity ${o} ${r}`,`margin ${o}`,`color ${o}`].join(",")}},[`${n}-item-icon`]:y({},Dn()),[`&${n}-item-only-child`]:{[`> ${l}, > ${n}-item-icon`]:{marginInlineEnd:0}}},[`${n}-item-disabled, ${n}-submenu-disabled`]:{background:"none !important",cursor:"not-allowed","&::after":{borderColor:"transparent !important"},a:{color:"inherit !important"},[`> ${n}-submenu-title`]:{color:"inherit !important",cursor:"not-allowed"}}}},Dt=e=>{const{componentCls:n,motionDurationSlow:t,motionEaseInOut:o,borderRadius:i,menuArrowSize:r,menuArrowOffset:d}=e;return{[`${n}-submenu`]:{"&-expand-icon, &-arrow":{position:"absolute",top:"50%",insetInlineEnd:e.margin,width:r,color:"currentcolor",transform:"translateY(-50%)",transition:`transform ${t} ${o}, opacity ${t}`},"&-arrow":{"&::before, &::after":{position:"absolute",width:r*.6,height:r*.15,backgroundColor:"currentcolor",borderRadius:i,transition:[`background ${t} ${o}`,`transform ${t} ${o}`,`top ${t} ${o}`,`color ${t} ${o}`].join(","),content:'""'},"&::before":{transform:`rotate(45deg) translateY(-${d})`},"&::after":{transform:`rotate(-45deg) translateY(${d})`}}}}},Do=e=>{const{antCls:n,componentCls:t,fontSize:o,motionDurationSlow:i,motionDurationMid:r,motionEaseInOut:d,lineHeight:l,paddingXS:u,padding:m,colorSplit:s,lineWidth:a,zIndexPopup:p,borderRadiusLG:c,radiusSubMenuItem:g,menuArrowSize:C,menuArrowOffset:v,lineType:h,menuPanelMaskInset:w}=e;return[{"":{[`${t}`]:y(y({},gt()),{"&-hidden":{display:"none"}})},[`${t}-submenu-hidden`]:{display:"none"}},{[t]:y(y(y(y(y(y(y({},ke(e)),gt()),{marginBottom:0,paddingInlineStart:0,fontSize:o,lineHeight:0,listStyle:"none",outline:"none",transition:`width ${i} cubic-bezier(0.2, 0, 0, 1) 0s`,"ul, ol":{margin:0,padding:0,listStyle:"none"},"&-overflow":{display:"flex",[`${t}-item`]:{flex:"none"}},[`${t}-item, ${t}-submenu, ${t}-submenu-title`]:{borderRadius:e.radiusItem},[`${t}-item-group-title`]:{padding:`${u}px ${m}px`,fontSize:o,lineHeight:l,transition:`all ${i}`},[`&-horizontal ${t}-submenu`]:{transition:[`border-color ${i} ${d}`,`background ${i} ${d}`].join(",")},[`${t}-submenu, ${t}-submenu-inline`]:{transition:[`border-color ${i} ${d}`,`background ${i} ${d}`,`padding ${r} ${d}`].join(",")},[`${t}-submenu ${t}-sub`]:{cursor:"initial",transition:[`background ${i} ${d}`,`padding ${i} ${d}`].join(",")},[`${t}-title-content`]:{transition:`color ${i}`},[`${t}-item a`]:{"&::before":{position:"absolute",inset:0,backgroundColor:"transparent",content:'""'}},[`${t}-item-divider`]:{overflow:"hidden",lineHeight:0,borderColor:s,borderStyle:h,borderWidth:0,borderTopWidth:a,marginBlock:a,padding:0,"&-dashed":{borderStyle:"dashed"}}}),Mt(e)),{[`${t}-item-group`]:{[`${t}-item-group-list`]:{margin:0,padding:0,[`${t}-item, ${t}-submenu-title`]:{paddingInline:`${o*2}px ${m}px`}}},"&-submenu":{"&-popup":{position:"absolute",zIndex:p,background:"transparent",borderRadius:c,boxShadow:"none",transformOrigin:"0 0","&::before":{position:"absolute",inset:`${w}px 0 0`,zIndex:-1,width:"100%",height:"100%",opacity:0,content:'""'}},"&-placement-rightTop::before":{top:0,insetInlineStart:w},[`> ${t}`]:y(y(y({borderRadius:c},Mt(e)),Dt(e)),{[`${t}-item, ${t}-submenu > ${t}-submenu-title`]:{borderRadius:g},[`${t}-submenu-title::after`]:{transition:`transform ${i} ${d}`}})}}),Dt(e)),{[`&-inline-collapsed ${t}-submenu-arrow,
        &-inline ${t}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateX(${v})`},"&::after":{transform:`rotate(45deg) translateX(-${v})`}},[`${t}-submenu-open${t}-submenu-inline > ${t}-submenu-title > ${t}-submenu-arrow`]:{transform:`translateY(-${C*.2}px)`,"&::after":{transform:`rotate(-45deg) translateX(-${v})`},"&::before":{transform:`rotate(45deg) translateX(${v})`}}})},{[`${n}-layout-header`]:{[t]:{lineHeight:"inherit"}}}]},_o=(e,n)=>Ve("Menu",(o,i)=>{let{overrideComponentToken:r}=i;if((n==null?void 0:n.value)===!1)return[];const{colorBgElevated:d,colorPrimary:l,colorError:u,colorErrorHover:m,colorTextLightSolid:s}=o,{controlHeightLG:a,fontSize:p}=o,c=p/7*5,g=Oe(o,{menuItemHeight:a,menuItemPaddingInline:o.margin,menuArrowSize:c,menuHorizontalHeight:a*1.15,menuArrowOffset:`${c*.25}px`,menuPanelMaskInset:-7,menuSubMenuBg:d}),C=new ft(s).setAlpha(.65).toRgbString(),v=Oe(g,{colorItemText:C,colorItemTextHover:s,colorGroupTitle:C,colorItemTextSelected:s,colorItemBg:"#001529",colorSubItemBg:"#000c17",colorItemBgActive:"transparent",colorItemBgSelected:l,colorActiveBarWidth:0,colorActiveBarHeight:0,colorActiveBarBorderSize:0,colorItemTextDisabled:new ft(s).setAlpha(.25).toRgbString(),colorDangerItemText:u,colorDangerItemTextHover:m,colorDangerItemTextSelected:s,colorDangerItemBgActive:u,colorDangerItemBgSelected:u,menuSubMenuBg:"#001529",colorItemTextSelectedHorizontal:s,colorItemBgSelectedHorizontal:l},y({},r));return[Do(g),Oo(g),Mo(g),Ot(g,"light"),Ot(v,"dark"),Po(g),vn(g),Re(g,"slide-up"),Re(g,"slide-down"),nt(g,"zoom-big")]},o=>{const{colorPrimary:i,colorError:r,colorTextDisabled:d,colorErrorBg:l,colorText:u,colorTextDescription:m,colorBgContainer:s,colorFillAlter:a,colorFillContent:p,lineWidth:c,lineWidthBold:g,controlItemBgActive:C,colorBgTextHover:v}=o;return{dropdownWidth:160,zIndexPopup:o.zIndexPopupBase+50,radiusItem:o.borderRadiusLG,radiusSubMenuItem:o.borderRadiusSM,colorItemText:u,colorItemTextHover:u,colorItemTextHoverHorizontal:i,colorGroupTitle:m,colorItemTextSelected:i,colorItemTextSelectedHorizontal:i,colorItemBg:s,colorItemBgHover:v,colorItemBgActive:p,colorSubItemBg:a,colorItemBgSelected:C,colorItemBgSelectedHorizontal:"transparent",colorActiveBarWidth:0,colorActiveBarHeight:g,colorActiveBarBorderSize:c,colorItemTextDisabled:d,colorDangerItemText:r,colorDangerItemTextHover:r,colorDangerItemTextSelected:r,colorDangerItemBgActive:l,colorDangerItemBgSelected:l,itemMarginInline:o.marginXXS}})(e),To=()=>({id:String,prefixCls:String,items:Array,disabled:Boolean,inlineCollapsed:Boolean,disabledOverflow:Boolean,forceSubMenuRender:Boolean,openKeys:Array,selectedKeys:Array,activeKey:String,selectable:{type:Boolean,default:!0},multiple:{type:Boolean,default:!1},tabindex:{type:[Number,String]},motion:Object,role:String,theme:{type:String,default:"light"},mode:{type:String,default:"vertical"},inlineIndent:{type:Number,default:24},subMenuOpenDelay:{type:Number,default:0},subMenuCloseDelay:{type:Number,default:.1},builtinPlacements:{type:Object},triggerSubMenuAction:{type:String,default:"hover"},getPopupContainer:Function,expandIcon:Function,onOpenChange:Function,onSelect:Function,onDeselect:Function,onClick:[Function,Array],onFocus:Function,onBlur:Function,onMousedown:Function,"onUpdate:openKeys":Function,"onUpdate:selectedKeys":Function,"onUpdate:activeKey":Function}),_t=[],me=G({compatConfig:{MODE:3},name:"AMenu",inheritAttrs:!1,props:To(),slots:Object,setup(e,n){let{slots:t,emit:o,attrs:i}=n;const{direction:r,getPrefixCls:d}=De("menu",e),l=Vt(),u=$(()=>{var b;return d("menu",e.prefixCls||((b=l==null?void 0:l.prefixCls)===null||b===void 0?void 0:b.value))}),[m,s]=_o(u,$(()=>!l)),a=R(new Map),p=ge(mo,V(void 0)),c=$(()=>p.value!==void 0?p.value:e.inlineCollapsed),{itemsNodes:g}=Io(e),C=R(!1);ot(()=>{C.value=!0}),Be(()=>{Se(!(e.inlineCollapsed===!0&&e.mode!=="inline"),"Menu","`inlineCollapsed` should only be used when `mode` is inline."),Se(!(p.value!==void 0&&e.inlineCollapsed===!0),"Menu","`inlineCollapsed` not control Menu under Sider. Should set `collapsed` on Sider instead.")});const v=V([]),h=V([]),w=V({});U(a,()=>{const b={};for(const O of a.value.values())b[O.key]=O;w.value=b},{flush:"post"}),Be(()=>{if(e.activeKey!==void 0){let b=[];const O=e.activeKey?w.value[e.activeKey]:void 0;O&&e.activeKey!==void 0?b=Je([].concat(_e(O.parentKeys),e.activeKey)):b=[],xe(v.value,b)||(v.value=b)}}),U(()=>e.selectedKeys,b=>{b&&(h.value=b.slice())},{immediate:!0,deep:!0});const _=V([]);U([w,h],()=>{let b=[];h.value.forEach(O=>{const A=w.value[O];A&&(b=b.concat(_e(A.parentKeys)))}),b=Je(b),xe(_.value,b)||(_.value=b)},{immediate:!0});const I=b=>{if(e.selectable){const{key:O}=b,A=h.value.includes(O);let L;e.multiple?A?L=h.value.filter(E=>E!==O):L=[...h.value,O]:L=[O];const H=y(y({},b),{selectedKeys:L});xe(L,h.value)||(e.selectedKeys===void 0&&(h.value=L),o("update:selectedKeys",L),A&&e.multiple?o("deselect",H):o("select",H))}B.value!=="inline"&&!e.multiple&&S.value.length&&q(_t)},S=V([]);U(()=>e.openKeys,function(){let b=arguments.length>0&&arguments[0]!==void 0?arguments[0]:S.value;xe(S.value,b)||(S.value=b.slice())},{immediate:!0,deep:!0});let x;const z=b=>{clearTimeout(x),x=setTimeout(()=>{e.activeKey===void 0&&(v.value=b),o("update:activeKey",b[b.length-1])})},T=$(()=>!!e.disabled),j=$(()=>r.value==="rtl"),B=V("vertical"),k=R(!1);Be(()=>{var b;(e.mode==="inline"||e.mode==="vertical")&&c.value?(B.value="vertical",k.value=c.value):(B.value=e.mode,k.value=!1),!((b=l==null?void 0:l.mode)===null||b===void 0)&&b.value&&(B.value=l.mode.value)});const X=$(()=>B.value==="inline"),q=b=>{S.value=b,o("update:openKeys",b),o("openChange",b)},ae=V(S.value),re=R(!1);U(S,()=>{X.value&&(ae.value=S.value)},{immediate:!0}),U(X,()=>{if(!re.value){re.value=!0;return}X.value?S.value=ae.value:q(_t)},{immediate:!0});const ee=$(()=>({[`${u.value}`]:!0,[`${u.value}-root`]:!0,[`${u.value}-${B.value}`]:!0,[`${u.value}-inline-collapsed`]:k.value,[`${u.value}-rtl`]:j.value,[`${u.value}-${e.theme}`]:!0})),te=$(()=>d()),se=$(()=>({horizontal:{name:`${te.value}-slide-up`},inline:fn(`${te.value}-motion-collapse`),other:{name:`${te.value}-zoom-big`}}));Ut(!0);const Y=function(){let b=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];const O=[],A=a.value;return b.forEach(L=>{const{key:H,childrenEventKeys:E}=A.get(L);O.push(H,...Y(_e(E)))}),O},de=b=>{var O;o("click",b),I(b),(O=l==null?void 0:l.onClick)===null||O===void 0||O.call(l)},P=(b,O)=>{var A;const L=((A=w.value[b])===null||A===void 0?void 0:A.childrenEventKeys)||[];let H=S.value.filter(E=>E!==b);if(O)H.push(b);else if(B.value!=="inline"){const E=Y(_e(L));H=Je(H.filter(Q=>!E.includes(Q)))}xe(S,H)||q(H)},N=(b,O)=>{a.value.set(b,O),a.value=new Map(a.value)},W=b=>{a.value.delete(b),a.value=new Map(a.value)},Z=V(0),J=$(()=>{var b;return e.expandIcon||t.expandIcon||!((b=l==null?void 0:l.expandIcon)===null||b===void 0)&&b.value?O=>{let A=e.expandIcon||t.expandIcon;return A=typeof A=="function"?A(O):A,ve(A,{class:`${u.value}-submenu-expand-icon`},!1)}:null});Xt({prefixCls:u,activeKeys:v,openKeys:S,selectedKeys:h,changeActiveKeys:z,disabled:T,rtl:j,mode:B,inlineIndent:$(()=>e.inlineIndent),subMenuCloseDelay:$(()=>e.subMenuCloseDelay),subMenuOpenDelay:$(()=>e.subMenuOpenDelay),builtinPlacements:$(()=>e.builtinPlacements),triggerSubMenuAction:$(()=>e.triggerSubMenuAction),getPopupContainer:$(()=>e.getPopupContainer),inlineCollapsed:k,theme:$(()=>e.theme),siderCollapsed:p,defaultMotions:$(()=>C.value?se.value:null),motion:$(()=>C.value?e.motion:null),overflowDisabled:R(void 0),onOpenChange:P,onItemClick:de,registerMenuInfo:N,unRegisterMenuInfo:W,selectedSubMenuKeys:_,expandIcon:J,forceSubMenuRender:$(()=>e.forceSubMenuRender),rootClassName:s});const ne=()=>{var b;return g.value||Nt((b=t.default)===null||b===void 0?void 0:b.call(t))};return()=>{var b;const O=ne(),A=Z.value>=O.length-1||B.value!=="horizontal"||e.disabledOverflow,L=E=>B.value!=="horizontal"||e.disabledOverflow?E:E.map((Q,be)=>f(je,{key:Q.key,overflowDisabled:be>Z.value},{default:()=>Q})),H=((b=t.overflowedIndicator)===null||b===void 0?void 0:b.call(t))||f(We,null,null);return m(f(Ie,M(M({},i),{},{onMousedown:e.onMousedown,prefixCls:`${u.value}-overflow`,component:"ul",itemComponent:Me,class:[ee.value,i.class,s.value],role:"menu",id:e.id,data:L(O),renderRawItem:E=>E,renderRawRest:E=>{const Q=E.length,be=Q?O.slice(-Q):null;return f(Pe,null,[f(Ce,{eventKey:Te,key:Te,title:H,disabled:A,internalPopupClose:Q===0},{default:()=>be}),f(Ct,null,{default:()=>[f(Ce,{eventKey:Te,key:Te,title:H,disabled:A,internalPopupClose:Q===0},{default:()=>be})]})])},maxCount:B.value!=="horizontal"||e.disabledOverflow?Ie.INVALIDATE:Ie.RESPONSIVE,ssr:"full","data-menu-list":!0,onVisibleChange:E=>{Z.value=E}}),{default:()=>[f(_n,{to:"body"},{default:()=>[f("div",{style:{display:"none"},"aria-hidden":!0},[f(Ct,null,{default:()=>[L(ne())]})])]})]}))}}});me.install=function(e){return e.component(me.name,me),e.component(Me.name,Me),e.component(Ce.name,Ce),e.component(Fe.name,Fe),e.component(He.name,He),e};me.Item=Me;me.Divider=Fe;me.SubMenu=Ce;me.ItemGroup=He;var Bo={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"}}]},name:"left",theme:"outlined"};function Tt(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?Object(arguments[n]):{},o=Object.keys(t);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(t).filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable}))),o.forEach(function(i){Ao(e,i,t[i])})}return e}function Ao(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}var Qt=function(n,t){var o=Tt({},n,t.attrs);return f(it,Tt({},o,{icon:Bo}),null)};Qt.displayName="LeftOutlined";Qt.inheritAttrs=!1;he.Button=Ne;he.install=function(e){return e.component(he.name,he),e.component(Ne.name,Ne),e};function Eo(e,n,t){var o=t||{},i=o.noTrailing,r=i===void 0?!1:i,d=o.noLeading,l=d===void 0?!1:d,u=o.debounceMode,m=u===void 0?void 0:u,s,a=!1,p=0;function c(){s&&clearTimeout(s)}function g(v){var h=v||{},w=h.upcomingOnly,_=w===void 0?!1:w;c(),a=!_}function C(){for(var v=arguments.length,h=new Array(v),w=0;w<v;w++)h[w]=arguments[w];var _=this,I=Date.now()-p;if(a)return;function S(){p=Date.now(),n.apply(_,h)}function x(){s=void 0}!l&&m&&!s&&S(),c(),m===void 0&&I>e?l?(p=Date.now(),r||(s=setTimeout(m?x:S,e))):S():r!==!0&&(s=setTimeout(m?x:S,m===void 0?e-I:e))}return C.cancel=g,C}function zo(e,n,t){var o={},i=o.atBegin,r=i===void 0?!1:i;return Eo(e,n,{debounceMode:r!==!1})}const Ko=new Lt("antSpinMove",{to:{opacity:1}}),Ro=new Lt("antRotate",{to:{transform:"rotate(405deg)"}}),No=e=>({[`${e.componentCls}`]:y(y({},ke(e)),{position:"absolute",display:"none",color:e.colorPrimary,textAlign:"center",verticalAlign:"middle",opacity:0,transition:`transform ${e.motionDurationSlow} ${e.motionEaseInOutCirc}`,"&-spinning":{position:"static",display:"inline-block",opacity:1},"&-nested-loading":{position:"relative",[`> div > ${e.componentCls}`]:{position:"absolute",top:0,insetInlineStart:0,zIndex:4,display:"block",width:"100%",height:"100%",maxHeight:e.contentHeight,[`${e.componentCls}-dot`]:{position:"absolute",top:"50%",insetInlineStart:"50%",margin:-e.spinDotSize/2},[`${e.componentCls}-text`]:{position:"absolute",top:"50%",width:"100%",paddingTop:(e.spinDotSize-e.fontSize)/2+2,textShadow:`0 1px 2px ${e.colorBgContainer}`},[`&${e.componentCls}-show-text ${e.componentCls}-dot`]:{marginTop:-(e.spinDotSize/2)-10},"&-sm":{[`${e.componentCls}-dot`]:{margin:-e.spinDotSizeSM/2},[`${e.componentCls}-text`]:{paddingTop:(e.spinDotSizeSM-e.fontSize)/2+2},[`&${e.componentCls}-show-text ${e.componentCls}-dot`]:{marginTop:-(e.spinDotSizeSM/2)-10}},"&-lg":{[`${e.componentCls}-dot`]:{margin:-(e.spinDotSizeLG/2)},[`${e.componentCls}-text`]:{paddingTop:(e.spinDotSizeLG-e.fontSize)/2+2},[`&${e.componentCls}-show-text ${e.componentCls}-dot`]:{marginTop:-(e.spinDotSizeLG/2)-10}}},[`${e.componentCls}-container`]:{position:"relative",transition:`opacity ${e.motionDurationSlow}`,"&::after":{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,zIndex:10,width:"100%",height:"100%",background:e.colorBgContainer,opacity:0,transition:`all ${e.motionDurationSlow}`,content:'""',pointerEvents:"none"}},[`${e.componentCls}-blur`]:{clear:"both",opacity:.5,userSelect:"none",pointerEvents:"none","&::after":{opacity:.4,pointerEvents:"auto"}}},"&-tip":{color:e.spinDotDefault},[`${e.componentCls}-dot`]:{position:"relative",display:"inline-block",fontSize:e.spinDotSize,width:"1em",height:"1em","&-item":{position:"absolute",display:"block",width:(e.spinDotSize-e.marginXXS/2)/2,height:(e.spinDotSize-e.marginXXS/2)/2,backgroundColor:e.colorPrimary,borderRadius:"100%",transform:"scale(0.75)",transformOrigin:"50% 50%",opacity:.3,animationName:Ko,animationDuration:"1s",animationIterationCount:"infinite",animationTimingFunction:"linear",animationDirection:"alternate","&:nth-child(1)":{top:0,insetInlineStart:0},"&:nth-child(2)":{top:0,insetInlineEnd:0,animationDelay:"0.4s"},"&:nth-child(3)":{insetInlineEnd:0,bottom:0,animationDelay:"0.8s"},"&:nth-child(4)":{bottom:0,insetInlineStart:0,animationDelay:"1.2s"}},"&-spin":{transform:"rotate(45deg)",animationName:Ro,animationDuration:"1.2s",animationIterationCount:"infinite",animationTimingFunction:"linear"}},[`&-sm ${e.componentCls}-dot`]:{fontSize:e.spinDotSizeSM,i:{width:(e.spinDotSizeSM-e.marginXXS/2)/2,height:(e.spinDotSizeSM-e.marginXXS/2)/2}},[`&-lg ${e.componentCls}-dot`]:{fontSize:e.spinDotSizeLG,i:{width:(e.spinDotSizeLG-e.marginXXS)/2,height:(e.spinDotSizeLG-e.marginXXS)/2}},[`&${e.componentCls}-show-text ${e.componentCls}-text`]:{display:"block"}})}),Lo=Ve("Spin",e=>{const n=Oe(e,{spinDotDefault:e.colorTextDescription,spinDotSize:e.controlHeightLG/2,spinDotSizeSM:e.controlHeightLG*.35,spinDotSizeLG:e.controlHeight});return[No(n)]},{contentHeight:400});var jo=function(e,n){var t={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&n.indexOf(o)<0&&(t[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,o=Object.getOwnPropertySymbols(e);i<o.length;i++)n.indexOf(o[i])<0&&Object.prototype.propertyIsEnumerable.call(e,o[i])&&(t[o[i]]=e[o[i]]);return t};const Ho=()=>({prefixCls:String,spinning:{type:Boolean,default:void 0},size:String,wrapperClassName:String,tip:K.any,delay:Number,indicator:K.any});let Ae=null;function Fo(e,n){return!!e&&!!n&&!isNaN(Number(n))}function Vo(e){const n=e.indicator;Ae=typeof n=="function"?n:()=>f(n,null,null)}const Ee=G({compatConfig:{MODE:3},name:"ASpin",inheritAttrs:!1,props:Xe(Ho(),{size:"default",spinning:!0,wrapperClassName:""}),setup(e,n){let{attrs:t,slots:o}=n;const{prefixCls:i,size:r,direction:d}=De("spin",e),[l,u]=Lo(i),m=R(e.spinning&&!Fo(e.spinning,e.delay));let s;return U([()=>e.spinning,()=>e.delay],()=>{s==null||s.cancel(),s=zo(e.delay,()=>{m.value=e.spinning}),s==null||s()},{immediate:!0,flush:"post"}),fe(()=>{s==null||s.cancel()}),()=>{var a,p;const{class:c}=t,g=jo(t,["class"]),{tip:C=(a=o.tip)===null||a===void 0?void 0:a.call(o)}=e,v=(p=o.default)===null||p===void 0?void 0:p.call(o),h={[u.value]:!0,[i.value]:!0,[`${i.value}-sm`]:r.value==="small",[`${i.value}-lg`]:r.value==="large",[`${i.value}-spinning`]:m.value,[`${i.value}-show-text`]:!!C,[`${i.value}-rtl`]:d.value==="rtl",[c]:!!c};function w(I){const S=`${I}-dot`;let x=lt(o,e,"indicator");return x===null?null:(Array.isArray(x)&&(x=x.length===1?x[0]:x),bt(x)?$t(x,{class:S}):Ae&&bt(Ae())?$t(Ae(),{class:S}):f("span",{class:`${S} ${I}-dot-spin`},[f("i",{class:`${I}-dot-item`},null),f("i",{class:`${I}-dot-item`},null),f("i",{class:`${I}-dot-item`},null),f("i",{class:`${I}-dot-item`},null)]))}const _=f("div",M(M({},g),{},{class:h,"aria-live":"polite","aria-busy":m.value}),[w(i.value),C?f("div",{class:`${i.value}-text`},[C]):null]);if(v&&et(v).length){const I={[`${i.value}-container`]:!0,[`${i.value}-blur`]:m.value};return l(f("div",{class:[`${i.value}-nested-loading`,e.wrapperClassName,u.value]},[m.value&&f("div",{key:"loading"},[_]),f("div",{class:I,key:"container"},[v])]))}return l(_)}}});Ee.setDefaultIndicator=Vo;Ee.install=function(e){return e.component(Ee.name,Ee),e};const ko=()=>{const e=R(!1);return fe(()=>{e.value=!0}),e},Xo={type:{type:String},actionFn:Function,close:Function,autofocus:Boolean,prefixCls:String,buttonProps:pe(),emitEvent:Boolean,quitOnNullishReturnValue:Boolean};function Bt(e){return!!(e&&e.then)}const ei=G({compatConfig:{MODE:3},name:"ActionButton",props:Xo,setup(e,n){let{slots:t}=n;const o=R(!1),i=R(),r=R(!1);let d;const l=ko();ot(()=>{e.autofocus&&(d=setTimeout(()=>{var a,p;return(p=(a=Tn(i.value))===null||a===void 0?void 0:a.focus)===null||p===void 0?void 0:p.call(a)}))}),fe(()=>{clearTimeout(d)});const u=function(){for(var a,p=arguments.length,c=new Array(p),g=0;g<p;g++)c[g]=arguments[g];(a=e.close)===null||a===void 0||a.call(e,...c)},m=a=>{Bt(a)&&(r.value=!0,a.then(function(){l.value||(r.value=!1),u(...arguments),o.value=!1},p=>(l.value||(r.value=!1),o.value=!1,Promise.reject(p))))},s=a=>{const{actionFn:p}=e;if(o.value)return;if(o.value=!0,!p){u();return}let c;if(e.emitEvent){if(c=p(a),e.quitOnNullishReturnValue&&!Bt(c)){o.value=!1,u(a);return}}else if(p.length)c=p(e.close),o.value=!1;else if(c=p(),!c){u();return}m(c)};return()=>{const{type:a,prefixCls:p,buttonProps:c}=e;return f(Ke,M(M(M({},An(a)),{},{onClick:s,loading:r.value,prefixCls:p},c),{},{ref:i}),t)}}});export{ei as A,he as D,Qt as L,me as M,Le as R,Ee as S,Me as _,Jo as a,Qo as b,mo as c,Zo as e,qo as u};
