import{T as o,cS as s,cT as c,cU as l,cV as n,cW as a}from"./index-sMW2Pm6g.js";import{b as i}from"./tools-DC78Tda0.js";const p=o("role",{state:()=>({rolelist:[],roleOptions:[],modulelist:[]}),actions:{reset(){this.$reset()},async fetchGetrolelist(r){try{const e=await a(r);this.rolelist=e;let t=i(e,{label:"roleName",value:"roleID"});return this.roleOptions=t,e}catch(e){throw console.error("获取失败:",e),e}},async fetchGetmodulelist(r){try{let e=await n(r);return this.modulelist=e,e}catch(e){throw console.error("获取失败:",e),e}},async fetchAddrole(r){try{return await l(r)}catch(e){throw console.error("获取失败:",e),e}},async fetchEditrole(r){try{return await c(r)}catch(e){throw console.error(e),e}},async fetchDeleterole(r){try{return await s(r)}catch(e){throw console.error(e),e}}}});export{p as u};
