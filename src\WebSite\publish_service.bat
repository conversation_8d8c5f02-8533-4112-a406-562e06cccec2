@echo off
setlocal enabledelayedexpansion

set PROJECT_PATH=WTCMSLive.WebSite.Core
set PUBLISH_PATH=%~dp0publish
set CONFIGURATION=Release
set FRAMEWORK=net6.0
set RUNTIME=win-x64

echo ========================================
echo 发布 Windows 服务应用程序
echo ========================================

REM 检查项目文件是否存在
if not exist "%PROJECT_PATH%\%PROJECT_PATH%.csproj" (
    echo 错误: 找不到项目文件: %PROJECT_PATH%\%PROJECT_PATH%.csproj
    pause
    exit /b 1
)

echo 项目路径: %PROJECT_PATH%
echo 发布路径: %PUBLISH_PATH%
echo 配置: %CONFIGURATION%
echo 框架: %FRAMEWORK%
echo 运行时: %RUNTIME%
echo.

REM 清理现有发布目录
if exist "%PUBLISH_PATH%" (
    echo 清理现有发布目录...
    rmdir /s /q "%PUBLISH_PATH%"
)

echo 正在发布应用程序...
dotnet publish "%PROJECT_PATH%" ^
    --configuration %CONFIGURATION% ^
    --framework %FRAMEWORK% ^
    --runtime %RUNTIME% ^
    --self-contained true ^
    --output "%PUBLISH_PATH%" ^
    --verbosity minimal

if %errorLevel% neq 0 (
    echo 错误: 发布失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo 发布完成！
echo ========================================
echo 发布目录: %PUBLISH_PATH%
echo 可执行文件: %PUBLISH_PATH%\%PROJECT_PATH%.exe
echo.

REM 检查可执行文件是否存在
if exist "%PUBLISH_PATH%\%PROJECT_PATH%.exe" (
    echo 可执行文件创建成功！
    echo.
    echo 下一步:
    echo 1. 运行 install_service.bat 安装服务
    echo 2. 或者运行 manage_service.bat 管理服务
) else (
    echo 警告: 找不到可执行文件
)

echo.
pause
