import{T as a,bl as c,bm as n,bn as u,bo as i,bp as h,bq as b,br as d,bs as l,bt as p,bu as D}from"./index-sMW2Pm6g.js";import{b as o}from"./tools-DC78Tda0.js";const M=a("configModbus",{state:()=>({modbusDevTypes:[],modbusDeviceList:[],modbusLocaOptions:[],modbusMeasLocoptions:[]}),actions:{reset(){this.$reset()},async fetchGetModbusDevType(s){try{const e=await D(s);let t=o(e,{label:"value",value:"key"},{nother:!0});return t=t.map(r=>(r.value=r.value+"",r)),this.modbusDevTypes=t,t}catch(e){throw console.error("操作失败:",e),e}},async fetchAddModbusDevice(s){try{return await p(s)}catch(e){throw console.error("操作失败:",e),e}},async fetchGetModbusDeviceList(s){try{const e=await l(s);this.modbusDeviceList=e;let t=o(e,{label:"modbusDeviceName",value:"modbusDeviceID"});return this.modbusDeviceOptions=t,e}catch(e){throw console.error("操作失败:",e),e}},async fetchEditModbusDevice(s){try{return await d(s)}catch(e){throw console.error("操作失败:",e),e}},async fetchBatchDeleteModbusDevice(s){try{return await b(s)}catch(e){throw console.error("操作失败:",e),e}},async fetchAddModbusChannel(s){try{return await h(s)}catch(e){throw console.error("操作失败:",e),e}},async fetchGetModbusChannelList(s){try{const e=await i(s);let t=o(e,{label:"measLocationName",value:"measLocationID"},{nother:!0});return this.modbusLocaOptions=t,e}catch(e){throw console.error("操作失败:",e),e}},async fetchEditModbusChannel(s){try{return await u(s)}catch(e){throw console.error("操作失败:",e),e}},async fetchBatchDeleteModbusChannel(s){try{return await n(s)}catch(e){throw console.error("操作失败:",e),e}},async fetchGetModbusMeasLocByDeviceID(s){try{const e=await c(s);let t=o(e,{label:"measLocName",value:"measLocationID"},{nother:!0});return this.modbusMeasLocoptions=t,t}catch(e){throw console.error("操作失败:",e),e}}}});export{M as u};
