﻿using System.Collections.Concurrent;
using System.Net;
using System.Net.Sockets;
using CMSFramework.Logger;
using CMSFramework.TypeDef;
using WindCMS.GWServer.Agent;
using WindCMS.GWServer.Utils;
using WTCMSLive.BusinessModel;

namespace WindCMS.GWServer.ListenerServer;

/// <summary>
/// TCP 监听服务
/// </summary>
public class TcpListenerServer
{
    /// <summary>
    /// TCP 监听服务
    /// </summary>
    private static TcpListenerServer? _tcpListenerServer;

    /// <summary>
    /// 线程字典
    /// </summary>
    private static readonly ConcurrentDictionary<string, TcpListenerAgent> TcpListenerAgentDictionary = new();
    
    /// <summary>
    /// Tcp服务端
    /// </summary>
    private readonly TcpListener _listener = new(IPAddress.Any, AppConfigUtils.ListenerPort);

    private TcpListenerServer()
    {
    }

    /// <summary>
    /// 获取
    /// </summary>
    public static TcpListenerServer Instance => _tcpListenerServer ??= new TcpListenerServer();

    /// <summary>
    /// 监听客户端
    /// </summary>
    public void StartAccept()
    {
        _listener.Start();
        while (true)
        {
            try
            {
                var tcpClient = _listener.AcceptTcpClient();
                Logger.LogInfoMessage($"[AcceptTcpClient] {tcpClient.Client.RemoteEndPoint} accepted.");

                var tcpListenerContextKey = GetTcpListenerContextKey(tcpClient);
                if (TcpListenerAgentDictionary.TryGetValue(tcpListenerContextKey, out var listenerContext))
                {
                    CloseTcpClient(listenerContext);
                    // 睡眠, 等待其他线程释放资源
                    Thread.Sleep(1 * 1000);
                }

                tcpClient.SendBufferSize = 64 * 1024;
                tcpClient.ReceiveBufferSize = 64 * 1024;
                
                // 构建上下文信息
                var listenerAgent = new TcpListenerAgent
                {
                    ConnectReady = false,
                    TcpClient = tcpClient, 
                    TcpListenerContextKey = GetTcpListenerContextKey(tcpClient), 
                    CancellationTokenSource = new CancellationTokenSource(), 
                    ListenerReadHandler = new ListenerReadHandler(),
                };
                
                // 创建读取数据线程
                var cancellationToken = listenerAgent.CancellationToken;
                if (cancellationToken != null)
                {
                    var readTask =
                        Task.Run(
                            () => listenerAgent.ListenerReadHandler.ReadData(listenerAgent), cancellationToken.Value);
                    listenerAgent.ReadDataTask = readTask;   
                }

                // 上下文信息添加到集合中
                TcpListenerAgentDictionary[tcpListenerContextKey] = listenerAgent;
                Logger.LogInfoMessage($"[AcceptTcpClient] {tcpClient.Client.RemoteEndPoint} 上下文信息创建完成.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"错误: {ex.Message}");
            }
        }
    }
    
    /// <summary>
    /// 获取客户端信息字典Key
    /// </summary>
    /// <param name="client"></param>
    /// <returns></returns>
    private string GetTcpListenerContextKey(TcpClient client)
    {
        var remoteEndPoint = (IPEndPoint)client.Client.RemoteEndPoint!;
        return $"{remoteEndPoint.Address}";
    }

    /// <summary>
    /// 关闭客户端
    /// </summary>
    /// <param name="agent"></param>
    public static void CloseTcpClient(TcpListenerAgent agent)
    {
        try
        {
            // 更新 DAU 状态
            UpdateDauStatus(agent);
            // 关闭 CancellationTokenSource
            CloseCancellationTokenSource(agent);
            // 关闭通道
            CloseTaskChannel(agent);
            // 关闭客户端
            CloseClient(agent);
            // 关闭
            agent.CancellationTokenSource.Dispose();
            // 字典中移除对应 key
            TcpListenerAgentDictionary.TryRemove(agent.TcpListenerContextKey, out _);
        }
        catch (Exception e)
        {
            // ignored
        }
    }
    
    
    /// <summary>
    /// 更新DAU状态
    /// </summary>
    /// <param name="agent"></param>
    private static void UpdateDauStatus(TcpListenerAgent agent)
    {
        try
        {
            var dauConfigExtension = agent.DauConfigExtension;
            DauManagement.UpdateDAUOnOffStatus(dauConfigExtension.WindTurbineId,
                dauConfigExtension.DauDeviceType.GetHashCode().ToString(), EnumDauOnOffStatus.Off);
        }
        catch (Exception)
        {
            // ignore
        }
    }

    /// <summary>
    /// 取消
    /// </summary>
    /// <param name="agent"></param>
    private static void CloseCancellationTokenSource(TcpListenerAgent agent)
    {
        try
        {
            agent.CancellationTokenSource.Cancel();
        }
        catch (Exception e)
        {
            // ignored
        }
    }

    /// <summary>
    /// 关闭客户端线程通道
    /// </summary>
    /// <param name="agent"></param>
    private static void CloseTaskChannel(TcpListenerAgent agent)
    {
        try
        {
            agent.ReadDataTask.Dispose();
        }
        catch (Exception e)
        {
            // ignored 关闭失败则认为通道已经关闭
        }
    }

    /// <summary>
    /// 关闭客户端
    /// </summary>
    /// <param name="agent"></param>
    private static void CloseClient(TcpListenerAgent agent)
    {
        try
        {
            agent.TcpClient.GetStream().Close();
        }
        catch (Exception e)
        {
            // ignored 关闭失败则认为链接已经断开
        }
        try
        {
            agent.TcpClient.Close();
        }
        catch (Exception e)
        {
            // ignored 关闭失败则认为链接已经断开
        }
        
        try
        {
            var close = agent.DauWorkContext?.DauFacade as IDisposable;
            close?.Dispose();
        }
        catch (Exception e)
        {
            // ignored 关闭失败则认为链接已经断开
        }
    }

    /// <summary>
    /// 获取客户端信息字典 Key
    /// </summary>
    /// <param name="ip"></param>
    /// <returns></returns>
    public static string GetTcpListenerContextKey(string ip)
    {
        return ip;
    }

    /// <summary>
    /// 获取客户端信息
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    public static TcpListenerAgent? GetTcpListenerAgent(string key)
    {
        var tcpListenerAgent = TcpListenerAgentDictionary.GetValueOrDefault(key);
        if (tcpListenerAgent != null) 
        {
            if (tcpListenerAgent.ConnectReady)
            {
                return tcpListenerAgent;
            }
        }
        return null;
    }
    
    /// <summary>
    /// 获取客户端信息
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    internal static TcpListenerAgent? GetTcpListenerAgent()
    {
        var tcpListenerAgent = TcpListenerAgentDictionary.Values.FirstOrDefault();
        return tcpListenerAgent is { ConnectReady: true } ? tcpListenerAgent : null;
    }
}