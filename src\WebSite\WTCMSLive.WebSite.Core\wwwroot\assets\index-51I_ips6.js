import{K as ke,L as $e,_ as k,M as Pe,bc as _e,as as ue,H as E,bh as B,cq as H,O as X,j as N,w as D,h as de,cd as ae,cr as ze,b as p,I as T,cs as Ee,bk as Fe,r as U,ct as je,bi as We,cu as he,cv as ve,co as ge,f as M,o as b,d as w,i as _,t as Q,g as ie,c as R,p as ye,cw as Ge,u as De,a as Ue,F as J,e as re,q as G,cx as Xe,cy as Se,v as Ke,cz as Ae}from"./index-sMW2Pm6g.js";import{_ as Be}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{u as Je,e as Ve,a as qe,b as Ie,L as xe,R as Ce,c as Ye,M as Me,_ as He,D as Te,S as Qe}from"./ActionButton-DMeJvyLo.js";import{r as Ze,R as et,c as tt}from"./styleChecker-LI4Lr2UF.js";import{M as nt}from"./index-sJyM5xm7.js";import"./ChangeLanguage.vue_vue_type_style_index_0_scoped_f1388662_lang-l0sNRNKZ.js";import{i as ot}from"./index-Bi-LLAnN.js";const at=e=>{const{antCls:n,componentCls:t,iconCls:o,avatarBg:r,avatarColor:d,containerSize:i,containerSizeLG:c,containerSizeSM:g,textFontSize:h,textFontSizeLG:v,textFontSizeSM:u,borderRadius:f,borderRadiusLG:y,borderRadiusSM:$,lineWidth:a,lineType:l}=e,s=(A,m,x)=>({width:A,height:A,lineHeight:`${A-a*2}px`,borderRadius:"50%",[`&${t}-square`]:{borderRadius:x},[`${t}-string`]:{position:"absolute",left:{_skip_check_:!0,value:"50%"},transformOrigin:"0 center"},[`&${t}-icon`]:{fontSize:m,[`> ${o}`]:{margin:0}}});return{[t]:k(k(k(k({},Pe(e)),{position:"relative",display:"inline-block",overflow:"hidden",color:d,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:r,border:`${a}px ${l} transparent`,"&-image":{background:"transparent"},[`${n}-image-img`]:{display:"block"}}),s(i,h,f)),{"&-lg":k({},s(c,v,y)),"&-sm":k({},s(g,u,$)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},rt=e=>{const{componentCls:n,groupBorderColor:t,groupOverlapping:o,groupSpace:r}=e;return{[`${n}-group`]:{display:"inline-flex",[`${n}`]:{borderColor:t},"> *:not(:first-child)":{marginInlineStart:o}},[`${n}-group-popover`]:{[`${n} + ${n}`]:{marginInlineStart:r}}}},Oe=ke("Avatar",e=>{const{colorTextLightSolid:n,colorTextPlaceholder:t}=e,o=$e(e,{avatarBg:t,avatarColor:n});return[at(o),rt(o)]},e=>{const{controlHeight:n,controlHeightLG:t,controlHeightSM:o,fontSize:r,fontSizeLG:d,fontSizeXL:i,fontSizeHeading3:c,marginXS:g,marginXXS:h,colorBorderBg:v}=e;return{containerSize:n,containerSizeLG:t,containerSizeSM:o,textFontSize:Math.round((d+i)/2),textFontSizeLG:c,textFontSizeSM:r,groupSpace:h,groupOverlapping:-g,groupBorderColor:v}}),Le=Symbol("AvatarContextKey"),st=()=>_e(Le,{}),lt=e=>ue(Le,e),it=()=>({prefixCls:String,shape:{type:String,default:"circle"},size:{type:[Number,String,Object],default:()=>"default"},src:String,srcset:String,icon:B.any,alt:String,gap:Number,draggable:{type:Boolean,default:void 0},crossOrigin:String,loadError:{type:Function}}),P=E({compatConfig:{MODE:3},name:"AAvatar",inheritAttrs:!1,props:it(),slots:Object,setup(e,n){let{slots:t,attrs:o}=n;const r=H(!0),d=H(!1),i=H(1),c=H(null),g=H(null),{prefixCls:h}=X("avatar",e),[v,u]=Oe(h),f=st(),y=N(()=>e.size==="default"?f.size:e.size),$=Je(),a=Ve(()=>{if(typeof e.size!="object")return;const m=Ze.find(S=>$.value[S]);return e.size[m]}),l=m=>a.value?{width:`${a.value}px`,height:`${a.value}px`,lineHeight:`${a.value}px`,fontSize:`${m?a.value/2:18}px`}:{},s=()=>{if(!c.value||!g.value)return;const m=c.value.offsetWidth,x=g.value.offsetWidth;if(m!==0&&x!==0){const{gap:S=4}=e;S*2<x&&(i.value=x-S*2<m?(x-S*2)/m:1)}},A=()=>{const{loadError:m}=e;(m==null?void 0:m())!==!1&&(r.value=!1)};return D(()=>e.src,()=>{ae(()=>{r.value=!0,i.value=1})}),D(()=>e.gap,()=>{ae(()=>{s()})}),de(()=>{ae(()=>{s(),d.value=!0})}),()=>{var m,x;const{shape:S,src:C,alt:F,srcset:ee,draggable:j,crossOrigin:I}=e,W=(m=f.shape)!==null&&m!==void 0?m:S,O=ze(t,e,"icon"),z=h.value,te={[`${o.class}`]:!!o.class,[z]:!0,[`${z}-lg`]:y.value==="large",[`${z}-sm`]:y.value==="small",[`${z}-${W}`]:!0,[`${z}-image`]:C&&r.value,[`${z}-icon`]:O,[u.value]:!0},ne=typeof y.value=="number"?{width:`${y.value}px`,height:`${y.value}px`,lineHeight:`${y.value}px`,fontSize:O?`${y.value/2}px`:"18px"}:{},K=(x=t.default)===null||x===void 0?void 0:x.call(t);let L;if(C&&r.value)L=p("img",{draggable:j,src:C,srcset:ee,onError:A,alt:F,crossorigin:I},null);else if(O)L=O;else if(d.value||i.value!==1){const oe=`scale(${i.value}) translateX(-50%)`,Re={msTransform:oe,WebkitTransform:oe,transform:oe},Ne=typeof y.value=="number"?{lineHeight:`${y.value}px`}:{};L=p(et,{onResize:s},{default:()=>[p("span",{class:`${z}-string`,ref:c,style:k(k({},Ne),Re)},[K])]})}else L=p("span",{class:`${z}-string`,ref:c,style:{opacity:0}},[K]);return v(p("span",T(T({},o),{},{ref:g,class:te,style:[ne,l(!!O),o.style]}),[L]))}}}),ct=()=>({prefixCls:String,maxCount:Number,maxStyle:{type:Object,default:void 0},maxPopoverPlacement:{type:String,default:"top"},maxPopoverTrigger:String,size:{type:[Number,String,Object],default:"default"},shape:{type:String,default:"circle"}}),ce=E({compatConfig:{MODE:3},name:"AAvatarGroup",inheritAttrs:!1,props:ct(),setup(e,n){let{slots:t,attrs:o}=n;const{prefixCls:r,direction:d}=X("avatar",e),i=N(()=>`${r.value}-group`),[c,g]=Oe(r);return Ee(()=>{const h={size:e.size,shape:e.shape};lt(h)}),()=>{const{maxPopoverPlacement:h="top",maxCount:v,maxStyle:u,maxPopoverTrigger:f="hover",shape:y}=e,$={[i.value]:!0,[`${i.value}-rtl`]:d.value==="rtl",[`${o.class}`]:!!o.class,[g.value]:!0},a=ze(t,e),l=Fe(a).map((A,m)=>tt(A,{key:`avatar-key-${m}`})),s=l.length;if(v&&v<s){const A=l.slice(0,v),m=l.slice(v,s);return A.push(p(qe,{key:"avatar-popover-key",content:m,trigger:f,placement:h,overlayClassName:`${i.value}-popover`},{default:()=>[p(P,{style:u,shape:y},{default:()=>[`+${s-v}`]})]})),c(p("div",T(T({},o),{},{class:$,style:o.style}),[A]))}return c(p("div",T(T({},o),{},{class:$,style:o.style}),[l]))}}});P.Group=ce;P.install=function(e){return e.component(P.name,P),e.component(ce.name,ce),e};const ut=e=>!isNaN(parseFloat(e))&&isFinite(e),dt=e=>{const{componentCls:n,colorBgContainer:t,colorBgBody:o,colorText:r}=e;return{[`${n}-sider-light`]:{background:t,[`${n}-sider-trigger`]:{color:r,background:t},[`${n}-sider-zero-width-trigger`]:{color:r,background:t,border:`1px solid ${o}`,borderInlineStart:0}}}},gt=e=>{const{antCls:n,componentCls:t,colorText:o,colorTextLightSolid:r,colorBgHeader:d,colorBgBody:i,colorBgTrigger:c,layoutHeaderHeight:g,layoutHeaderPaddingInline:h,layoutHeaderColor:v,layoutFooterPadding:u,layoutTriggerHeight:f,layoutZeroTriggerSize:y,motionDurationMid:$,motionDurationSlow:a,fontSize:l,borderRadius:s}=e;return{[t]:k(k({display:"flex",flex:"auto",flexDirection:"column",color:o,minHeight:0,background:i,"&, *":{boxSizing:"border-box"},[`&${t}-has-sider`]:{flexDirection:"row",[`> ${t}, > ${t}-content`]:{width:0}},[`${t}-header, &${t}-footer`]:{flex:"0 0 auto"},[`${t}-header`]:{height:g,paddingInline:h,color:v,lineHeight:`${g}px`,background:d,[`${n}-menu`]:{lineHeight:"inherit"}},[`${t}-footer`]:{padding:u,color:o,fontSize:l,background:i},[`${t}-content`]:{flex:"auto",minHeight:0},[`${t}-sider`]:{position:"relative",minWidth:0,background:d,transition:`all ${$}, background 0s`,"&-children":{height:"100%",marginTop:-.1,paddingTop:.1,[`${n}-menu${n}-menu-inline-collapsed`]:{width:"auto"}},"&-has-trigger":{paddingBottom:f},"&-right":{order:1},"&-trigger":{position:"fixed",bottom:0,zIndex:1,height:f,color:r,lineHeight:`${f}px`,textAlign:"center",background:c,cursor:"pointer",transition:`all ${$}`},"&-zero-width":{"> *":{overflow:"hidden"},"&-trigger":{position:"absolute",top:g,insetInlineEnd:-y,zIndex:1,width:y,height:y,color:r,fontSize:e.fontSizeXL,display:"flex",alignItems:"center",justifyContent:"center",background:d,borderStartStartRadius:0,borderStartEndRadius:s,borderEndEndRadius:s,borderEndStartRadius:0,cursor:"pointer",transition:`background ${a} ease`,"&::after":{position:"absolute",inset:0,background:"transparent",transition:`all ${a}`,content:'""'},"&:hover::after":{background:"rgba(255, 255, 255, 0.2)"},"&-right":{insetInlineStart:-y,borderStartStartRadius:s,borderStartEndRadius:0,borderEndEndRadius:0,borderEndStartRadius:s}}}}},dt(e)),{"&-rtl":{direction:"rtl"}})}},pt=ke("Layout",e=>{const{colorText:n,controlHeightSM:t,controlHeight:o,controlHeightLG:r,marginXXS:d}=e,i=r*1.25,c=$e(e,{layoutHeaderHeight:o*2,layoutHeaderPaddingInline:i,layoutHeaderColor:n,layoutFooterPadding:`${t}px ${i}px`,layoutTriggerHeight:r+d*2,layoutZeroTriggerSize:r});return[gt(c)]},e=>{const{colorBgLayout:n}=e;return{colorBgHeader:"#001529",colorBgBody:n,colorBgTrigger:"#002140"}}),pe=()=>({prefixCls:String,hasSider:{type:Boolean,default:void 0},tagName:String});function Z(e){let{suffixCls:n,tagName:t,name:o}=e;return r=>E({compatConfig:{MODE:3},name:o,props:pe(),setup(i,c){let{slots:g}=c;const{prefixCls:h}=X(n,i);return()=>{const v=k(k({},i),{prefixCls:h.value,tagName:t});return p(r,v,g)}}})}const me=E({compatConfig:{MODE:3},props:pe(),setup(e,n){let{slots:t}=n;return()=>p(e.tagName,{class:e.prefixCls},t)}}),mt=E({compatConfig:{MODE:3},inheritAttrs:!1,props:pe(),setup(e,n){let{slots:t,attrs:o}=n;const{prefixCls:r,direction:d}=X("",e),[i,c]=pt(r),g=U([]);ue(Ie,{addSider:u=>{g.value=[...g.value,u]},removeSider:u=>{g.value=g.value.filter(f=>f!==u)}});const v=N(()=>{const{prefixCls:u,hasSider:f}=e;return{[c.value]:!0,[`${u}`]:!0,[`${u}-has-sider`]:typeof f=="boolean"?f:g.value.length>0,[`${u}-rtl`]:d.value==="rtl"}});return()=>{const{tagName:u}=e;return i(p(u,k(k({},o),{class:[v.value,o.class]}),t))}}}),se=Z({suffixCls:"layout",tagName:"section",name:"ALayout"})(mt),V=Z({suffixCls:"layout-header",tagName:"header",name:"ALayoutHeader"})(me),le=Z({suffixCls:"layout-footer",tagName:"footer",name:"ALayoutFooter"})(me),q=Z({suffixCls:"layout-content",tagName:"main",name:"ALayoutContent"})(me);var ft={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"bars",theme:"outlined"};function be(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?Object(arguments[n]):{},o=Object.keys(t);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(t).filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable}))),o.forEach(function(r){ht(e,r,t[r])})}return e}function ht(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}var fe=function(n,t){var o=be({},n,t.attrs);return p(je,be({},o,{icon:ft}),null)};fe.displayName="BarsOutlined";fe.inheritAttrs=!1;const we={xs:"479.98px",sm:"575.98px",md:"767.98px",lg:"991.98px",xl:"1199.98px",xxl:"1599.98px",xxxl:"1999.98px"},vt=()=>({prefixCls:String,collapsible:{type:Boolean,default:void 0},collapsed:{type:Boolean,default:void 0},defaultCollapsed:{type:Boolean,default:void 0},reverseArrow:{type:Boolean,default:void 0},zeroWidthTriggerStyle:{type:Object,default:void 0},trigger:B.any,width:B.oneOfType([B.number,B.string]),collapsedWidth:B.oneOfType([B.number,B.string]),breakpoint:B.oneOf(ve("xs","sm","md","lg","xl","xxl","xxxl")),theme:B.oneOf(ve("light","dark")).def("dark"),onBreakpoint:Function,onCollapse:Function}),yt=(()=>{let e=0;return function(){let n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";return e+=1,`${n}${e}`}})(),Y=E({compatConfig:{MODE:3},name:"ALayoutSider",inheritAttrs:!1,props:ot(vt(),{collapsible:!1,defaultCollapsed:!1,reverseArrow:!1,width:200,collapsedWidth:80}),emits:["breakpoint","update:collapsed","collapse"],setup(e,n){let{emit:t,attrs:o,slots:r}=n;const{prefixCls:d}=X("layout-sider",e),i=_e(Ie,void 0),c=H(!!(e.collapsed!==void 0?e.collapsed:e.defaultCollapsed)),g=H(!1);D(()=>e.collapsed,()=>{c.value=!!e.collapsed}),ue(Ye,c);const h=(a,l)=>{e.collapsed===void 0&&(c.value=a),t("update:collapsed",a),t("collapse",a,l)},v=H(a=>{g.value=a.matches,t("breakpoint",a.matches),c.value!==a.matches&&h(a.matches,"responsive")});let u;function f(a){return v.value(a)}const y=yt("ant-sider-");i&&i.addSider(y),de(()=>{D(()=>e.breakpoint,()=>{try{u==null||u.removeEventListener("change",f)}catch{u==null||u.removeListener(f)}if(typeof window<"u"){const{matchMedia:a}=window;if(a&&e.breakpoint&&e.breakpoint in we){u=a(`(max-width: ${we[e.breakpoint]})`);try{u.addEventListener("change",f)}catch{u.addListener(f)}f(u)}}},{immediate:!0})}),We(()=>{try{u==null||u.removeEventListener("change",f)}catch{u==null||u.removeListener(f)}i&&i.removeSider(y)});const $=()=>{h(!c.value,"clickTrigger")};return()=>{var a,l;const s=d.value,{collapsedWidth:A,width:m,reverseArrow:x,zeroWidthTriggerStyle:S,trigger:C=(a=r.trigger)===null||a===void 0?void 0:a.call(r),collapsible:F,theme:ee}=e,j=c.value?A:m,I=ut(j)?`${j}px`:String(j),W=parseFloat(String(A||0))===0?p("span",{onClick:$,class:he(`${s}-zero-width-trigger`,`${s}-zero-width-trigger-${x?"right":"left"}`),style:S},[C||p(fe,null,null)]):null,O={expanded:x?p(Ce,null,null):p(xe,null,null),collapsed:x?p(xe,null,null):p(Ce,null,null)},z=c.value?"collapsed":"expanded",te=O[z],ne=C!==null?W||p("div",{class:`${s}-trigger`,onClick:$,style:{width:I}},[C||te]):null,K=[o.style,{flex:`0 0 ${I}`,maxWidth:I,minWidth:I,width:I}],L=he(s,`${s}-${ee}`,{[`${s}-collapsed`]:!!c.value,[`${s}-has-trigger`]:F&&C!==null&&!W,[`${s}-below`]:!!g.value,[`${s}-zero-width`]:parseFloat(I)===0},o.class);return p("aside",T(T({},o),{},{class:L,style:K}),[p("div",{class:`${s}-children`},[(l=r.default)===null||l===void 0?void 0:l.call(r)]),F||g.value&&W?ne:null])}}}),St=V,Wt=Y,Gt=q,Dt=k(se,{Header:V,Footer:le,Content:q,Sider:Y,install:e=>(e.component(se.name,se),e.component(V.name,V),e.component(le.name,le),e.component(Y.name,Y),e.component(q.name,q),e)}),At=ge(),xt={name:"AvatarDropdown",props:{currentUser:{type:Object,default:()=>null},menu:{type:Boolean,default:!0}},methods:{handleToCenter(){this.$router.push({path:"/account/center"})},handleToSettings(){this.$router.push({path:"/account/settings"})},handleLogout(e){nt.confirm({title:this.$t("layouts.usermenu.dialog.title"),content:this.$t("layouts.usermenu.dialog.content"),okText:this.$t("button.confirm"),cancelText:this.$t("button.cancel"),onOk:()=>{At.logout()},onCancel(){}})}}},Ct={class:"ant-pro-account-avatar"};function bt(e,n,t,o,r,d){const i=P,c=He,g=Me,h=Te,v=Qe;return t.currentUser&&t.currentUser.name?(b(),M(h,{key:0,placement:"bottomRight",overlayClassName:"logoutClass"},{overlay:w(()=>[p(g,{class:"ant-pro-drop-down menu","selected-keys":[]},{default:w(()=>[p(c,{key:"logout",onClick:d.handleLogout},{default:w(()=>[ie(Q(e.$t("menu.logout")),1)]),_:1},8,["onClick"])]),_:1})]),default:w(()=>[_("span",Ct,[p(i,{size:"small",src:"/userIcon.png",class:"antd-pro-global-header-index-avatar"}),_("span",null,Q(t.currentUser.name),1)])]),_:1})):(b(),M(h,{key:1,overlayClassName:"logoutClass",placement:"bottomRight"},{overlay:w(()=>[p(g,{class:"ant-pro-drop-down menu","selected-keys":[]},{default:w(()=>[p(c,{key:"logout",onClick:d.handleLogout},{default:w(()=>n[1]||(n[1]=[ie(" 返回登录 ",-1)])),_:1,__:[1]},8,["onClick"])]),_:1})]),default:w(()=>[_("span",null,[p(v,{size:"small",style:{marginLeft:8,marginRight:8}}),n[0]||(n[0]=_("span",{class:"noLogin"},"未登录",-1))])]),_:1}))}const wt=Be(xt,[["render",bt],["__scopeId","data-v-dd3e31e2"]]),kt={__name:"RightContent",props:{prefixCls:{type:String,default:"ant-pro-global-header-index-action"},isMobile:{type:Boolean,default:()=>!1}},setup(e){const n=ge(),t=U(!0),o=U({}),r=N(()=>({"ant-pro-global-header-index-right":!0}));return de(()=>{let d=localStorage.getItem("user");if(d){let i=JSON.parse(d);o.value={name:i.username}}else n.logout()}),(d,i)=>(b(),R("div",{class:ye(r.value)},[p(wt,{menu:t.value,"current-user":o.value,class:ye(e.prefixCls)},null,8,["menu","current-user","class"])],2))}},$t="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAi5JREFUWEfdV4FNAzEMtDehk0AngU5COwl0kpZJ6Cam97Ijf97+JA9SJSKhtjQfn8/ni8v04MUPjk9DAETkiYheieiFiPAefzdNAq9XIvpiZrx2rS4AIoKAHxqw52CA+WTmU2vzKgDNGIEBAGs6GFniPTPfdA++q9mx/Xvsy4CkADTriw/ck5ECAuB3V6ITMwP4YoUAquBXZt63qKy/d3o5KnOHSBsLAPrgtx6IOh5Gg9v+AMSiHBEA0A4KN2WeMIFyvKludn7PDICIYBNER/d6NztERHBws+2UCSQGoYKF0qY1AFCPTahXKBqP/g5AtN2aZXLJoXsKCwWAF15P9gAyCACJWUsXFjwA0AnFhsITETyMErVWakJaMsQ4Wkt7ACa+kH7NFsG9zUKsCOiNJhWwY7kI3AOw+u8i51IAs86IShDtq9oScYoOPAAIKlX/vwDghFsSHS7BnSR/w0E30MTif5F9O5cNS2AinBmFq99Uos6VdZIZXfl+pA3Rx3YtGw60Jhg4z+w1MTHXymEbIsBMoa1sR4xI67/otNqKV70guGj+zooVISgGiJlfZ0yMMCAiln1+GSkIY+FXs0B1aaVXfDaQ2NVZxNLSwwpL/g5ZuGw2kpkgJ+/fMpJVbOJj2N5rQylAGBPdY7YGtpsVH/FsOA9O1r9GbTKWW99HY/kzrlp3JoJvG8srEcHBbMzukQMCp6P4zLR6TnN23PPT7Lz2Q6SO1xw8RwBu2ftwAD8fm4IwmWb6LAAAAABJRU5ErkJggg==",_t="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAj5JREFUWEfFV4ttwkAMtSdpmaRlkpZJCpO0TFI6SdnE5UX2yee7S5wUiUiIoDjn5+fnD0wPvvjB/mkVABF5JqI3InolItzjc9Ug8H0hoh9mxnfqSgEQETj8VIeZgwHmi5lPS8azADRiOAYAXNPBiBL3zHxVGzyL7Jj9HnYjIEMAGvW3d5yJSAEB8IdL0YmZAby5ugCC8wsz75eojM+dXo7K3KGnjQaAvvirByKPh7XOzb4DoklHDwBoB4WbIh8wgXS8q2523qYCICIwgujolu9UhWTYUSYQGIQKFkqZRgCgHkbIV1c0GYc9GxccqqewUAB44cXoRcTS0jv7uKI6rKQLCx4A8gTFNsITEWMGz+N1nqtzbywi5qOA9gAsyoZ+B2DXc2a1j7S5PgCqq5bsWC4C9wAsysZJAoCJF7rBvV1VelyJFx14ADJSvwMQ6UeLRZ940fThtzUtBFQJ7h4AGg1AgC63JX1OuBWjN9sq0HulwMQ1C2CJARNh1SjAeUIDWQBFK9bi15ZhrzlhND+pBpYYQB8AiG4ZogM2wlEG7MVeI7L9ADYl3yIyveObWo/J2IqHvWBrC3aT0ejvt2KNFFMQICqj/zoPOhoPIzU0Fv61C4QWPBzxo4XERmdq0MwxZFpQm6bLjlYyEyTe27yYhCnalPfUeUfowxKRXrM1jdYX8HNq16P/ClvWcky482Att5lgcU2zYdNaHkSEErI1O1MUcDxcxf0Bq/a+5F+z9IIyq4FMmPewWcXAPRzGM/4AhCeGMAVcCY8AAAAASUVORK5CYII=",zt={class:"topMenus"},Bt={class:"topRight clearfix"},It={key:0,class:"pullLeft"},Mt={key:1,class:"pullLeft"},Ht=["onClick"],Tt={class:"pullLeft"},Ot={__name:"index",props:{currentTreeNode:{type:Object,default:()=>({})}},emits:["menu-select","changeView"],setup(e,{emit:n}){const t=n,o=e,{locale:r}=Ge(),d=De(),i=Ue();ge();const c=U(window.localStorage.getItem("templateManagement")==="true"),g=U([]);N(()=>r.value==="en"?$t:_t);const h=N(()=>{const a=i.getRoutes();return c.value?a.filter(l=>{var s;return((s=l.meta)==null?void 0:s.locationLeft)&&l.name==="config"}):o.currentTreeNode&&o.currentTreeNode.type?a.filter(l=>{var s;return((s=l.meta)==null?void 0:s.locationLeft)&&l.children.some(A=>{var m;return((m=A.meta)==null?void 0:m.selectNodeType)===o.currentTreeNode.type})}):a.filter(l=>{var s;return(s=l.meta)==null?void 0:s.locationLeft})}),v=N(()=>i.getRoutes().filter(a=>{var l;return(l=a.meta)==null?void 0:l.locationRight})),u=({item:a,key:l,keyPath:s})=>{t("menu-select",l)},f=a=>{try{a.name=="templateManagement"?(window.localStorage.setItem("templateManagement",!0),Ae({templateView:!0}),c.value=!0,t("changeView","enter")):i.push({name:a.name})}catch(l){console.error("Error navigating to route:",l)}},y=()=>{window.localStorage.setItem("templateManagement",!1),Ae({}),c.value=!1,t("changeView","exit")};D(()=>d.path,()=>{d.matched&&d.matched.length>0&&(g.value=[d.matched[0].path])},{immediate:!0});const $=()=>{Ke(d,i)};return(a,l)=>{const s=He,A=Me,m=Te,x=St;return b(),M(x,{class:"header clearfix"},{default:w(()=>[_("div",{class:"logo pullLeft",onClick:$},l[2]||(l[2]=[_("span",{class:"title"},"配置网站",-1)])),_("div",zt,[p(A,{selectedKeys:g.value,"onUpdate:selectedKeys":l[0]||(l[0]=S=>g.value=S),theme:"dark",mode:"horizontal",onSelect:u},{default:w(()=>[(b(!0),R(J,null,re(h.value,S=>(b(),M(s,{key:S.path},{default:w(()=>[ie(Q(a.$t(S.meta.title)),1)]),_:2},1024))),128))]),_:1},8,["selectedKeys"])]),_("div",Bt,[c.value?(b(),R("div",It,[_("span",{class:"exitTemp",onClick:y},"返回")])):G("",!0),c.value?G("",!0):(b(),R("div",Mt,[(b(!0),R(J,null,re(v.value,S=>(b(),M(m,{key:S.path,placement:"bottom",overlayClassName:"manageClass",arrow:""},{overlay:w(()=>[p(A,null,{default:w(()=>[(b(!0),R(J,null,re(S.children,C=>(b(),R(J,{key:C.path},[C.meta&&!C.meta.hideMenu?(b(),M(s,{key:0},{default:w(()=>[C.icon?(b(),M(Se(C.icon),{key:0})):G("",!0),_("a",{href:"javascript:;",onClick:F=>f(C),class:"dropMenuItem"},Q(a.$t(C.meta.title)),9,Ht)]),_:2},1024)):G("",!0)],64))),128))]),_:2},1024)]),default:w(()=>[_("a",{class:"dropdown-link",onClick:l[1]||(l[1]=Xe(()=>{},["prevent"]))},[S.meta&&S.meta.icon?(b(),M(Se(S.meta.icon),{key:0})):G("",!0)])]),_:2},1024))),128))])),_("div",Tt,[p(kt)])])]),_:1})}}},Ut=Be(Ot,[["__scopeId","data-v-f5a5d8b5"]]);export{Ut as H,Wt as L,Dt as _,Gt as a};
