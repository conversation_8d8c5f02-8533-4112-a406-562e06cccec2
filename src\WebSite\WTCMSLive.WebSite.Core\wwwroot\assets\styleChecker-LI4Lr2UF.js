import{H as k,Y as Ln,h as Dt,J as Kt,x as qe,w as X,dc as ut,dd as Bn,_ as m,bh as P,e4 as Ke,b as T,dx as Zt,I as q,ba as Ze,e5 as Hn,r as G,bk as Qe,cu as rt,cq as S,bi as Je,j as H,da as Qt,dz as tn,d5 as In,d9 as zn,e6 as Vn,F as en,dE as Fn,dB as ot,cd as pt,bj as Wn,dA as jn,bb as Xn,cx as se,e7 as kn,cs as nn,df as st,e8 as Yn,e9 as ae,D as Un,ea as Gn,bd as qn,eb as le,cZ as Kn,c_ as Zn,as as Qn,dC as Jn,bc as to,N as V,ec as eo,cr as no,B as gt,K as oo,L as on,M as io,O as ro,cY as so,ed as ao,d7 as lo,bf as uo,S as co}from"./index-sMW2Pm6g.js";import{w as j,o as fo,M as rn,s as po,l as ue,k as Tt,r as ho,z as Xt,G as sn,H as mo,q as vo,v as ce,t as fe,a as go,i as yo}from"./index-Bi-LLAnN.js";var an=function(){if(typeof Map<"u")return Map;function t(e,n){var o=-1;return e.some(function(i,r){return i[0]===n?(o=r,!0):!1}),o}return function(){function e(){this.__entries__=[]}return Object.defineProperty(e.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),e.prototype.get=function(n){var o=t(this.__entries__,n),i=this.__entries__[o];return i&&i[1]},e.prototype.set=function(n,o){var i=t(this.__entries__,n);~i?this.__entries__[i][1]=o:this.__entries__.push([n,o])},e.prototype.delete=function(n){var o=this.__entries__,i=t(o,n);~i&&o.splice(i,1)},e.prototype.has=function(n){return!!~t(this.__entries__,n)},e.prototype.clear=function(){this.__entries__.splice(0)},e.prototype.forEach=function(n,o){o===void 0&&(o=null);for(var i=0,r=this.__entries__;i<r.length;i++){var s=r[i];n.call(o,s[1],s[0])}},e}()}(),kt=typeof window<"u"&&typeof document<"u"&&window.document===document,St=function(){return typeof global<"u"&&global.Math===Math?global:typeof self<"u"&&self.Math===Math?self:typeof window<"u"&&window.Math===Math?window:Function("return this")()}(),bo=function(){return typeof requestAnimationFrame=="function"?requestAnimationFrame.bind(St):function(t){return setTimeout(function(){return t(Date.now())},1e3/60)}}(),wo=2;function Oo(t,e){var n=!1,o=!1,i=0;function r(){n&&(n=!1,t()),o&&a()}function s(){bo(r)}function a(){var u=Date.now();if(n){if(u-i<wo)return;o=!0}else n=!0,o=!1,setTimeout(s,e);i=u}return a}var _o=20,Co=["top","right","bottom","left","width","height","size","weight"],Po=typeof MutationObserver<"u",xo=function(){function t(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=Oo(this.refresh.bind(this),_o)}return t.prototype.addObserver=function(e){~this.observers_.indexOf(e)||this.observers_.push(e),this.connected_||this.connect_()},t.prototype.removeObserver=function(e){var n=this.observers_,o=n.indexOf(e);~o&&n.splice(o,1),!n.length&&this.connected_&&this.disconnect_()},t.prototype.refresh=function(){var e=this.updateObservers_();e&&this.refresh()},t.prototype.updateObservers_=function(){var e=this.observers_.filter(function(n){return n.gatherActive(),n.hasActive()});return e.forEach(function(n){return n.broadcastActive()}),e.length>0},t.prototype.connect_=function(){!kt||this.connected_||(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),Po?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},t.prototype.disconnect_=function(){!kt||!this.connected_||(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},t.prototype.onTransitionEnd_=function(e){var n=e.propertyName,o=n===void 0?"":n,i=Co.some(function(r){return!!~o.indexOf(r)});i&&this.refresh()},t.getInstance=function(){return this.instance_||(this.instance_=new t),this.instance_},t.instance_=null,t}(),ln=function(t,e){for(var n=0,o=Object.keys(e);n<o.length;n++){var i=o[n];Object.defineProperty(t,i,{value:e[i],enumerable:!1,writable:!1,configurable:!0})}return t},at=function(t){var e=t&&t.ownerDocument&&t.ownerDocument.defaultView;return e||St},un=Rt(0,0,0,0);function $t(t){return parseFloat(t)||0}function pe(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return e.reduce(function(o,i){var r=t["border-"+i+"-width"];return o+$t(r)},0)}function To(t){for(var e=["top","right","bottom","left"],n={},o=0,i=e;o<i.length;o++){var r=i[o],s=t["padding-"+r];n[r]=$t(s)}return n}function So(t){var e=t.getBBox();return Rt(0,0,e.width,e.height)}function $o(t){var e=t.clientWidth,n=t.clientHeight;if(!e&&!n)return un;var o=at(t).getComputedStyle(t),i=To(o),r=i.left+i.right,s=i.top+i.bottom,a=$t(o.width),u=$t(o.height);if(o.boxSizing==="border-box"&&(Math.round(a+r)!==e&&(a-=pe(o,"left","right")+r),Math.round(u+s)!==n&&(u-=pe(o,"top","bottom")+s)),!Ao(t)){var l=Math.round(a+r)-e,f=Math.round(u+s)-n;Math.abs(l)!==1&&(a-=l),Math.abs(f)!==1&&(u-=f)}return Rt(i.left,i.top,a,u)}var Eo=function(){return typeof SVGGraphicsElement<"u"?function(t){return t instanceof at(t).SVGGraphicsElement}:function(t){return t instanceof at(t).SVGElement&&typeof t.getBBox=="function"}}();function Ao(t){return t===at(t).document.documentElement}function Mo(t){return kt?Eo(t)?So(t):$o(t):un}function Do(t){var e=t.x,n=t.y,o=t.width,i=t.height,r=typeof DOMRectReadOnly<"u"?DOMRectReadOnly:Object,s=Object.create(r.prototype);return ln(s,{x:e,y:n,width:o,height:i,top:n,right:e+o,bottom:i+n,left:e}),s}function Rt(t,e,n,o){return{x:t,y:e,width:n,height:o}}var Ro=function(){function t(e){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=Rt(0,0,0,0),this.target=e}return t.prototype.isActive=function(){var e=Mo(this.target);return this.contentRect_=e,e.width!==this.broadcastWidth||e.height!==this.broadcastHeight},t.prototype.broadcastRect=function(){var e=this.contentRect_;return this.broadcastWidth=e.width,this.broadcastHeight=e.height,e},t}(),No=function(){function t(e,n){var o=Do(n);ln(this,{target:e,contentRect:o})}return t}(),Lo=function(){function t(e,n,o){if(this.activeObservations_=[],this.observations_=new an,typeof e!="function")throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=e,this.controller_=n,this.callbackCtx_=o}return t.prototype.observe=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if(!(typeof Element>"u"||!(Element instanceof Object))){if(!(e instanceof at(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var n=this.observations_;n.has(e)||(n.set(e,new Ro(e)),this.controller_.addObserver(this),this.controller_.refresh())}},t.prototype.unobserve=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if(!(typeof Element>"u"||!(Element instanceof Object))){if(!(e instanceof at(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var n=this.observations_;n.has(e)&&(n.delete(e),n.size||this.controller_.removeObserver(this))}},t.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},t.prototype.gatherActive=function(){var e=this;this.clearActive(),this.observations_.forEach(function(n){n.isActive()&&e.activeObservations_.push(n)})},t.prototype.broadcastActive=function(){if(this.hasActive()){var e=this.callbackCtx_,n=this.activeObservations_.map(function(o){return new No(o.target,o.broadcastRect())});this.callback_.call(e,n,e),this.clearActive()}},t.prototype.clearActive=function(){this.activeObservations_.splice(0)},t.prototype.hasActive=function(){return this.activeObservations_.length>0},t}(),cn=typeof WeakMap<"u"?new WeakMap:new an,fn=function(){function t(e){if(!(this instanceof t))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var n=xo.getInstance(),o=new Lo(e,n,this);cn.set(this,o)}return t}();["observe","unobserve","disconnect"].forEach(function(t){fn.prototype[t]=function(){var e;return(e=cn.get(this))[t].apply(e,arguments)}});var pn=function(){return typeof St.ResizeObserver<"u"?St.ResizeObserver:fn}();const Gs=k({compatConfig:{MODE:3},name:"ResizeObserver",props:{disabled:Boolean,onResize:Function},emits:["resize"],setup(t,e){let{slots:n}=e;const o=Ln({width:0,height:0,offsetHeight:0,offsetWidth:0});let i=null,r=null;const s=()=>{r&&(r.disconnect(),r=null)},a=f=>{const{onResize:d}=t,p=f[0].target,{width:c,height:h}=p.getBoundingClientRect(),{offsetWidth:v,offsetHeight:O}=p,y=Math.floor(c),w=Math.floor(h);if(o.width!==y||o.height!==w||o.offsetWidth!==v||o.offsetHeight!==O){const _={width:y,height:w,offsetWidth:v,offsetHeight:O};m(o,_),d&&Promise.resolve().then(()=>{d(m(m({},_),{offsetWidth:v,offsetHeight:O}),p)})}},u=Bn(),l=()=>{const{disabled:f}=t;if(f){s();return}const d=ut(u);d!==i&&(s(),i=d),!r&&d&&(r=new pn(a),r.observe(d))};return Dt(()=>{l()}),Kt(()=>{l()}),qe(()=>{s()}),X(()=>t.disabled,()=>{l()},{flush:"post"}),()=>{var f;return(f=n.default)===null||f===void 0?void 0:f.call(n)[0]}}});let tt=!1;try{const t=Object.defineProperty({},"passive",{get(){tt=!0}});window.addEventListener("testPassive",null,t),window.removeEventListener("testPassive",null,t)}catch{}function ct(t,e,n,o){if(t&&t.addEventListener){let i=o;i===void 0&&tt&&(e==="touchstart"||e==="touchmove"||e==="wheel")&&(i={passive:!1}),t.addEventListener(e,n,i)}return{remove:()=>{t&&t.removeEventListener&&t.removeEventListener(e,n)}}}const Et=["blue","purple","cyan","green","magenta","pink","red","orange","yellow","volcano","geekblue","lime","gold"],Bo=(t,e,n,o,i)=>{const r=t/2,s=0,a=r,u=n*1/Math.sqrt(2),l=r-n*(1-1/Math.sqrt(2)),f=r-e*(1/Math.sqrt(2)),d=n*(Math.sqrt(2)-1)+e*(1/Math.sqrt(2)),p=2*r-f,c=d,h=2*r-u,v=l,O=2*r-s,y=a,w=r*Math.sqrt(2)+n*(Math.sqrt(2)-2),_=n*(Math.sqrt(2)-1);return{pointerEvents:"none",width:t,height:t,overflow:"hidden","&::after":{content:'""',position:"absolute",width:w,height:w,bottom:0,insetInline:0,margin:"auto",borderRadius:{_skip_check_:!0,value:`0 0 ${e}px 0`},transform:"translateY(50%) rotate(-135deg)",boxShadow:i,zIndex:0,background:"transparent"},"&::before":{position:"absolute",bottom:0,insetInlineStart:0,width:t,height:t/2,background:o,clipPath:{_multi_value_:!0,value:[`polygon(${_}px 100%, 50% ${_}px, ${2*r-_}px 100%, ${_}px 100%)`,`path('M ${s} ${a} A ${n} ${n} 0 0 0 ${u} ${l} L ${f} ${d} A ${e} ${e} 0 0 1 ${p} ${c} L ${h} ${v} A ${n} ${n} 0 0 0 ${O} ${y} Z')`]},content:'""'}}};function Ho(t,e){return Et.reduce((n,o)=>{const i=t[`${o}-1`],r=t[`${o}-3`],s=t[`${o}-6`],a=t[`${o}-7`];return m(m({},n),e(o,{lightColor:i,lightBorderColor:r,darkColor:s,textColor:a}))},{})}function qs(t,e){const n=m({},t);for(let o=0;o<e.length;o+=1){const i=e[o];delete n[i]}return n}function Io(){return""}function zo(t){return t?t.ownerDocument:window.document}function dn(){}const Vo=()=>({action:P.oneOfType([P.string,P.arrayOf(P.string)]).def([]),showAction:P.any.def([]),hideAction:P.any.def([]),getPopupClassNameFromAlign:P.any.def(Io),onPopupVisibleChange:Function,afterPopupVisibleChange:P.func.def(dn),popup:P.any,arrow:P.bool.def(!0),popupStyle:{type:Object,default:void 0},prefixCls:P.string.def("rc-trigger-popup"),popupClassName:P.string.def(""),popupPlacement:String,builtinPlacements:P.object,popupTransitionName:String,popupAnimation:P.any,mouseEnterDelay:P.number.def(0),mouseLeaveDelay:P.number.def(.1),zIndex:Number,focusDelay:P.number.def(0),blurDelay:P.number.def(.15),getPopupContainer:Function,getDocument:P.func.def(zo),forceRender:{type:Boolean,default:void 0},destroyPopupOnHide:{type:Boolean,default:!1},mask:{type:Boolean,default:!1},maskClosable:{type:Boolean,default:!0},popupAlign:P.object.def(()=>({})),popupVisible:{type:Boolean,default:void 0},defaultPopupVisible:{type:Boolean,default:!1},maskTransitionName:String,maskAnimation:String,stretch:String,alignPoint:{type:Boolean,default:void 0},autoDestroy:{type:Boolean,default:!1},mobile:Object,getTriggerDOMNode:Function}),Jt={visible:Boolean,prefixCls:String,zIndex:Number,destroyPopupOnHide:Boolean,forceRender:Boolean,arrow:{type:Boolean,default:!0},animation:[String,Object],transitionName:String,stretch:{type:String},align:{type:Object},point:{type:Object},getRootDomNode:{type:Function},getClassNameFromAlign:{type:Function},onAlign:{type:Function},onMouseenter:{type:Function},onMouseleave:{type:Function},onMousedown:{type:Function},onTouchstart:{type:Function}},Fo=m(m({},Jt),{mobile:{type:Object}}),Wo=m(m({},Jt),{mask:Boolean,mobile:{type:Object},maskAnimation:String,maskTransitionName:String});function hn(t){const{prefixCls:e,visible:n,zIndex:o,mask:i,maskAnimation:r,maskTransitionName:s}=t;if(!i)return null;let a={};return(s||r)&&(a=Ke({prefixCls:e,transitionName:s,animation:r})),T(Zt,q({appear:!0},a),{default:()=>[Ze(T("div",{style:{zIndex:o},class:`${e}-mask`},null),[[Hn("if"),n]])]})}hn.displayName="Mask";const jo=k({compatConfig:{MODE:3},name:"MobilePopupInner",inheritAttrs:!1,props:Fo,emits:["mouseenter","mouseleave","mousedown","touchstart","align"],setup(t,e){let{expose:n,slots:o}=e;const i=G();return n({forceAlign:()=>{},getElement:()=>i.value}),()=>{var r;const{zIndex:s,visible:a,prefixCls:u,mobile:{popupClassName:l,popupStyle:f,popupMotion:d={},popupRender:p}={}}=t,c=m({zIndex:s},f);let h=Qe((r=o.default)===null||r===void 0?void 0:r.call(o));h.length>1&&(h=T("div",{class:`${u}-content`},[h])),p&&(h=p(h));const v=rt(u,l);return T(Zt,q({ref:i},d),{default:()=>[a?T("div",{class:v,style:c},[h]):null]})}}});var Xo=function(t,e,n,o){function i(r){return r instanceof n?r:new n(function(s){s(r)})}return new(n||(n=Promise))(function(r,s){function a(f){try{l(o.next(f))}catch(d){s(d)}}function u(f){try{l(o.throw(f))}catch(d){s(d)}}function l(f){f.done?r(f.value):i(f.value).then(a,u)}l((o=o.apply(t,e||[])).next())})};const de=["measure","align",null,"motion"],ko=(t,e)=>{const n=S(null),o=S(),i=S(!1);function r(u){i.value||(n.value=u)}function s(){j.cancel(o.value)}function a(u){s(),o.value=j(()=>{let l=n.value;switch(n.value){case"align":l="motion";break;case"motion":l="stable";break}r(l),u==null||u()})}return X(t,()=>{r("measure")},{immediate:!0,flush:"post"}),Dt(()=>{X(n,()=>{switch(n.value){case"measure":e();break}n.value&&(o.value=j(()=>Xo(void 0,void 0,void 0,function*(){const u=de.indexOf(n.value),l=de[u+1];l&&u!==-1&&r(l)})))},{immediate:!0,flush:"post"})}),Je(()=>{i.value=!0,s()}),[n,a]},Yo=t=>{const e=S({width:0,height:0});function n(i){e.value={width:i.offsetWidth,height:i.offsetHeight}}return[H(()=>{const i={};if(t.value){const{width:r,height:s}=e.value;t.value.indexOf("height")!==-1&&s?i.height=`${s}px`:t.value.indexOf("minHeight")!==-1&&s&&(i.minHeight=`${s}px`),t.value.indexOf("width")!==-1&&r?i.width=`${r}px`:t.value.indexOf("minWidth")!==-1&&r&&(i.minWidth=`${r}px`)}return i}),n]};function he(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,o)}return n}function me(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?he(Object(n),!0).forEach(function(o){Uo(t,o,n[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):he(Object(n)).forEach(function(o){Object.defineProperty(t,o,Object.getOwnPropertyDescriptor(n,o))})}return t}function Yt(t){"@babel/helpers - typeof";return Yt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Yt(t)}function Uo(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var lt,Go={Webkit:"-webkit-",Moz:"-moz-",ms:"-ms-",O:"-o-"};function At(){if(lt!==void 0)return lt;lt="";var t=document.createElement("p").style,e="Transform";for(var n in Go)n+e in t&&(lt=n);return lt}function mn(){return At()?"".concat(At(),"TransitionProperty"):"transitionProperty"}function Nt(){return At()?"".concat(At(),"Transform"):"transform"}function ve(t,e){var n=mn();n&&(t.style[n]=e,n!=="transitionProperty"&&(t.style.transitionProperty=e))}function It(t,e){var n=Nt();n&&(t.style[n]=e,n!=="transform"&&(t.style.transform=e))}function qo(t){return t.style.transitionProperty||t.style[mn()]}function Ko(t){var e=window.getComputedStyle(t,null),n=e.getPropertyValue("transform")||e.getPropertyValue(Nt());if(n&&n!=="none"){var o=n.replace(/[^0-9\-.,]/g,"").split(",");return{x:parseFloat(o[12]||o[4],0),y:parseFloat(o[13]||o[5],0)}}return{x:0,y:0}}var Zo=/matrix\((.*)\)/,Qo=/matrix3d\((.*)\)/;function Jo(t,e){var n=window.getComputedStyle(t,null),o=n.getPropertyValue("transform")||n.getPropertyValue(Nt());if(o&&o!=="none"){var i,r=o.match(Zo);if(r)r=r[1],i=r.split(",").map(function(a){return parseFloat(a,10)}),i[4]=e.x,i[5]=e.y,It(t,"matrix(".concat(i.join(","),")"));else{var s=o.match(Qo)[1];i=s.split(",").map(function(a){return parseFloat(a,10)}),i[12]=e.x,i[13]=e.y,It(t,"matrix3d(".concat(i.join(","),")"))}}else It(t,"translateX(".concat(e.x,"px) translateY(").concat(e.y,"px) translateZ(0)"))}var ti=/[\-+]?(?:\d*\.|)\d+(?:[eE][\-+]?\d+|)/.source,vt;function ge(t){var e=t.style.display;t.style.display="none",t.offsetHeight,t.style.display=e}function it(t,e,n){var o=n;if(Yt(e)==="object"){for(var i in e)e.hasOwnProperty(i)&&it(t,i,e[i]);return}if(typeof o<"u"){typeof o=="number"&&(o="".concat(o,"px")),t.style[e]=o;return}return vt(t,e)}function ei(t){var e,n,o,i=t.ownerDocument,r=i.body,s=i&&i.documentElement;return e=t.getBoundingClientRect(),n=Math.floor(e.left),o=Math.floor(e.top),n-=s.clientLeft||r.clientLeft||0,o-=s.clientTop||r.clientTop||0,{left:n,top:o}}function vn(t,e){var n=t["page".concat(e?"Y":"X","Offset")],o="scroll".concat(e?"Top":"Left");if(typeof n!="number"){var i=t.document;n=i.documentElement[o],typeof n!="number"&&(n=i.body[o])}return n}function gn(t){return vn(t)}function yn(t){return vn(t,!0)}function dt(t){var e=ei(t),n=t.ownerDocument,o=n.defaultView||n.parentWindow;return e.left+=gn(o),e.top+=yn(o),e}function te(t){return t!=null&&t==t.window}function bn(t){return te(t)?t.document:t.nodeType===9?t:t.ownerDocument}function ni(t,e,n){var o=n,i="",r=bn(t);return o=o||r.defaultView.getComputedStyle(t,null),o&&(i=o.getPropertyValue(e)||o[e]),i}var oi=new RegExp("^(".concat(ti,")(?!px)[a-z%]+$"),"i"),ii=/^(top|right|bottom|left)$/,zt="currentStyle",Vt="runtimeStyle",Q="left",ri="px";function si(t,e){var n=t[zt]&&t[zt][e];if(oi.test(n)&&!ii.test(e)){var o=t.style,i=o[Q],r=t[Vt][Q];t[Vt][Q]=t[zt][Q],o[Q]=e==="fontSize"?"1em":n||0,n=o.pixelLeft+ri,o[Q]=i,t[Vt][Q]=r}return n===""?"auto":n}typeof window<"u"&&(vt=window.getComputedStyle?ni:si);function yt(t,e){return t==="left"?e.useCssRight?"right":t:e.useCssBottom?"bottom":t}function ye(t){if(t==="left")return"right";if(t==="right")return"left";if(t==="top")return"bottom";if(t==="bottom")return"top"}function be(t,e,n){it(t,"position")==="static"&&(t.style.position="relative");var o=-999,i=-999,r=yt("left",n),s=yt("top",n),a=ye(r),u=ye(s);r!=="left"&&(o=999),s!=="top"&&(i=999);var l="",f=dt(t);("left"in e||"top"in e)&&(l=qo(t)||"",ve(t,"none")),"left"in e&&(t.style[a]="",t.style[r]="".concat(o,"px")),"top"in e&&(t.style[u]="",t.style[s]="".concat(i,"px")),ge(t);var d=dt(t),p={};for(var c in e)if(e.hasOwnProperty(c)){var h=yt(c,n),v=c==="left"?o:i,O=f[c]-d[c];h===c?p[h]=v+O:p[h]=v-O}it(t,p),ge(t),("left"in e||"top"in e)&&ve(t,l);var y={};for(var w in e)if(e.hasOwnProperty(w)){var _=yt(w,n),$=e[w]-f[w];w===_?y[_]=p[_]+$:y[_]=p[_]-$}it(t,y)}function ai(t,e){var n=dt(t),o=Ko(t),i={x:o.x,y:o.y};"left"in e&&(i.x=o.x+e.left-n.left),"top"in e&&(i.y=o.y+e.top-n.top),Jo(t,i)}function li(t,e,n){if(n.ignoreShake){var o=dt(t),i=o.left.toFixed(0),r=o.top.toFixed(0),s=e.left.toFixed(0),a=e.top.toFixed(0);if(i===s&&r===a)return}n.useCssRight||n.useCssBottom?be(t,e,n):n.useCssTransform&&Nt()in document.body.style?ai(t,e):be(t,e,n)}function ee(t,e){for(var n=0;n<t.length;n++)e(t[n])}function wn(t){return vt(t,"boxSizing")==="border-box"}var ui=["margin","border","padding"],Ut=-1,ci=2,Gt=1,fi=0;function pi(t,e,n){var o={},i=t.style,r;for(r in e)e.hasOwnProperty(r)&&(o[r]=i[r],i[r]=e[r]);n.call(t);for(r in e)e.hasOwnProperty(r)&&(i[r]=o[r])}function ft(t,e,n){var o=0,i,r,s;for(r=0;r<e.length;r++)if(i=e[r],i)for(s=0;s<n.length;s++){var a=void 0;i==="border"?a="".concat(i).concat(n[s],"Width"):a=i+n[s],o+=parseFloat(vt(t,a))||0}return o}var W={getParent:function(e){var n=e;do n.nodeType===11&&n.host?n=n.host:n=n.parentNode;while(n&&n.nodeType!==1&&n.nodeType!==9);return n}};ee(["Width","Height"],function(t){W["doc".concat(t)]=function(e){var n=e.document;return Math.max(n.documentElement["scroll".concat(t)],n.body["scroll".concat(t)],W["viewport".concat(t)](n))},W["viewport".concat(t)]=function(e){var n="client".concat(t),o=e.document,i=o.body,r=o.documentElement,s=r[n];return o.compatMode==="CSS1Compat"&&s||i&&i[n]||s}});function we(t,e,n){var o=n;if(te(t))return e==="width"?W.viewportWidth(t):W.viewportHeight(t);if(t.nodeType===9)return e==="width"?W.docWidth(t):W.docHeight(t);var i=e==="width"?["Left","Right"]:["Top","Bottom"],r=Math.floor(e==="width"?t.getBoundingClientRect().width:t.getBoundingClientRect().height),s=wn(t),a=0;(r==null||r<=0)&&(r=void 0,a=vt(t,e),(a==null||Number(a)<0)&&(a=t.style[e]||0),a=Math.floor(parseFloat(a))||0),o===void 0&&(o=s?Gt:Ut);var u=r!==void 0||s,l=r||a;return o===Ut?u?l-ft(t,["border","padding"],i):a:u?o===Gt?l:l+(o===ci?-ft(t,["border"],i):ft(t,["margin"],i)):a+ft(t,ui.slice(o),i)}var di={position:"absolute",visibility:"hidden",display:"block"};function Oe(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var o,i=e[0];return i.offsetWidth!==0?o=we.apply(void 0,e):pi(i,di,function(){o=we.apply(void 0,e)}),o}ee(["width","height"],function(t){var e=t.charAt(0).toUpperCase()+t.slice(1);W["outer".concat(e)]=function(o,i){return o&&Oe(o,t,i?fi:Gt)};var n=t==="width"?["Left","Right"]:["Top","Bottom"];W[t]=function(o,i){var r=i;if(r!==void 0){if(o){var s=wn(o);return s&&(r+=ft(o,["padding","border"],n)),it(o,t,r)}return}return o&&Oe(o,t,Ut)}});function On(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}var b={getWindow:function(e){if(e&&e.document&&e.setTimeout)return e;var n=e.ownerDocument||e;return n.defaultView||n.parentWindow},getDocument:bn,offset:function(e,n,o){if(typeof n<"u")li(e,n,o||{});else return dt(e)},isWindow:te,each:ee,css:it,clone:function(e){var n,o={};for(n in e)e.hasOwnProperty(n)&&(o[n]=e[n]);var i=e.overflow;if(i)for(n in e)e.hasOwnProperty(n)&&(o.overflow[n]=e.overflow[n]);return o},mix:On,getWindowScrollLeft:function(e){return gn(e)},getWindowScrollTop:function(e){return yn(e)},merge:function(){for(var e={},n=0;n<arguments.length;n++)b.mix(e,n<0||arguments.length<=n?void 0:arguments[n]);return e},viewportWidth:0,viewportHeight:0};On(b,W);var Ft=b.getParent;function qt(t){if(b.isWindow(t)||t.nodeType===9)return null;var e=b.getDocument(t),n=e.body,o,i=b.css(t,"position"),r=i==="fixed"||i==="absolute";if(!r)return t.nodeName.toLowerCase()==="html"?null:Ft(t);for(o=Ft(t);o&&o!==n&&o.nodeType!==9;o=Ft(o))if(i=b.css(o,"position"),i!=="static")return o;return null}var _e=b.getParent;function hi(t){if(b.isWindow(t)||t.nodeType===9)return!1;var e=b.getDocument(t),n=e.body,o=null;for(o=_e(t);o&&o!==n&&o!==e;o=_e(o)){var i=b.css(o,"position");if(i==="fixed")return!0}return!1}function ne(t,e){for(var n={left:0,right:1/0,top:0,bottom:1/0},o=qt(t),i=b.getDocument(t),r=i.defaultView||i.parentWindow,s=i.body,a=i.documentElement;o;){if((navigator.userAgent.indexOf("MSIE")===-1||o.clientWidth!==0)&&o!==s&&o!==a&&b.css(o,"overflow")!=="visible"){var u=b.offset(o);u.left+=o.clientLeft,u.top+=o.clientTop,n.top=Math.max(n.top,u.top),n.right=Math.min(n.right,u.left+o.clientWidth),n.bottom=Math.min(n.bottom,u.top+o.clientHeight),n.left=Math.max(n.left,u.left)}else if(o===s||o===a)break;o=qt(o)}var l=null;if(!b.isWindow(t)&&t.nodeType!==9){l=t.style.position;var f=b.css(t,"position");f==="absolute"&&(t.style.position="fixed")}var d=b.getWindowScrollLeft(r),p=b.getWindowScrollTop(r),c=b.viewportWidth(r),h=b.viewportHeight(r),v=a.scrollWidth,O=a.scrollHeight,y=window.getComputedStyle(s);if(y.overflowX==="hidden"&&(v=r.innerWidth),y.overflowY==="hidden"&&(O=r.innerHeight),t.style&&(t.style.position=l),e||hi(t))n.left=Math.max(n.left,d),n.top=Math.max(n.top,p),n.right=Math.min(n.right,d+c),n.bottom=Math.min(n.bottom,p+h);else{var w=Math.max(v,d+c);n.right=Math.min(n.right,w);var _=Math.max(O,p+h);n.bottom=Math.min(n.bottom,_)}return n.top>=0&&n.left>=0&&n.bottom>n.top&&n.right>n.left?n:null}function mi(t,e,n,o){var i=b.clone(t),r={width:e.width,height:e.height};return o.adjustX&&i.left<n.left&&(i.left=n.left),o.resizeWidth&&i.left>=n.left&&i.left+r.width>n.right&&(r.width-=i.left+r.width-n.right),o.adjustX&&i.left+r.width>n.right&&(i.left=Math.max(n.right-r.width,n.left)),o.adjustY&&i.top<n.top&&(i.top=n.top),o.resizeHeight&&i.top>=n.top&&i.top+r.height>n.bottom&&(r.height-=i.top+r.height-n.bottom),o.adjustY&&i.top+r.height>n.bottom&&(i.top=Math.max(n.bottom-r.height,n.top)),b.mix(i,r)}function oe(t){var e,n,o;if(!b.isWindow(t)&&t.nodeType!==9)e=b.offset(t),n=b.outerWidth(t),o=b.outerHeight(t);else{var i=b.getWindow(t);e={left:b.getWindowScrollLeft(i),top:b.getWindowScrollTop(i)},n=b.viewportWidth(i),o=b.viewportHeight(i)}return e.width=n,e.height=o,e}function Ce(t,e){var n=e.charAt(0),o=e.charAt(1),i=t.width,r=t.height,s=t.left,a=t.top;return n==="c"?a+=r/2:n==="b"&&(a+=r),o==="c"?s+=i/2:o==="r"&&(s+=i),{left:s,top:a}}function bt(t,e,n,o,i){var r=Ce(e,n[1]),s=Ce(t,n[0]),a=[s.left-r.left,s.top-r.top];return{left:Math.round(t.left-a[0]+o[0]-i[0]),top:Math.round(t.top-a[1]+o[1]-i[1])}}function Pe(t,e,n){return t.left<n.left||t.left+e.width>n.right}function xe(t,e,n){return t.top<n.top||t.top+e.height>n.bottom}function vi(t,e,n){return t.left>n.right||t.left+e.width<n.left}function gi(t,e,n){return t.top>n.bottom||t.top+e.height<n.top}function wt(t,e,n){var o=[];return b.each(t,function(i){o.push(i.replace(e,function(r){return n[r]}))}),o}function Ot(t,e){return t[e]=-t[e],t}function Te(t,e){var n;return/%$/.test(t)?n=parseInt(t.substring(0,t.length-1),10)/100*e:n=parseInt(t,10),n||0}function Se(t,e){t[0]=Te(t[0],e.width),t[1]=Te(t[1],e.height)}function _n(t,e,n,o){var i=n.points,r=n.offset||[0,0],s=n.targetOffset||[0,0],a=n.overflow,u=n.source||t;r=[].concat(r),s=[].concat(s),a=a||{};var l={},f=0,d=!!(a&&a.alwaysByViewport),p=ne(u,d),c=oe(u);Se(r,c),Se(s,e);var h=bt(c,e,i,r,s),v=b.merge(c,h);if(p&&(a.adjustX||a.adjustY)&&o){if(a.adjustX&&Pe(h,c,p)){var O=wt(i,/[lr]/gi,{l:"r",r:"l"}),y=Ot(r,0),w=Ot(s,0),_=bt(c,e,O,y,w);vi(_,c,p)||(f=1,i=O,r=y,s=w)}if(a.adjustY&&xe(h,c,p)){var $=wt(i,/[tb]/gi,{t:"b",b:"t"}),N=Ot(r,1),x=Ot(s,1),B=bt(c,e,$,N,x);gi(B,c,p)||(f=1,i=$,r=N,s=x)}f&&(h=bt(c,e,i,r,s),b.mix(v,h));var A=Pe(h,c,p),D=xe(h,c,p);if(A||D){var g=i;A&&(g=wt(i,/[lr]/gi,{l:"r",r:"l"})),D&&(g=wt(i,/[tb]/gi,{t:"b",b:"t"})),i=g,r=n.offset||[0,0],s=n.targetOffset||[0,0]}l.adjustX=a.adjustX&&A,l.adjustY=a.adjustY&&D,(l.adjustX||l.adjustY)&&(v=mi(h,c,p,l))}return v.width!==c.width&&b.css(u,"width",b.width(u)+v.width-c.width),v.height!==c.height&&b.css(u,"height",b.height(u)+v.height-c.height),b.offset(u,{left:v.left,top:v.top},{useCssRight:n.useCssRight,useCssBottom:n.useCssBottom,useCssTransform:n.useCssTransform,ignoreShake:n.ignoreShake}),{points:i,offset:r,targetOffset:s,overflow:l}}function yi(t,e){var n=ne(t,e),o=oe(t);return!n||o.left+o.width<=n.left||o.top+o.height<=n.top||o.left>=n.right||o.top>=n.bottom}function ie(t,e,n){var o=n.target||e,i=oe(o),r=!yi(o,n.overflow&&n.overflow.alwaysByViewport);return _n(t,i,n,r)}ie.__getOffsetParent=qt;ie.__getVisibleRectForElement=ne;function bi(t,e,n){var o,i,r=b.getDocument(t),s=r.defaultView||r.parentWindow,a=b.getWindowScrollLeft(s),u=b.getWindowScrollTop(s),l=b.viewportWidth(s),f=b.viewportHeight(s);"pageX"in e?o=e.pageX:o=a+e.clientX,"pageY"in e?i=e.pageY:i=u+e.clientY;var d={left:o,top:i,width:0,height:0},p=o>=0&&o<=a+l&&i>=0&&i<=u+f,c=[n.points[0],"cc"];return _n(t,d,me(me({},n),{},{points:c}),p)}function ht(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1,i=t;if(Array.isArray(t)&&(i=Qt(t)[0]),!i)return null;const r=tn(i,e,o);return r.props=n?m(m({},r.props),e):r.props,In(typeof r.props.class!="object"),r}function Ks(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;return t.map(o=>ht(o,e,n))}function Zs(t,e,n){Fn(tn(t,m({},e)),n)}const Cn=t=>(t||[]).some(e=>zn(e)?!(e.type===Vn||e.type===en&&!Cn(e.children)):!0)?t:null;function Qs(t,e,n,o){var i;const r=(i=t[e])===null||i===void 0?void 0:i.call(t,n);return Cn(r)?r:o==null?void 0:o()}function wi(t,e){return t===e?!0:!t||!e?!1:"pageX"in e&&"pageY"in e?t.pageX===e.pageX&&t.pageY===e.pageY:"clientX"in e&&"clientY"in e?t.clientX===e.clientX&&t.clientY===e.clientY:!1}function Oi(t,e){t!==document.activeElement&&ot(e,t)&&typeof t.focus=="function"&&t.focus()}function $e(t,e){let n=null,o=null;function i(s){let[{target:a}]=s;if(!document.documentElement.contains(a))return;const{width:u,height:l}=a.getBoundingClientRect(),f=Math.floor(u),d=Math.floor(l);(n!==f||o!==d)&&Promise.resolve().then(()=>{e({width:f,height:d})}),n=f,o=d}const r=new pn(i);return t&&r.observe(t),()=>{r.disconnect()}}const _i=(t,e)=>{let n=!1,o=null;function i(){clearTimeout(o)}function r(s){if(!n||s===!0){if(t()===!1)return;n=!0,i(),o=setTimeout(()=>{n=!1},e.value)}else i(),o=setTimeout(()=>{n=!1,r()},e.value)}return[r,()=>{n=!1,i()}]};function Ci(){this.__data__=[],this.size=0}function Pn(t,e){return t===e||t!==t&&e!==e}function Lt(t,e){for(var n=t.length;n--;)if(Pn(t[n][0],e))return n;return-1}var Pi=Array.prototype,xi=Pi.splice;function Ti(t){var e=this.__data__,n=Lt(e,t);if(n<0)return!1;var o=e.length-1;return n==o?e.pop():xi.call(e,n,1),--this.size,!0}function Si(t){var e=this.__data__,n=Lt(e,t);return n<0?void 0:e[n][1]}function $i(t){return Lt(this.__data__,t)>-1}function Ei(t,e){var n=this.__data__,o=Lt(n,t);return o<0?(++this.size,n.push([t,e])):n[o][1]=e,this}function Y(t){var e=-1,n=t==null?0:t.length;for(this.clear();++e<n;){var o=t[e];this.set(o[0],o[1])}}Y.prototype.clear=Ci;Y.prototype.delete=Ti;Y.prototype.get=Si;Y.prototype.has=$i;Y.prototype.set=Ei;function Ai(){this.__data__=new Y,this.size=0}function Mi(t){var e=this.__data__,n=e.delete(t);return this.size=e.size,n}function Di(t){return this.__data__.get(t)}function Ri(t){return this.__data__.has(t)}var mt=fo(Object,"create");function Ni(){this.__data__=mt?mt(null):{},this.size=0}function Li(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}var Bi="__lodash_hash_undefined__",Hi=Object.prototype,Ii=Hi.hasOwnProperty;function zi(t){var e=this.__data__;if(mt){var n=e[t];return n===Bi?void 0:n}return Ii.call(e,t)?e[t]:void 0}var Vi=Object.prototype,Fi=Vi.hasOwnProperty;function Wi(t){var e=this.__data__;return mt?e[t]!==void 0:Fi.call(e,t)}var ji="__lodash_hash_undefined__";function Xi(t,e){var n=this.__data__;return this.size+=this.has(t)?0:1,n[t]=mt&&e===void 0?ji:e,this}function et(t){var e=-1,n=t==null?0:t.length;for(this.clear();++e<n;){var o=t[e];this.set(o[0],o[1])}}et.prototype.clear=Ni;et.prototype.delete=Li;et.prototype.get=zi;et.prototype.has=Wi;et.prototype.set=Xi;function ki(){this.size=0,this.__data__={hash:new et,map:new(rn||Y),string:new et}}function Yi(t){var e=typeof t;return e=="string"||e=="number"||e=="symbol"||e=="boolean"?t!=="__proto__":t===null}function Bt(t,e){var n=t.__data__;return Yi(e)?n[typeof e=="string"?"string":"hash"]:n.map}function Ui(t){var e=Bt(this,t).delete(t);return this.size-=e?1:0,e}function Gi(t){return Bt(this,t).get(t)}function qi(t){return Bt(this,t).has(t)}function Ki(t,e){var n=Bt(this,t),o=n.size;return n.set(t,e),this.size+=n.size==o?0:1,this}function nt(t){var e=-1,n=t==null?0:t.length;for(this.clear();++e<n;){var o=t[e];this.set(o[0],o[1])}}nt.prototype.clear=ki;nt.prototype.delete=Ui;nt.prototype.get=Gi;nt.prototype.has=qi;nt.prototype.set=Ki;var Zi=200;function Qi(t,e){var n=this.__data__;if(n instanceof Y){var o=n.__data__;if(!rn||o.length<Zi-1)return o.push([t,e]),this.size=++n.size,this;n=this.__data__=new nt(o)}return n.set(t,e),this.size=n.size,this}function K(t){var e=this.__data__=new Y(t);this.size=e.size}K.prototype.clear=Ai;K.prototype.delete=Mi;K.prototype.get=Di;K.prototype.has=Ri;K.prototype.set=Qi;var Ji="__lodash_hash_undefined__";function tr(t){return this.__data__.set(t,Ji),this}function er(t){return this.__data__.has(t)}function Mt(t){var e=-1,n=t==null?0:t.length;for(this.__data__=new nt;++e<n;)this.add(t[e])}Mt.prototype.add=Mt.prototype.push=tr;Mt.prototype.has=er;function nr(t,e){for(var n=-1,o=t==null?0:t.length;++n<o;)if(e(t[n],n,t))return!0;return!1}function or(t,e){return t.has(e)}var ir=1,rr=2;function xn(t,e,n,o,i,r){var s=n&ir,a=t.length,u=e.length;if(a!=u&&!(s&&u>a))return!1;var l=r.get(t),f=r.get(e);if(l&&f)return l==e&&f==t;var d=-1,p=!0,c=n&rr?new Mt:void 0;for(r.set(t,e),r.set(e,t);++d<a;){var h=t[d],v=e[d];if(o)var O=s?o(v,h,d,e,t,r):o(h,v,d,t,e,r);if(O!==void 0){if(O)continue;p=!1;break}if(c){if(!nr(e,function(y,w){if(!or(c,w)&&(h===y||i(h,y,n,o,r)))return c.push(w)})){p=!1;break}}else if(!(h===v||i(h,v,n,o,r))){p=!1;break}}return r.delete(t),r.delete(e),p}var Ee=po.Uint8Array;function sr(t){var e=-1,n=Array(t.size);return t.forEach(function(o,i){n[++e]=[i,o]}),n}function ar(t){var e=-1,n=Array(t.size);return t.forEach(function(o){n[++e]=o}),n}var lr=1,ur=2,cr="[object Boolean]",fr="[object Date]",pr="[object Error]",dr="[object Map]",hr="[object Number]",mr="[object RegExp]",vr="[object Set]",gr="[object String]",yr="[object Symbol]",br="[object ArrayBuffer]",wr="[object DataView]",Ae=ue?ue.prototype:void 0,Wt=Ae?Ae.valueOf:void 0;function Or(t,e,n,o,i,r,s){switch(n){case wr:if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case br:return!(t.byteLength!=e.byteLength||!r(new Ee(t),new Ee(e)));case cr:case fr:case hr:return Pn(+t,+e);case pr:return t.name==e.name&&t.message==e.message;case mr:case gr:return t==e+"";case dr:var a=sr;case vr:var u=o&lr;if(a||(a=ar),t.size!=e.size&&!u)return!1;var l=s.get(t);if(l)return l==e;o|=ur,s.set(t,e);var f=xn(a(t),a(e),o,i,r,s);return s.delete(t),f;case yr:if(Wt)return Wt.call(t)==Wt.call(e)}return!1}function _r(t,e){for(var n=-1,o=e.length,i=t.length;++n<o;)t[i+n]=e[n];return t}function Cr(t,e,n){var o=e(t);return Tt(t)?o:_r(o,n(t))}function Pr(t,e){for(var n=-1,o=t==null?0:t.length,i=0,r=[];++n<o;){var s=t[n];e(s,n,t)&&(r[i++]=s)}return r}function xr(){return[]}var Tr=Object.prototype,Sr=Tr.propertyIsEnumerable,Me=Object.getOwnPropertySymbols,$r=Me?function(t){return t==null?[]:(t=Object(t),Pr(Me(t),function(e){return Sr.call(t,e)}))}:xr;function Er(t,e){for(var n=-1,o=Array(t);++n<t;)o[n]=e(n);return o}var Ar=9007199254740991,Mr=/^(?:0|[1-9]\d*)$/;function Dr(t,e){var n=typeof t;return e=e??Ar,!!e&&(n=="number"||n!="symbol"&&Mr.test(t))&&t>-1&&t%1==0&&t<e}var Rr=Object.prototype,Nr=Rr.hasOwnProperty;function Lr(t,e){var n=Tt(t),o=!n&&ho(t),i=!n&&!o&&Xt(t),r=!n&&!o&&!i&&sn(t),s=n||o||i||r,a=s?Er(t.length,String):[],u=a.length;for(var l in t)(e||Nr.call(t,l))&&!(s&&(l=="length"||i&&(l=="offset"||l=="parent")||r&&(l=="buffer"||l=="byteLength"||l=="byteOffset")||Dr(l,u)))&&a.push(l);return a}function Br(t){return vo(t)?Lr(t):mo(t)}function De(t){return Cr(t,Br,$r)}var Hr=1,Ir=Object.prototype,zr=Ir.hasOwnProperty;function Vr(t,e,n,o,i,r){var s=n&Hr,a=De(t),u=a.length,l=De(e),f=l.length;if(u!=f&&!s)return!1;for(var d=u;d--;){var p=a[d];if(!(s?p in e:zr.call(e,p)))return!1}var c=r.get(t),h=r.get(e);if(c&&h)return c==e&&h==t;var v=!0;r.set(t,e),r.set(e,t);for(var O=s;++d<u;){p=a[d];var y=t[p],w=e[p];if(o)var _=s?o(w,y,p,e,t,r):o(y,w,p,t,e,r);if(!(_===void 0?y===w||i(y,w,n,o,r):_)){v=!1;break}O||(O=p=="constructor")}if(v&&!O){var $=t.constructor,N=e.constructor;$!=N&&"constructor"in t&&"constructor"in e&&!(typeof $=="function"&&$ instanceof $&&typeof N=="function"&&N instanceof N)&&(v=!1)}return r.delete(t),r.delete(e),v}var Fr=1,Re="[object Arguments]",Ne="[object Array]",_t="[object Object]",Wr=Object.prototype,Le=Wr.hasOwnProperty;function jr(t,e,n,o,i,r){var s=Tt(t),a=Tt(e),u=s?Ne:ce(t),l=a?Ne:ce(e);u=u==Re?_t:u,l=l==Re?_t:l;var f=u==_t,d=l==_t,p=u==l;if(p&&Xt(t)){if(!Xt(e))return!1;s=!0,f=!1}if(p&&!f)return r||(r=new K),s||sn(t)?xn(t,e,n,o,i,r):Or(t,e,u,n,o,i,r);if(!(n&Fr)){var c=f&&Le.call(t,"__wrapped__"),h=d&&Le.call(e,"__wrapped__");if(c||h){var v=c?t.value():t,O=h?e.value():e;return r||(r=new K),i(v,O,n,o,r)}}return p?(r||(r=new K),Vr(t,e,n,o,i,r)):!1}function Tn(t,e,n,o,i){return t===e?!0:t==null||e==null||!fe(t)&&!fe(e)?t!==t&&e!==e:jr(t,e,n,o,Tn,i)}function Xr(t,e){return Tn(t,e)}const kr={align:Object,target:[Object,Function],onAlign:Function,monitorBufferTime:Number,monitorWindowResize:Boolean,disabled:Boolean};function Be(t){return typeof t!="function"?null:t()}function He(t){return typeof t!="object"||!t?null:t}const Yr=k({compatConfig:{MODE:3},name:"Align",props:kr,emits:["align"],setup(t,e){let{expose:n,slots:o}=e;const i=G({}),r=G(),[s,a]=_i(()=>{const{disabled:p,target:c,align:h,onAlign:v}=t;if(!p&&c&&r.value){const O=r.value;let y;const w=Be(c),_=He(c);i.value.element=w,i.value.point=_,i.value.align=h;const{activeElement:$}=document;return w&&go(w)?y=ie(O,w,h):_&&(y=bi(O,_,h)),Oi($,O),v&&y&&v(O,y),!0}return!1},H(()=>t.monitorBufferTime)),u=G({cancel:()=>{}}),l=G({cancel:()=>{}}),f=()=>{const p=t.target,c=Be(p),h=He(p);r.value!==l.value.element&&(l.value.cancel(),l.value.element=r.value,l.value.cancel=$e(r.value,s)),(i.value.element!==c||!wi(i.value.point,h)||!Xr(i.value.align,t.align))&&(s(),u.value.element!==c&&(u.value.cancel(),u.value.element=c,u.value.cancel=$e(c,s)))};Dt(()=>{pt(()=>{f()})}),Kt(()=>{pt(()=>{f()})}),X(()=>t.disabled,p=>{p?a():s()},{immediate:!0,flush:"post"});const d=G(null);return X(()=>t.monitorWindowResize,p=>{p?d.value||(d.value=ct(window,"resize",s)):d.value&&(d.value.remove(),d.value=null)},{flush:"post"}),qe(()=>{u.value.cancel(),l.value.cancel(),d.value&&d.value.remove(),a()}),n({forceAlign:()=>s(!0)}),()=>{const p=o==null?void 0:o.default();return p?ht(p[0],{ref:r},!0,!0):null}}}),Ur=k({compatConfig:{MODE:3},name:"PopupInner",inheritAttrs:!1,props:Jt,emits:["mouseenter","mouseleave","mousedown","touchstart","align"],setup(t,e){let{expose:n,attrs:o,slots:i}=e;const r=S(),s=S(),a=S(),[u,l]=Yo(Wn(t,"stretch")),f=()=>{t.stretch&&l(t.getRootDomNode())},d=S(!1);let p;X(()=>t.visible,x=>{clearTimeout(p),x?p=setTimeout(()=>{d.value=t.visible}):d.value=!1},{immediate:!0});const[c,h]=ko(d,f),v=S(),O=()=>t.point?t.point:t.getRootDomNode,y=()=>{var x;(x=r.value)===null||x===void 0||x.forceAlign()},w=(x,B)=>{var A;const D=t.getClassNameFromAlign(B),g=a.value;a.value!==D&&(a.value=D),c.value==="align"&&(g!==D?Promise.resolve().then(()=>{y()}):h(()=>{var C;(C=v.value)===null||C===void 0||C.call(v)}),(A=t.onAlign)===null||A===void 0||A.call(t,x,B))},_=H(()=>{const x=typeof t.animation=="object"?t.animation:Ke(t);return["onAfterEnter","onAfterLeave"].forEach(B=>{const A=x[B];x[B]=D=>{h(),c.value="stable",A==null||A(D)}}),x}),$=()=>new Promise(x=>{v.value=x});X([_,c],()=>{!_.value&&c.value==="motion"&&h()},{immediate:!0}),n({forceAlign:y,getElement:()=>s.value.$el||s.value});const N=H(()=>{var x;return!(!((x=t.align)===null||x===void 0)&&x.points&&(c.value==="align"||c.value==="stable"))});return()=>{var x;const{zIndex:B,align:A,prefixCls:D,destroyPopupOnHide:g,onMouseenter:C,onMouseleave:F,onTouchstart:M=()=>{},onMousedown:L}=t,E=c.value,R=[m(m({},u.value),{zIndex:B,opacity:E==="motion"||E==="stable"||!d.value?null:0,pointerEvents:!d.value&&E!=="stable"?"none":null}),o.style];let Z=Qe((x=i.default)===null||x===void 0?void 0:x.call(i,{visible:t.visible}));Z.length>1&&(Z=T("div",{class:`${D}-content`},[Z]));const U=rt(D,o.class,a.value,!t.arrow&&`${D}-arrow-hidden`),Ht=d.value||!t.visible?jn(_.value.name,_.value):{};return T(Zt,q(q({ref:s},Ht),{},{onBeforeEnter:$}),{default:()=>!g||t.visible?Ze(T(Yr,{target:O(),key:"popup",ref:r,monitorWindowResize:!0,disabled:N.value,align:A,onAlign:w},{default:()=>T("div",{class:U,onMouseenter:C,onMouseleave:F,onMousedown:se(L,["capture"]),[tt?"onTouchstartPassive":"onTouchstart"]:se(M,["capture"]),style:R},[Z])}),[[Xn,d.value]]):null})}}}),Gr=k({compatConfig:{MODE:3},name:"Popup",inheritAttrs:!1,props:Wo,setup(t,e){let{attrs:n,slots:o,expose:i}=e;const r=S(!1),s=S(!1),a=S(),u=S();return X([()=>t.visible,()=>t.mobile],()=>{r.value=t.visible,t.visible&&t.mobile&&(s.value=!0)},{immediate:!0,flush:"post"}),i({forceAlign:()=>{var l;(l=a.value)===null||l===void 0||l.forceAlign()},getElement:()=>{var l;return(l=a.value)===null||l===void 0?void 0:l.getElement()}}),()=>{const l=m(m(m({},t),n),{visible:r.value}),f=s.value?T(jo,q(q({},l),{},{mobile:t.mobile,ref:a}),{default:o.default}):T(Ur,q(q({},l),{},{ref:a}),{default:o.default});return T("div",{ref:u},[T(hn,l,null),f])}}});function qr(t,e,n){return n?t[0]===e[0]:t[0]===e[0]&&t[1]===e[1]}function Ie(t,e,n){const o=t[e]||{};return m(m({},o),n)}function Kr(t,e,n,o){const{points:i}=n,r=Object.keys(t);for(let s=0;s<r.length;s+=1){const a=r[s];if(qr(t[a].points,i,o))return`${e}-placement-${a}`}return""}const Zr={methods:{setState(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0,n=typeof t=="function"?t(this.$data,this.$props):t;if(this.getDerivedStateFromProps){const o=this.getDerivedStateFromProps(kn(this),m(m({},this.$data),n));if(o===null)return;n=m(m({},n),o||{})}m(this.$data,n),this._.isMounted&&this.$forceUpdate(),pt(()=>{e&&e()})},__emit(){const t=[].slice.call(arguments,0);let e=t[0];e=`on${e[0].toUpperCase()}${e.substring(1)}`;const n=this.$props[e]||this.$attrs[e];if(t.length&&n)if(Array.isArray(n))for(let o=0,i=n.length;o<i;o++)n[o](...t.slice(1));else n(...t.slice(1))}}};let jt;function Sn(t){if(typeof document>"u")return 0;if(jt===void 0){const e=document.createElement("div");e.style.width="100%",e.style.height="200px";const n=document.createElement("div"),o=n.style;o.position="absolute",o.top="0",o.left="0",o.pointerEvents="none",o.visibility="hidden",o.width="200px",o.height="150px",o.overflow="hidden",n.appendChild(e),document.body.appendChild(n);const i=e.offsetWidth;n.style.overflow="scroll";let r=e.offsetWidth;i===r&&(r=n.clientWidth),document.body.removeChild(n),jt=i-r}return jt}function ze(t){const e=t.match(/^(.*)px$/),n=Number(e==null?void 0:e[1]);return Number.isNaN(n)?Sn():n}function Js(t){if(typeof document>"u"||!t||!(t instanceof Element))return{width:0,height:0};const{width:e,height:n}=getComputedStyle(t,"::-webkit-scrollbar");return{width:ze(e),height:ze(n)}}const Qr=`vc-util-locker-${Date.now()}`;let Ve=0;function Jr(){return document.body.scrollHeight>(window.innerHeight||document.documentElement.clientHeight)&&window.innerWidth>document.body.offsetWidth}function ts(t){const e=H(()=>!!t&&!!t.value);Ve+=1;const n=`${Qr}_${Ve}`;nn(o=>{if(st()){if(e.value){const i=Sn(),r=Jr();Yn(`
html body {
  overflow-y: hidden;
  ${r?`width: calc(100% - ${i}px);`:""}
}`,n)}else ae(n);o(()=>{ae(n)})}},{flush:"post"})}let J=0;const xt=st(),Fe=t=>{if(!xt)return null;if(t){if(typeof t=="string")return document.querySelectorAll(t)[0];if(typeof t=="function")return t();if(typeof t=="object"&&t instanceof window.HTMLElement)return t}return document.body},es=k({compatConfig:{MODE:3},name:"PortalWrapper",inheritAttrs:!1,props:{wrapperClassName:String,forceRender:{type:Boolean,default:void 0},getContainer:P.any,visible:{type:Boolean,default:void 0},autoLock:Un(),didUpdate:Function},setup(t,e){let{slots:n}=e;const o=S(),i=S(),r=S(),s=S(1),a=st()&&document.createElement("div"),u=()=>{var c,h;o.value===a&&((h=(c=o.value)===null||c===void 0?void 0:c.parentNode)===null||h===void 0||h.removeChild(o.value)),o.value=null};let l=null;const f=function(){return(arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1)||o.value&&!o.value.parentNode?(l=Fe(t.getContainer),l?(l.appendChild(o.value),!0):!1):!0},d=()=>xt?(o.value||(o.value=a,f(!0)),p(),o.value):null,p=()=>{const{wrapperClassName:c}=t;o.value&&c&&c!==o.value.className&&(o.value.className=c)};return Kt(()=>{p(),f()}),ts(H(()=>t.autoLock&&t.visible&&st()&&(o.value===document.body||o.value===a))),Dt(()=>{let c=!1;X([()=>t.visible,()=>t.getContainer],(h,v)=>{let[O,y]=h,[w,_]=v;xt&&(l=Fe(t.getContainer),l===document.body&&(O&&!w?J+=1:c&&(J-=1))),c&&(typeof y=="function"&&typeof _=="function"?y.toString()!==_.toString():y!==_)&&u(),c=!0},{immediate:!0,flush:"post"}),pt(()=>{f()||(r.value=j(()=>{s.value+=1}))})}),Je(()=>{const{visible:c}=t;xt&&l===document.body&&(J=c&&J?J-1:J),u(),j.cancel(r.value)}),()=>{const{forceRender:c,visible:h}=t;let v=null;const O={getOpenCount:()=>J,getContainer:d};return s.value&&(c||h||i.value)&&(v=T(Gn,{getContainer:d,ref:i,didUpdate:t.didUpdate},{default:()=>{var y;return(y=n.default)===null||y===void 0?void 0:y.call(n,O)}})),v}}}),ns=["onClick","onMousedown","onTouchstart","onMouseenter","onMouseleave","onFocus","onBlur","onContextmenu"],os=k({compatConfig:{MODE:3},name:"Trigger",mixins:[Zr],inheritAttrs:!1,props:Vo(),setup(t){const e=H(()=>{const{popupPlacement:i,popupAlign:r,builtinPlacements:s}=t;return i&&s?Ie(s,i,r):r}),n=S(null),o=i=>{n.value=i};return{vcTriggerContext:to("vcTriggerContext",{}),popupRef:n,setPopupRef:o,triggerRef:S(null),align:e,focusTime:null,clickOutsideHandler:null,contextmenuOutsideHandler1:null,contextmenuOutsideHandler2:null,touchOutsideHandler:null,attachId:null,delayTimer:null,hasPopupMouseDown:!1,preClickTime:null,preTouchTime:null,mouseDownTimeout:null,childOriginEvents:{}}},data(){const t=this.$props;let e;return this.popupVisible!==void 0?e=!!t.popupVisible:e=!!t.defaultPopupVisible,ns.forEach(n=>{this[`fire${n}`]=o=>{this.fireEvents(n,o)}}),{prevPopupVisible:e,sPopupVisible:e,point:null}},watch:{popupVisible(t){t!==void 0&&(this.prevPopupVisible=this.sPopupVisible,this.sPopupVisible=t)}},created(){Qn("vcTriggerContext",{onPopupMouseDown:this.onPopupMouseDown,onPopupMouseenter:this.onPopupMouseenter,onPopupMouseleave:this.onPopupMouseleave}),Jn(this)},deactivated(){this.setPopupVisible(!1)},mounted(){this.$nextTick(()=>{this.updatedCal()})},updated(){this.$nextTick(()=>{this.updatedCal()})},beforeUnmount(){this.clearDelayTimer(),this.clearOutsideHandler(),clearTimeout(this.mouseDownTimeout),j.cancel(this.attachId)},methods:{updatedCal(){const t=this.$props;if(this.$data.sPopupVisible){let n;!this.clickOutsideHandler&&(this.isClickToHide()||this.isContextmenuToShow())&&(n=t.getDocument(this.getRootDomNode()),this.clickOutsideHandler=ct(n,"mousedown",this.onDocumentClick)),this.touchOutsideHandler||(n=n||t.getDocument(this.getRootDomNode()),this.touchOutsideHandler=ct(n,"touchstart",this.onDocumentClick,tt?{passive:!1}:!1)),!this.contextmenuOutsideHandler1&&this.isContextmenuToShow()&&(n=n||t.getDocument(this.getRootDomNode()),this.contextmenuOutsideHandler1=ct(n,"scroll",this.onContextmenuClose)),!this.contextmenuOutsideHandler2&&this.isContextmenuToShow()&&(this.contextmenuOutsideHandler2=ct(window,"blur",this.onContextmenuClose))}else this.clearOutsideHandler()},onMouseenter(t){const{mouseEnterDelay:e}=this.$props;this.fireEvents("onMouseenter",t),this.delaySetPopupVisible(!0,e,e?null:t)},onMouseMove(t){this.fireEvents("onMousemove",t),this.setPoint(t)},onMouseleave(t){this.fireEvents("onMouseleave",t),this.delaySetPopupVisible(!1,this.$props.mouseLeaveDelay)},onPopupMouseenter(){const{vcTriggerContext:t={}}=this;t.onPopupMouseenter&&t.onPopupMouseenter(),this.clearDelayTimer()},onPopupMouseleave(t){var e;if(t&&t.relatedTarget&&!t.relatedTarget.setTimeout&&ot((e=this.popupRef)===null||e===void 0?void 0:e.getElement(),t.relatedTarget))return;this.isMouseLeaveToHide()&&this.delaySetPopupVisible(!1,this.$props.mouseLeaveDelay);const{vcTriggerContext:n={}}=this;n.onPopupMouseleave&&n.onPopupMouseleave(t)},onFocus(t){this.fireEvents("onFocus",t),this.clearDelayTimer(),this.isFocusToShow()&&(this.focusTime=Date.now(),this.delaySetPopupVisible(!0,this.$props.focusDelay))},onMousedown(t){this.fireEvents("onMousedown",t),this.preClickTime=Date.now()},onTouchstart(t){this.fireEvents("onTouchstart",t),this.preTouchTime=Date.now()},onBlur(t){ot(t.target,t.relatedTarget||document.activeElement)||(this.fireEvents("onBlur",t),this.clearDelayTimer(),this.isBlurToHide()&&this.delaySetPopupVisible(!1,this.$props.blurDelay))},onContextmenu(t){t.preventDefault(),this.fireEvents("onContextmenu",t),this.setPopupVisible(!0,t)},onContextmenuClose(){this.isContextmenuToShow()&&this.close()},onClick(t){if(this.fireEvents("onClick",t),this.focusTime){let n;if(this.preClickTime&&this.preTouchTime?n=Math.min(this.preClickTime,this.preTouchTime):this.preClickTime?n=this.preClickTime:this.preTouchTime&&(n=this.preTouchTime),Math.abs(n-this.focusTime)<20)return;this.focusTime=0}this.preClickTime=0,this.preTouchTime=0,this.isClickToShow()&&(this.isClickToHide()||this.isBlurToHide())&&t&&t.preventDefault&&t.preventDefault(),t&&t.domEvent&&t.domEvent.preventDefault();const e=!this.$data.sPopupVisible;(this.isClickToHide()&&!e||e&&this.isClickToShow())&&this.setPopupVisible(!this.$data.sPopupVisible,t)},onPopupMouseDown(){const{vcTriggerContext:t={}}=this;this.hasPopupMouseDown=!0,clearTimeout(this.mouseDownTimeout),this.mouseDownTimeout=setTimeout(()=>{this.hasPopupMouseDown=!1},0),t.onPopupMouseDown&&t.onPopupMouseDown(...arguments)},onDocumentClick(t){if(this.$props.mask&&!this.$props.maskClosable)return;const e=t.target,n=this.getRootDomNode(),o=this.getPopupDomNode();(!ot(n,e)||this.isContextMenuOnly())&&!ot(o,e)&&!this.hasPopupMouseDown&&this.delaySetPopupVisible(!1,.1)},getPopupDomNode(){var t;return((t=this.popupRef)===null||t===void 0?void 0:t.getElement())||null},getRootDomNode(){var t,e,n,o;const{getTriggerDOMNode:i}=this.$props;if(i){const r=((e=(t=this.triggerRef)===null||t===void 0?void 0:t.$el)===null||e===void 0?void 0:e.nodeName)==="#comment"?null:ut(this.triggerRef);return ut(i(r))}try{const r=((o=(n=this.triggerRef)===null||n===void 0?void 0:n.$el)===null||o===void 0?void 0:o.nodeName)==="#comment"?null:ut(this.triggerRef);if(r)return r}catch{}return ut(this)},handleGetPopupClassFromAlign(t){const e=[],n=this.$props,{popupPlacement:o,builtinPlacements:i,prefixCls:r,alignPoint:s,getPopupClassNameFromAlign:a}=n;return o&&i&&e.push(Kr(i,r,t,s)),a&&e.push(a(t)),e.join(" ")},getPopupAlign(){const t=this.$props,{popupPlacement:e,popupAlign:n,builtinPlacements:o}=t;return e&&o?Ie(o,e,n):n},getComponent(){const t={};this.isMouseEnterToShow()&&(t.onMouseenter=this.onPopupMouseenter),this.isMouseLeaveToHide()&&(t.onMouseleave=this.onPopupMouseleave),t.onMousedown=this.onPopupMouseDown,t[tt?"onTouchstartPassive":"onTouchstart"]=this.onPopupMouseDown;const{handleGetPopupClassFromAlign:e,getRootDomNode:n,$attrs:o}=this,{prefixCls:i,destroyPopupOnHide:r,popupClassName:s,popupAnimation:a,popupTransitionName:u,popupStyle:l,mask:f,maskAnimation:d,maskTransitionName:p,zIndex:c,stretch:h,alignPoint:v,mobile:O,arrow:y,forceRender:w}=this.$props,{sPopupVisible:_,point:$}=this.$data,N=m(m({prefixCls:i,arrow:y,destroyPopupOnHide:r,visible:_,point:v?$:null,align:this.align,animation:a,getClassNameFromAlign:e,stretch:h,getRootDomNode:n,mask:f,zIndex:c,transitionName:u,maskAnimation:d,maskTransitionName:p,class:s,style:l,onAlign:o.onPopupAlign||dn},t),{ref:this.setPopupRef,mobile:O,forceRender:w});return T(Gr,N,{default:this.$slots.popup||(()=>Zn(this,"popup"))})},attachParent(t){j.cancel(this.attachId);const{getPopupContainer:e,getDocument:n}=this.$props,o=this.getRootDomNode();let i;e?(o||e.length===0)&&(i=e(o)):i=n(this.getRootDomNode()).body,i?i.appendChild(t):this.attachId=j(()=>{this.attachParent(t)})},getContainer(){const{$props:t}=this,{getDocument:e}=t,n=e(this.getRootDomNode()).createElement("div");return n.style.position="absolute",n.style.top="0",n.style.left="0",n.style.width="100%",this.attachParent(n),n},setPopupVisible(t,e){const{alignPoint:n,sPopupVisible:o,onPopupVisibleChange:i}=this;this.clearDelayTimer(),o!==t&&(Kn(this,"popupVisible")||this.setState({sPopupVisible:t,prevPopupVisible:o}),i&&i(t)),n&&e&&t&&this.setPoint(e)},setPoint(t){const{alignPoint:e}=this.$props;!e||!t||this.setState({point:{pageX:t.pageX,pageY:t.pageY}})},handlePortalUpdate(){this.prevPopupVisible!==this.sPopupVisible&&this.afterPopupVisibleChange(this.sPopupVisible)},delaySetPopupVisible(t,e,n){const o=e*1e3;if(this.clearDelayTimer(),o){const i=n?{pageX:n.pageX,pageY:n.pageY}:null;this.delayTimer=setTimeout(()=>{this.setPopupVisible(t,i),this.clearDelayTimer()},o)}else this.setPopupVisible(t,n)},clearDelayTimer(){this.delayTimer&&(clearTimeout(this.delayTimer),this.delayTimer=null)},clearOutsideHandler(){this.clickOutsideHandler&&(this.clickOutsideHandler.remove(),this.clickOutsideHandler=null),this.contextmenuOutsideHandler1&&(this.contextmenuOutsideHandler1.remove(),this.contextmenuOutsideHandler1=null),this.contextmenuOutsideHandler2&&(this.contextmenuOutsideHandler2.remove(),this.contextmenuOutsideHandler2=null),this.touchOutsideHandler&&(this.touchOutsideHandler.remove(),this.touchOutsideHandler=null)},createTwoChains(t){let e=()=>{};const n=le(this);return this.childOriginEvents[t]&&n[t]?this[`fire${t}`]:(e=this.childOriginEvents[t]||n[t]||e,e)},isClickToShow(){const{action:t,showAction:e}=this.$props;return t.indexOf("click")!==-1||e.indexOf("click")!==-1},isContextMenuOnly(){const{action:t}=this.$props;return t==="contextmenu"||t.length===1&&t[0]==="contextmenu"},isContextmenuToShow(){const{action:t,showAction:e}=this.$props;return t.indexOf("contextmenu")!==-1||e.indexOf("contextmenu")!==-1},isClickToHide(){const{action:t,hideAction:e}=this.$props;return t.indexOf("click")!==-1||e.indexOf("click")!==-1},isMouseEnterToShow(){const{action:t,showAction:e}=this.$props;return t.indexOf("hover")!==-1||e.indexOf("mouseenter")!==-1},isMouseLeaveToHide(){const{action:t,hideAction:e}=this.$props;return t.indexOf("hover")!==-1||e.indexOf("mouseleave")!==-1},isFocusToShow(){const{action:t,showAction:e}=this.$props;return t.indexOf("focus")!==-1||e.indexOf("focus")!==-1},isBlurToHide(){const{action:t,hideAction:e}=this.$props;return t.indexOf("focus")!==-1||e.indexOf("blur")!==-1},forcePopupAlign(){var t;this.$data.sPopupVisible&&((t=this.popupRef)===null||t===void 0||t.forceAlign())},fireEvents(t,e){this.childOriginEvents[t]&&this.childOriginEvents[t](e);const n=this.$props[t]||this.$attrs[t];n&&n(e)},close(){this.setPopupVisible(!1)}},render(){const{$attrs:t}=this,e=Qt(qn(this)),{alignPoint:n,getPopupContainer:o}=this.$props,i=e[0];this.childOriginEvents=le(i);const r={key:"trigger"};this.isContextmenuToShow()?r.onContextmenu=this.onContextmenu:r.onContextmenu=this.createTwoChains("onContextmenu"),this.isClickToHide()||this.isClickToShow()?(r.onClick=this.onClick,r.onMousedown=this.onMousedown,r[tt?"onTouchstartPassive":"onTouchstart"]=this.onTouchstart):(r.onClick=this.createTwoChains("onClick"),r.onMousedown=this.createTwoChains("onMousedown"),r[tt?"onTouchstartPassive":"onTouchstart"]=this.createTwoChains("onTouchstart")),this.isMouseEnterToShow()?(r.onMouseenter=this.onMouseenter,n&&(r.onMousemove=this.onMouseMove)):r.onMouseenter=this.createTwoChains("onMouseenter"),this.isMouseLeaveToHide()?r.onMouseleave=this.onMouseleave:r.onMouseleave=this.createTwoChains("onMouseleave"),this.isFocusToShow()||this.isBlurToHide()?(r.onFocus=this.onFocus,r.onBlur=this.onBlur):(r.onFocus=this.createTwoChains("onFocus"),r.onBlur=l=>{l&&(!l.relatedTarget||!ot(l.target,l.relatedTarget))&&this.createTwoChains("onBlur")(l)});const s=rt(i&&i.props&&i.props.class,t.class);s&&(r.class=s);const a=ht(i,m(m({},r),{ref:"triggerRef"}),!0,!0),u=T(es,{key:"portal",getContainer:o&&(()=>o(this.getRootDomNode())),didUpdate:this.handlePortalUpdate,visible:this.$data.sPopupVisible},{default:this.getComponent});return T(en,null,[a,u])}});function is(t,e,n,o){for(var i=t.length,r=n+-1;++r<i;)if(e(t[r],r,t))return r;return-1}function rs(t){return t!==t}function ss(t,e,n){for(var o=n-1,i=t.length;++o<i;)if(t[o]===e)return o;return-1}function as(t,e,n){return e===e?ss(t,e,n):is(t,rs,n)}function ta(t,e){var n=t==null?0:t.length;return!!n&&as(t,e,0)>-1}const ls=t=>({animationDuration:t,animationFillMode:"both"}),us=t=>({animationDuration:t,animationFillMode:"both"}),cs=function(t,e,n,o){const r=(arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1)?"&":"";return{[`
      ${r}${t}-enter,
      ${r}${t}-appear
    `]:m(m({},ls(o)),{animationPlayState:"paused"}),[`${r}${t}-leave`]:m(m({},us(o)),{animationPlayState:"paused"}),[`
      ${r}${t}-enter${t}-enter-active,
      ${r}${t}-appear${t}-appear-active
    `]:{animationName:e,animationPlayState:"running"},[`${r}${t}-leave${t}-leave-active`]:{animationName:n,animationPlayState:"running",pointerEvents:"none"}}},fs=new V("antZoomIn",{"0%":{transform:"scale(0.2)",opacity:0},"100%":{transform:"scale(1)",opacity:1}}),ps=new V("antZoomOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0.2)",opacity:0}}),We=new V("antZoomBigIn",{"0%":{transform:"scale(0.8)",opacity:0},"100%":{transform:"scale(1)",opacity:1}}),je=new V("antZoomBigOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0.8)",opacity:0}}),ds=new V("antZoomUpIn",{"0%":{transform:"scale(0.8)",transformOrigin:"50% 0%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"50% 0%"}}),hs=new V("antZoomUpOut",{"0%":{transform:"scale(1)",transformOrigin:"50% 0%"},"100%":{transform:"scale(0.8)",transformOrigin:"50% 0%",opacity:0}}),ms=new V("antZoomLeftIn",{"0%":{transform:"scale(0.8)",transformOrigin:"0% 50%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"0% 50%"}}),vs=new V("antZoomLeftOut",{"0%":{transform:"scale(1)",transformOrigin:"0% 50%"},"100%":{transform:"scale(0.8)",transformOrigin:"0% 50%",opacity:0}}),gs=new V("antZoomRightIn",{"0%":{transform:"scale(0.8)",transformOrigin:"100% 50%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"100% 50%"}}),ys=new V("antZoomRightOut",{"0%":{transform:"scale(1)",transformOrigin:"100% 50%"},"100%":{transform:"scale(0.8)",transformOrigin:"100% 50%",opacity:0}}),bs=new V("antZoomDownIn",{"0%":{transform:"scale(0.8)",transformOrigin:"50% 100%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"50% 100%"}}),ws=new V("antZoomDownOut",{"0%":{transform:"scale(1)",transformOrigin:"50% 100%"},"100%":{transform:"scale(0.8)",transformOrigin:"50% 100%",opacity:0}}),Os={zoom:{inKeyframes:fs,outKeyframes:ps},"zoom-big":{inKeyframes:We,outKeyframes:je},"zoom-big-fast":{inKeyframes:We,outKeyframes:je},"zoom-left":{inKeyframes:ms,outKeyframes:vs},"zoom-right":{inKeyframes:gs,outKeyframes:ys},"zoom-up":{inKeyframes:ds,outKeyframes:hs},"zoom-down":{inKeyframes:bs,outKeyframes:ws}},_s=(t,e)=>{const{antCls:n}=t,o=`${n}-${e}`,{inKeyframes:i,outKeyframes:r}=Os[e];return[cs(o,i,r,e==="zoom-big-fast"?t.motionDurationFast:t.motionDurationMid),{[`
        ${o}-enter,
        ${o}-appear
      `]:{transform:"scale(0)",opacity:0,animationTimingFunction:t.motionEaseOutCirc,"&-prepare":{transform:"none"}},[`${o}-leave`]:{animationTimingFunction:t.motionEaseInOutCirc}}]},ea=t=>({[t.componentCls]:{[`${t.antCls}-motion-collapse-legacy`]:{overflow:"hidden","&-active":{transition:`height ${t.motionDurationMid} ${t.motionEaseInOut},
        opacity ${t.motionDurationMid} ${t.motionEaseInOut} !important`}},[`${t.antCls}-motion-collapse`]:{overflow:"hidden",transition:`height ${t.motionDurationMid} ${t.motionEaseInOut},
        opacity ${t.motionDurationMid} ${t.motionEaseInOut} !important`}}}),na=["xxxl","xxl","xl","lg","md","sm","xs"],Cs=t=>({xs:`(max-width: ${t.screenXSMax}px)`,sm:`(min-width: ${t.screenSM}px)`,md:`(min-width: ${t.screenMD}px)`,lg:`(min-width: ${t.screenLG}px)`,xl:`(min-width: ${t.screenXL}px)`,xxl:`(min-width: ${t.screenXXL}px)`,xxxl:`{min-width: ${t.screenXXXL}px}`});function oa(){const[,t]=eo();return H(()=>{const e=Cs(t.value),n=new Map;let o=-1,i={};return{matchHandlers:{},dispatch(r){return i=r,n.forEach(s=>s(i)),n.size>=1},subscribe(r){return n.size||this.register(),o+=1,n.set(o,r),r(i),o},unsubscribe(r){n.delete(r),n.size||this.unregister()},unregister(){Object.keys(e).forEach(r=>{const s=e[r],a=this.matchHandlers[s];a==null||a.mql.removeListener(a==null?void 0:a.listener)}),n.clear()},register(){Object.keys(e).forEach(r=>{const s=e[r],a=l=>{let{matches:f}=l;this.dispatch(m(m({},i),{[r]:f}))},u=window.matchMedia(s);u.addListener(a),this.matchHandlers[s]={mql:u,listener:a},a(u)})},responsiveMap:e}})}const I={adjustX:1,adjustY:1},z=[0,0],$n={left:{points:["cr","cl"],overflow:I,offset:[-4,0],targetOffset:z},right:{points:["cl","cr"],overflow:I,offset:[4,0],targetOffset:z},top:{points:["bc","tc"],overflow:I,offset:[0,-4],targetOffset:z},bottom:{points:["tc","bc"],overflow:I,offset:[0,4],targetOffset:z},topLeft:{points:["bl","tl"],overflow:I,offset:[0,-4],targetOffset:z},leftTop:{points:["tr","tl"],overflow:I,offset:[-4,0],targetOffset:z},topRight:{points:["br","tr"],overflow:I,offset:[0,-4],targetOffset:z},rightTop:{points:["tl","tr"],overflow:I,offset:[4,0],targetOffset:z},bottomRight:{points:["tr","br"],overflow:I,offset:[0,4],targetOffset:z},rightBottom:{points:["bl","br"],overflow:I,offset:[4,0],targetOffset:z},bottomLeft:{points:["tl","bl"],overflow:I,offset:[0,4],targetOffset:z},leftBottom:{points:["br","bl"],overflow:I,offset:[-4,0],targetOffset:z}},Ps={prefixCls:String,id:String,overlayInnerStyle:P.any},xs=k({compatConfig:{MODE:3},name:"TooltipContent",props:Ps,setup(t,e){let{slots:n}=e;return()=>{var o;return T("div",{class:`${t.prefixCls}-inner`,id:t.id,role:"tooltip",style:t.overlayInnerStyle},[(o=n.overlay)===null||o===void 0?void 0:o.call(n)])}}});var Ts=function(t,e){var n={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&e.indexOf(o)<0&&(n[o]=t[o]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,o=Object.getOwnPropertySymbols(t);i<o.length;i++)e.indexOf(o[i])<0&&Object.prototype.propertyIsEnumerable.call(t,o[i])&&(n[o[i]]=t[o[i]]);return n};function Xe(){}const Ss=k({compatConfig:{MODE:3},name:"Tooltip",inheritAttrs:!1,props:{trigger:P.any.def(["hover"]),defaultVisible:{type:Boolean,default:void 0},visible:{type:Boolean,default:void 0},placement:P.string.def("right"),transitionName:String,animation:P.any,afterVisibleChange:P.func.def(()=>{}),overlayStyle:{type:Object,default:void 0},overlayClassName:String,prefixCls:P.string.def("rc-tooltip"),mouseEnterDelay:P.number.def(.1),mouseLeaveDelay:P.number.def(.1),getPopupContainer:Function,destroyTooltipOnHide:{type:Boolean,default:!1},align:P.object.def(()=>({})),arrowContent:P.any.def(null),tipId:String,builtinPlacements:P.object,overlayInnerStyle:{type:Object,default:void 0},popupVisible:{type:Boolean,default:void 0},onVisibleChange:Function,onPopupAlign:Function,arrow:{type:Boolean,default:!0}},setup(t,e){let{slots:n,attrs:o,expose:i}=e;const r=S(),s=()=>{const{prefixCls:f,tipId:d,overlayInnerStyle:p}=t;return[t.arrow?T("div",{class:`${f}-arrow`,key:"arrow"},[no(n,t,"arrowContent")]):null,T(xs,{key:"content",prefixCls:f,id:d,overlayInnerStyle:p},{overlay:n.overlay})]};i({getPopupDomNode:()=>r.value.getPopupDomNode(),triggerDOM:r,forcePopupAlign:()=>{var f;return(f=r.value)===null||f===void 0?void 0:f.forcePopupAlign()}});const u=S(!1),l=S(!1);return nn(()=>{const{destroyTooltipOnHide:f}=t;if(typeof f=="boolean")u.value=f;else if(f&&typeof f=="object"){const{keepParent:d}=f;u.value=d===!0,l.value=d===!1}}),()=>{const{overlayClassName:f,trigger:d,mouseEnterDelay:p,mouseLeaveDelay:c,overlayStyle:h,prefixCls:v,afterVisibleChange:O,transitionName:y,animation:w,placement:_,align:$,destroyTooltipOnHide:N,defaultVisible:x}=t,B=Ts(t,["overlayClassName","trigger","mouseEnterDelay","mouseLeaveDelay","overlayStyle","prefixCls","afterVisibleChange","transitionName","animation","placement","align","destroyTooltipOnHide","defaultVisible"]),A=m({},B);t.visible!==void 0&&(A.popupVisible=t.visible);const D=m(m(m({popupClassName:f,prefixCls:v,action:d,builtinPlacements:$n,popupPlacement:_,popupAlign:$,afterPopupVisibleChange:O,popupTransitionName:y,popupAnimation:w,defaultPopupVisible:x,destroyPopupOnHide:u.value,autoDestroy:l.value,mouseLeaveDelay:c,popupStyle:h,mouseEnterDelay:p},A),o),{onPopupVisibleChange:t.onVisibleChange||Xe,onPopupAlign:t.onPopupAlign||Xe,ref:r,arrow:!!t.arrow,popup:s()});return T(os,D,{default:n.default})}}}),$s=()=>({trigger:[String,Array],open:{type:Boolean,default:void 0},visible:{type:Boolean,default:void 0},placement:String,color:String,transitionName:String,overlayStyle:gt(),overlayInnerStyle:gt(),overlayClassName:String,openClassName:String,prefixCls:String,mouseEnterDelay:Number,mouseLeaveDelay:Number,getPopupContainer:Function,arrowPointAtCenter:{type:Boolean,default:void 0},arrow:{type:[Boolean,Object],default:!0},autoAdjustOverflow:{type:[Boolean,Object],default:void 0},destroyTooltipOnHide:{type:Boolean,default:void 0},align:gt(),builtinPlacements:gt(),children:Array,onVisibleChange:Function,"onUpdate:visible":Function,onOpenChange:Function,"onUpdate:open":Function}),Es={adjustX:1,adjustY:1},ke={adjustX:0,adjustY:0},As=[0,0];function Ye(t){return typeof t=="boolean"?t?Es:ke:m(m({},ke),t)}function Ms(t){const{arrowWidth:e=4,horizontalArrowShift:n=16,verticalArrowShift:o=8,autoAdjustOverflow:i,arrowPointAtCenter:r}=t,s={left:{points:["cr","cl"],offset:[-4,0]},right:{points:["cl","cr"],offset:[4,0]},top:{points:["bc","tc"],offset:[0,-4]},bottom:{points:["tc","bc"],offset:[0,4]},topLeft:{points:["bl","tc"],offset:[-(n+e),-4]},leftTop:{points:["tr","cl"],offset:[-4,-(o+e)]},topRight:{points:["br","tc"],offset:[n+e,-4]},rightTop:{points:["tl","cr"],offset:[4,-(o+e)]},bottomRight:{points:["tr","bc"],offset:[n+e,4]},rightBottom:{points:["bl","cr"],offset:[4,o+e]},bottomLeft:{points:["tl","bc"],offset:[-(n+e),4]},leftBottom:{points:["br","cl"],offset:[-4,o+e]}};return Object.keys(s).forEach(a=>{s[a]=r?m(m({},s[a]),{overflow:Ye(i),targetOffset:As}):m(m({},$n[a]),{overflow:Ye(i)}),s[a].ignoreShake=!0}),s}function Ds(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];for(let e=0,n=t.length;e<n;e++)if(t[e]!==void 0)return t[e]}const Rs=Et.map(t=>`${t}-inverse`),Ns=["success","processing","error","default","warning"];function Ls(t){return(arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0)?[...Rs,...Et].includes(t):Et.includes(t)}function ia(t){return Ns.includes(t)}function Bs(t,e){const n=Ls(e),o=rt({[`${t}-${e}`]:e&&n}),i={},r={};return e&&!n&&(i.background=e,r["--antd-arrow-background-color"]=e),{className:o,overlayStyle:i,arrowStyle:r}}function Ct(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";return t.map(n=>`${e}${n}`).join(",")}const En=8;function Hs(t){const e=En,{sizePopupArrow:n,contentRadius:o,borderRadiusOuter:i,limitVerticalRadius:r}=t,s=n/2-Math.ceil(i*(Math.sqrt(2)-1)),a=(o>12?o+2:12)-s,u=r?e-s:a;return{dropdownArrowOffset:a,dropdownArrowOffsetVertical:u}}function Is(t,e){const{componentCls:n,sizePopupArrow:o,marginXXS:i,borderRadiusXS:r,borderRadiusOuter:s,boxShadowPopoverArrow:a}=t,{colorBg:u,showArrowCls:l,contentRadius:f=t.borderRadiusLG,limitVerticalRadius:d}=e,{dropdownArrowOffsetVertical:p,dropdownArrowOffset:c}=Hs({sizePopupArrow:o,contentRadius:f,borderRadiusOuter:s,limitVerticalRadius:d}),h=o/2+i;return{[n]:{[`${n}-arrow`]:[m(m({position:"absolute",zIndex:1,display:"block"},Bo(o,r,s,u,a)),{"&:before":{background:u}})],[[`&-placement-top ${n}-arrow`,`&-placement-topLeft ${n}-arrow`,`&-placement-topRight ${n}-arrow`].join(",")]:{bottom:0,transform:"translateY(100%) rotate(180deg)"},[`&-placement-top ${n}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(100%) rotate(180deg)"},[`&-placement-topLeft ${n}-arrow`]:{left:{_skip_check_:!0,value:c}},[`&-placement-topRight ${n}-arrow`]:{right:{_skip_check_:!0,value:c}},[[`&-placement-bottom ${n}-arrow`,`&-placement-bottomLeft ${n}-arrow`,`&-placement-bottomRight ${n}-arrow`].join(",")]:{top:0,transform:"translateY(-100%)"},[`&-placement-bottom ${n}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(-100%)"},[`&-placement-bottomLeft ${n}-arrow`]:{left:{_skip_check_:!0,value:c}},[`&-placement-bottomRight ${n}-arrow`]:{right:{_skip_check_:!0,value:c}},[[`&-placement-left ${n}-arrow`,`&-placement-leftTop ${n}-arrow`,`&-placement-leftBottom ${n}-arrow`].join(",")]:{right:{_skip_check_:!0,value:0},transform:"translateX(100%) rotate(90deg)"},[`&-placement-left ${n}-arrow`]:{top:{_skip_check_:!0,value:"50%"},transform:"translateY(-50%) translateX(100%) rotate(90deg)"},[`&-placement-leftTop ${n}-arrow`]:{top:p},[`&-placement-leftBottom ${n}-arrow`]:{bottom:p},[[`&-placement-right ${n}-arrow`,`&-placement-rightTop ${n}-arrow`,`&-placement-rightBottom ${n}-arrow`].join(",")]:{left:{_skip_check_:!0,value:0},transform:"translateX(-100%) rotate(-90deg)"},[`&-placement-right ${n}-arrow`]:{top:{_skip_check_:!0,value:"50%"},transform:"translateY(-50%) translateX(-100%) rotate(-90deg)"},[`&-placement-rightTop ${n}-arrow`]:{top:p},[`&-placement-rightBottom ${n}-arrow`]:{bottom:p},[Ct(["&-placement-topLeft","&-placement-top","&-placement-topRight"].map(v=>v+=":not(&-arrow-hidden)"),l)]:{paddingBottom:h},[Ct(["&-placement-bottomLeft","&-placement-bottom","&-placement-bottomRight"].map(v=>v+=":not(&-arrow-hidden)"),l)]:{paddingTop:h},[Ct(["&-placement-leftTop","&-placement-left","&-placement-leftBottom"].map(v=>v+=":not(&-arrow-hidden)"),l)]:{paddingRight:{_skip_check_:!0,value:h}},[Ct(["&-placement-rightTop","&-placement-right","&-placement-rightBottom"].map(v=>v+=":not(&-arrow-hidden)"),l)]:{paddingLeft:{_skip_check_:!0,value:h}}}}}const zs=t=>{const{componentCls:e,tooltipMaxWidth:n,tooltipColor:o,tooltipBg:i,tooltipBorderRadius:r,zIndexPopup:s,controlHeight:a,boxShadowSecondary:u,paddingSM:l,paddingXS:f,tooltipRadiusOuter:d}=t;return[{[e]:m(m(m(m({},io(t)),{position:"absolute",zIndex:s,display:"block","&":[{width:"max-content"},{width:"intrinsic"}],maxWidth:n,visibility:"visible","&-hidden":{display:"none"},"--antd-arrow-background-color":i,[`${e}-inner`]:{minWidth:a,minHeight:a,padding:`${l/2}px ${f}px`,color:o,textAlign:"start",textDecoration:"none",wordWrap:"break-word",backgroundColor:i,borderRadius:r,boxShadow:u},[["&-placement-left","&-placement-leftTop","&-placement-leftBottom","&-placement-right","&-placement-rightTop","&-placement-rightBottom"].join(",")]:{[`${e}-inner`]:{borderRadius:Math.min(r,En)}},[`${e}-content`]:{position:"relative"}}),Ho(t,(p,c)=>{let{darkColor:h}=c;return{[`&${e}-${p}`]:{[`${e}-inner`]:{backgroundColor:h},[`${e}-arrow`]:{"--antd-arrow-background-color":h}}}})),{"&-rtl":{direction:"rtl"}})},Is(on(t,{borderRadiusOuter:d}),{colorBg:"var(--antd-arrow-background-color)",showArrowCls:"",contentRadius:r,limitVerticalRadius:!0}),{[`${e}-pure`]:{position:"relative",maxWidth:"none"}}]},Vs=(t,e)=>oo("Tooltip",o=>{if((e==null?void 0:e.value)===!1)return[];const{borderRadius:i,colorTextLightSolid:r,colorBgDefault:s,borderRadiusOuter:a}=o,u=on(o,{tooltipMaxWidth:250,tooltipColor:r,tooltipBorderRadius:i,tooltipBg:s,tooltipRadiusOuter:a>4?4:a});return[zs(u),_s(o,"zoom-big-fast")]},o=>{let{zIndexPopupBase:i,colorBgSpotlight:r}=o;return{zIndexPopup:i+70,colorBgDefault:r}})(t),Fs=(t,e)=>{const n={},o=m({},t);return e.forEach(i=>{t&&i in t&&(n[i]=t[i],delete o[i])}),{picked:n,omitted:o}},Ws=()=>m(m({},$s()),{title:P.any}),ra=()=>({trigger:"hover",align:{},placement:"top",mouseEnterDelay:.1,mouseLeaveDelay:.1,arrowPointAtCenter:!1,autoAdjustOverflow:!0}),js=k({compatConfig:{MODE:3},name:"ATooltip",inheritAttrs:!1,props:yo(Ws(),{trigger:"hover",align:{},placement:"top",mouseEnterDelay:.1,mouseLeaveDelay:.1,arrowPointAtCenter:!1,autoAdjustOverflow:!0}),slots:Object,setup(t,e){let{slots:n,emit:o,attrs:i,expose:r}=e;const{prefixCls:s,getPopupContainer:a,direction:u,rootPrefixCls:l}=ro("tooltip",t),f=H(()=>{var g;return(g=t.open)!==null&&g!==void 0?g:t.visible}),d=G(Ds([t.open,t.visible])),p=G();let c;X(f,g=>{j.cancel(c),c=j(()=>{d.value=!!g})});const h=()=>{var g;const C=(g=t.title)!==null&&g!==void 0?g:n.title;return!C&&C!==0},v=g=>{const C=h();f.value===void 0&&(d.value=C?!1:g),C||(o("update:visible",g),o("visibleChange",g),o("update:open",g),o("openChange",g))};r({getPopupDomNode:()=>p.value.getPopupDomNode(),open:d,forcePopupAlign:()=>{var g;return(g=p.value)===null||g===void 0?void 0:g.forcePopupAlign()}});const y=H(()=>{var g;const{builtinPlacements:C,autoAdjustOverflow:F,arrow:M,arrowPointAtCenter:L}=t;let E=L;return typeof M=="object"&&(E=(g=M.pointAtCenter)!==null&&g!==void 0?g:L),C||Ms({arrowPointAtCenter:E,autoAdjustOverflow:F})}),w=g=>g||g==="",_=g=>{const C=g.type;if(typeof C=="object"&&g.props&&((C.__ANT_BUTTON===!0||C==="button")&&w(g.props.disabled)||C.__ANT_SWITCH===!0&&(w(g.props.disabled)||w(g.props.loading))||C.__ANT_RADIO===!0&&w(g.props.disabled))){const{picked:F,omitted:M}=Fs(uo(g),["position","left","right","top","bottom","float","display","zIndex"]),L=m(m({display:"inline-block"},F),{cursor:"not-allowed",lineHeight:1,width:g.props&&g.props.block?"100%":void 0}),E=m(m({},M),{pointerEvents:"none"}),R=ht(g,{style:E},!0);return T("span",{style:L,class:`${s.value}-disabled-compatible-wrapper`},[R])}return g},$=()=>{var g,C;return(g=t.title)!==null&&g!==void 0?g:(C=n.title)===null||C===void 0?void 0:C.call(n)},N=(g,C)=>{const F=y.value,M=Object.keys(F).find(L=>{var E,R;return F[L].points[0]===((E=C.points)===null||E===void 0?void 0:E[0])&&F[L].points[1]===((R=C.points)===null||R===void 0?void 0:R[1])});if(M){const L=g.getBoundingClientRect(),E={top:"50%",left:"50%"};M.indexOf("top")>=0||M.indexOf("Bottom")>=0?E.top=`${L.height-C.offset[1]}px`:(M.indexOf("Top")>=0||M.indexOf("bottom")>=0)&&(E.top=`${-C.offset[1]}px`),M.indexOf("left")>=0||M.indexOf("Right")>=0?E.left=`${L.width-C.offset[0]}px`:(M.indexOf("right")>=0||M.indexOf("Left")>=0)&&(E.left=`${-C.offset[0]}px`),g.style.transformOrigin=`${E.left} ${E.top}`}},x=H(()=>Bs(s.value,t.color)),B=H(()=>i["data-popover-inject"]),[A,D]=Vs(s,H(()=>!B.value));return()=>{var g,C;const{openClassName:F,overlayClassName:M,overlayStyle:L,overlayInnerStyle:E}=t;let R=(C=Qt((g=n.default)===null||g===void 0?void 0:g.call(n)))!==null&&C!==void 0?C:null;R=R.length===1?R[0]:R;let Z=d.value;if(f.value===void 0&&h()&&(Z=!1),!R)return null;const U=_(so(R)&&!ao(R)?R:T("span",null,[R])),re=rt({[F||`${s.value}-open`]:!0,[U.props&&U.props.class]:U.props&&U.props.class}),Ht=rt(M,{[`${s.value}-rtl`]:u.value==="rtl"},x.value.className,D.value),Dn=m(m({},x.value.overlayStyle),E),Rn=x.value.arrowStyle,Nn=m(m(m({},i),t),{prefixCls:s.value,arrow:!!t.arrow,getPopupContainer:a==null?void 0:a.value,builtinPlacements:y.value,visible:Z,ref:p,overlayClassName:Ht,overlayStyle:m(m({},Rn),L),overlayInnerStyle:Dn,onVisibleChange:v,onPopupAlign:N,transitionName:lo(l.value,"zoom-big-fast",t.transitionName)});return A(T(Ss,Nn,{default:()=>[d.value?ht(U,{class:re}):U],arrowContent:()=>T("span",{class:`${s.value}-arrow-content`},null),overlay:$}))}}}),sa=co(js);function An(t,e){return t.classList?t.classList.contains(e):` ${t.className} `.indexOf(` ${e} `)>-1}function Ue(t,e){t.classList?t.classList.add(e):An(t,e)||(t.className=`${t.className} ${e}`)}function Ge(t,e){if(t.classList)t.classList.remove(e);else if(An(t,e)){const n=t.className;t.className=` ${n} `.replace(` ${e} `," ")}}const aa=function(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"ant-motion-collapse",e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return{name:t,appear:e,css:!0,onBeforeEnter:n=>{n.style.height="0px",n.style.opacity="0",Ue(n,t)},onEnter:n=>{pt(()=>{n.style.height=`${n.scrollHeight}px`,n.style.opacity="1"})},onAfterEnter:n=>{n&&(Ge(n,t),n.style.height=null,n.style.opacity=null)},onBeforeLeave:n=>{Ue(n,t),n.style.height=`${n.offsetHeight}px`,n.style.opacity=null},onLeave:n=>{setTimeout(()=>{n.style.height="0px",n.style.opacity="0"})},onAfterLeave:n=>{n&&(Ge(n,t),n.style&&(n.style.height=null,n.style.opacity=null))}}},Xs=()=>st()&&window.document.documentElement,Mn=t=>{if(st()&&window.document.documentElement){const e=Array.isArray(t)?t:[t],{documentElement:n}=window.document;return e.some(o=>o in n.style)}return!1},ks=(t,e)=>{if(!Mn(t))return!1;const n=document.createElement("div"),o=n.style[t];return n.style[t]=e,n.style[t]!==o};function la(t,e){return!Array.isArray(t)&&e!==void 0?ks(t,e):Mn(t)}let Pt;const ua=()=>{if(!Xs())return!1;if(Pt!==void 0)return Pt;const t=document.createElement("div");return t.style.display="flex",t.style.flexDirection="column",t.style.rowGap="1px",t.appendChild(document.createElement("div")),t.appendChild(document.createElement("div")),document.body.appendChild(t),Pt=t.scrollHeight===1,document.body.removeChild(t),Pt};export{ua as $,Et as A,Zr as B,os as C,Hs as D,Bo as E,Ms as F,Ho as G,Ls as H,ia as I,Pn as J,Lr as K,_r as L,nt as M,Br as N,$r as O,es as P,xr as Q,Gs as R,Mt as S,sa as T,Cr as U,Ee as V,K as W,De as X,Tn as Y,Dr as Z,is as _,$s as a,fs as a0,Xr as a1,Ks as b,ht as c,Qs as d,Ue as e,Ds as f,Ge as g,ct as h,la as i,Sn as j,Js as k,aa as l,ea as m,cs as n,qs as o,_s as p,Xs as q,na as r,tt as s,ra as t,oa as u,Zs as v,ar as w,ta as x,or as y,Is as z};
