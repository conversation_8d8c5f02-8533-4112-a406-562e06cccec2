﻿using System.Collections.Concurrent;
using System.Net;
using CMSFramework.Logger;
using CMSFramework.TypeDef;
using WindCMS.GWServer.Agent;
using WindCMS.GWServer.Entity;
using WindCMS.GWServer.Utils;
using WTCMSLive.BusinessModel;

namespace WindCMS.GWServer.ListenerServer;

/// <summary>
/// 监听读事件处理
/// </summary>
public class ListenerReadHandler
{
    /// <summary>
    /// Dau 信息队列
    /// </summary>
    public static readonly ConcurrentQueue<DauInfoEntity> DauInfoQueue = new ConcurrentQueue<DauInfoEntity>();

    public void ReadData(TcpListenerAgent agent)
    {
        while (!(agent.CancellationToken?.IsCancellationRequested ?? true))
        {
            try
            {
                var networkStream = agent.TcpClient.GetStream();
                // 判断是否正在执行其他操作
                if (agent.HasInteraction)
                {
                    continue;
                }

                // 判断是否有数据
                if (!networkStream.DataAvailable)
                {
                    continue;
                }

                // 判断是否正在执行其他操作
                if (agent.HasInteraction)
                {
                    continue;
                }

                // 获取数据
                var bytesRead = networkStream.Read(agent.ReadDataBuffer, 0, agent.ReadDataBuffer.Length);
                if (bytesRead <= 0)
                {
                    continue;
                }

                var dataByteArray = new byte[bytesRead];
                Buffer.BlockCopy(agent.ReadDataBuffer, 0, dataByteArray, 0, bytesRead);

                // 过滤掉心跳报文
                if (dataByteArray[0] != 0)
                {
                    var remoteEndPoint = (IPEndPoint)agent.TcpClient.Client.RemoteEndPoint;
                    var ipAddress = remoteEndPoint.Address.ToString();
                    var contextKey = TcpListenerServer.GetTcpListenerContextKey(ipAddress);
                    var tcpListenerAgent = TcpListenerServer.GetTcpListenerAgent(contextKey);
                    // 判断是否真的存在连接
                    if (tcpListenerAgent == null)
                    {
                        TcpListenerServer.CloseTcpClient(agent);
                        break;
                    }
                    agent.LastCommunicationTime = DateTime.Now;
                    continue;
                }

                // 挑选出上报报文接口
                if (dataByteArray[4] == 123)
                {
                    agent.LastCommunicationTime = DateTime.Now;
                    GetStationsInformation(agent, dataByteArray);
                }
            }
            catch (Exception)
            {
                // 出现异常认为连接断开
                agent.ConnectReady = false;
            }
            finally
            {
                // 判断DAU是否长时间无通讯
                if (!agent.ConnectReady ||
                    agent.LastCommunicationTime.Add(AppConfigUtils.DauNoResponseTime) <= DateTime.Now)
                {
                    // 设置DAU状态
                    var windDau = agent.DauWorkContext?.Dau;
                    if (windDau != null)
                    {
                        windDau.DauOnOffStatus = EnumDauOnOffStatus.Off;
                    }

                    // 出现通讯异常后关闭客户端
                    TcpListenerServer.CloseTcpClient(agent);
                }

                // 防止频繁刷新
                Thread.Sleep(100);
            }
        }
    }

    /// <summary>
    /// 获取上报信息
    /// </summary>
    /// <param name="agent"></param>
    /// <param name="dataByteArray"></param>
    private void GetStationsInformation(TcpListenerAgent agent, byte[] dataByteArray)
    {
        var remoteEndPoint = (IPEndPoint)agent.TcpClient.Client.RemoteEndPoint!;
        // 基于上报信息构建DAU上下文信息
        agent.DauWorkContext = BuildDauContext.BuildContext(agent, remoteEndPoint, dataByteArray);
        Logger.LogInfoMessage(
            $"[GetStationsInformation] 接收到{remoteEndPoint.Address}:{remoteEndPoint.Port}上报数据, 设备类型: {(agent.IsGatewayDevice ? "Dau设备" : "网关设备")}.");
        // 构建写数据线程
        agent.ListenerWriteHandler = new ListenerWriteHandlerProxy(agent);

        // 判断是网关设备还是DAU设备
        DauInfoEntity dauInfoEntity;

        if (agent.IsGatewayDevice)
        {
            // // 获取上下文信息前停止采集
            // agent.ListenerWriteHandler.StopDaq();
            var wtLiveDbByteArray = agent.ListenerWriteHandler.GetDauContext();
            // // 获取完成后再次进行启动
            // agent.ListenerWriteHandler.StartDaq();
            dauInfoEntity = new DauInfoEntity
            {
                DauIp = remoteEndPoint.Address.ToString(),
                DauConfig = agent.DauConfigExtension,
                WtLiveDbByteArray = wtLiveDbByteArray
            };
        }
        else
        {
            // 获取DAU基础信息
            agent.ListenerWriteHandler.GetDauBaseParameter();
            Logger.LogDebugMessage($"[GetStationsInformation] 完成 DAU 基础信息获取, 获取结果：{(agent.ResultExecution ? "成功" : "失败")}.");
            if (!agent.ResultExecution)
            {
                TcpListenerServer.CloseTcpClient(agent);
                return;
            }
            
            // 获取是否坏标志
            agent.ListenerWriteHandler.GetDauEquipmentWarranty();
            if (!agent.ResultExecution)
            {
                TcpListenerServer.CloseTcpClient(agent);
                return;
            }
            
            // 获取DAU推送信息
            agent.ListenerWriteHandler.GetDauPushParameter();
            Logger.LogDebugMessage($"[GetStationsInformation] 完成DAU推送信息获取, 获取结果：{(agent.ResultExecution ? "成功" : "失败")}.");
            if (!agent.ResultExecution)
            {
                TcpListenerServer.CloseTcpClient(agent);
                return;
            }
            
            // 获取采集策略(获取对时服务数据)
            agent.ListenerWriteHandler.GetDauCollectionStrategy();
            Logger.LogDebugMessage($"[GetStationsInformation] 完成采集策略获取, 获取结果：{(agent.ResultExecution ? "成功" : "失败")}.");
            if (!agent.ResultExecution)
            {
                TcpListenerServer.CloseTcpClient(agent);
                return;
            }

            // 同步DAU上下文信息
            byte[] dbByteArray = Array.Empty<byte>();
            if (AppConfigUtils.DauSynchronizationContext)
            {
                dbByteArray = agent.ListenerWriteHandler.GetDauContext();
                Logger.LogDebugMessage($"[GetStationsInformation] 完成上下文信息获取, 获取结果：{(agent.ResultExecution ? "成功" : "失败")}.");
                if (!agent.ResultExecution)
                {
                    TcpListenerServer.CloseTcpClient(agent);
                    return;
                }
            }
            
            // 添加数据到队列中
            dauInfoEntity = new DauInfoEntity
            {
                DauIp = remoteEndPoint.Address.ToString(),
                DauConfig = agent.DauConfigExtension,
                WtLiveDbByteArray = dbByteArray
            };
        }

        // 添加数据到队列中
        DauInfoQueue.Enqueue(dauInfoEntity);
        Logger.LogDebugMessage($"[GetStationsInformation] 完成数据上报构建, 添加至解析队列.");

        // 处理后就绪
        agent.ConnectReady = true;
    }
}