using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using RealtimePerf.Hubs;
using System.Text;
using System.Text.Json;
using WTCMSLive.WebSite.Core.Models;
using WTCMSLive.WebSite.Core.Services;
using WTCMSLive.WebSite.Core.Middleware;
using ProtoBuf.Meta;
using Microsoft.Extensions.Hosting.WindowsServices;

namespace WTCMSLive.WebSite.Core
{
    public class Program
    {
        public static void Main(string[] args)
        {
            // 检查是否以服务方式运行
            var isService = WindowsServiceHelpers.IsWindowsService();

            var builder = WebApplication.CreateBuilder(args);
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

            // 设置环境变量（如果以服务方式运行，强制使用 Production 环境）
            if (isService && string.IsNullOrEmpty(Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")))
            {
                Environment.SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", "Production");
                builder.Environment.EnvironmentName = "Production";
            }

            // 配置日志
            if (isService)
            {
                builder.Logging.ClearProviders();
                builder.Logging.AddEventLog(settings =>
                {
                    settings.SourceName = "WRD_CMSConfigureWeb_Service";
                });
            }

            // 添加服务支持
            builder.Host.UseWindowsService(options =>
            {
                options.ServiceName = "WRD_CMSConfigureWeb_Service";
            })
            .UseSystemd();

            // 配置Web主机选项
            if (isService)
            {
                builder.WebHost.UseContentRoot(AppContext.BaseDirectory);
            }

            // Add services to the container.
            builder.Services.AddControllers(options =>
            {
                // 添加批量操作中间件
                options.Filters.Add<ActionContextMiddleware>();
            })
               .AddJsonOptions(options =>
               {
                   options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                   // 把 "" 当成 null
                   options.JsonSerializerOptions.NumberHandling =
                       System.Text.Json.Serialization.JsonNumberHandling.AllowReadingFromString;
               });


            // Configure JWT Authentication
            var jwtSettings = builder.Configuration.GetSection("JwtSettings").Get<JwtSettings>();
            var key = Encoding.ASCII.GetBytes(jwtSettings.SecretKey);

            builder.Services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.RequireHttpsMetadata = false;
                options.SaveToken = true;
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidIssuer = jwtSettings.Issuer,
                    ValidAudience = jwtSettings.Audience,
                    ClockSkew = TimeSpan.Zero
                };
            });

            builder.Services.AddCors(o => o.AddPolicy("any", p =>
            p.WithOrigins("http://127.0.0.1:5500",
                               "http://127.0.0.1:803")
             .AllowAnyMethod()
             .AllowAnyHeader()
             .AllowCredentials()));

            builder.Services.AddSingleton(builder.Configuration);

            builder.Services.AddSignalR();

            // 注册性能数据存储服务
            builder.Services.AddSingleton<IMetricsStorageService, MetricsStorageService>();

            // 注册服务管理服务
            builder.Services.AddSingleton<IServiceManagementService, ServiceManagementService>();

            // 注册服务日志服务
            builder.Services.AddSingleton<IServiceLogService, ServiceLogService>();

            // 注册批量操作服务
            builder.Services.AddScoped<IBatchOperationService, BatchOperationService>();

            // 注册数据中心服务
            builder.Services.AddHttpClient<IDataCenterService, DataCenterService>();

            // 注册录波数据进度监控服务
            builder.Services.AddSingleton<IRecordedDataProgressService, RecordedDataProgressService>();

            // HostedService
            builder.Services.AddSingleton<MetricsCollectorService>();
            builder.Services.AddHostedService(sp => sp.GetRequiredService<MetricsCollectorService>());

            // 注册DAU配置队列服务
            builder.Services.AddSingleton<IDAUConfigQueueService, DAUConfigQueueService>();

            // 注册DAU配置处理后台服务
            builder.Services.AddSingleton<DAUConfigProcessorService>();
            builder.Services.AddHostedService(sp => sp.GetRequiredService<DAUConfigProcessorService>());

            // tcp服务
            builder.Services.AddSingleton<TcpHostedService>();
            builder.Services.AddHostedService(sp => sp.GetRequiredService<TcpHostedService>());

            // 机组配置分发和同步服务
            builder.Services.AddSingleton<TurbineConfigDistributionService>();
            builder.Services.AddSingleton<TurbineConfigSyncService>();

            CMSFramework.BusinessEntity.ConfigInfo.SetConfiguration(builder.Services.BuildServiceProvider().GetRequiredService<IConfiguration>());

            var app = builder.Build();

            // 配置应用程序
            app.UseCors("any");

            app.UseHttpsRedirection();
            app.UseStaticFiles();
            app.UseAuthentication();

            app.UseRouting();

            app.UseAuthorization();
            app.MapControllers();
            app.MapHub<ServerPerformanceHub>("/Hubs/ServerPerformanceHub");
            // Vue files
            app.MapFallbackToFile("/index.html");

            try
            {
                if (isService)
                {
                    app.Logger.LogInformation("Starting Windows Service: WRD_CMSConfigureWeb_Service");
                }
                else
                {
                    app.Logger.LogInformation("Starting application in console mode");
                }

                app.Run();
            }
            catch (Exception ex)
            {
                app.Logger.LogError(ex, "Application terminated unexpectedly");
                throw;
            }
        }
    }
}