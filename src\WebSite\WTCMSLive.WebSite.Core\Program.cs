using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using RealtimePerf.Hubs;
using System.Text;
using System.Text.Json;
using WTCMSLive.WebSite.Core.Models;
using WTCMSLive.WebSite.Core.Services;
using WTCMSLive.WebSite.Core.Middleware;
using ProtoBuf.Meta;
using Microsoft.Extensions.Hosting.WindowsServices;
using Microsoft.Extensions.Hosting.Systemd;

namespace WTCMSLive.WebSite.Core
{
    public class Program
    {
        public static void Main(string[] args)
        {
            // 1. 首先判断运行环境
            var isWindowsService = WindowsServiceHelpers.IsWindowsService();
            var isLinuxService = SystemdHelpers.IsSystemdService();

            // 2. 使用 WebApplicationOptions 创建构建器，在创建时就设置 ContentRootPath
            var options = new WebApplicationOptions
            {
                Args = args,
                // 如果是服务方式运行，将ContentRoot设置为程序所在目录
                ContentRootPath = isWindowsService || isLinuxService
                    ? AppContext.BaseDirectory
                    : default
            };

            var builder = WebApplication.CreateBuilder(options);

            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

            // 3. 设置环境变量（如果以服务方式运行，强制使用 Production 环境）
            //if ((isWindowsService || isLinuxService) &&
            //    string.IsNullOrEmpty(Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")))
            //{
            //    Environment.SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", "Production");
            //    builder.Environment.EnvironmentName = "Production";
            //}

            // 4. 配置日志（仅在Windows服务时配置事件日志）
            if (isWindowsService)
            {
                builder.Logging.AddEventLog(settings =>
                {
                    settings.SourceName = "WRD_CMSConfigureWeb_Service";
                });
            }

            // 5. 配置服务支持（使用条件判断，避免不必要的配置）
            if (isWindowsService)
            {
                builder.Host.UseWindowsService(options =>
                {
                    options.ServiceName = "WRD_CMSConfigureWeb_Service";
                });
            }
            else if (isLinuxService)
            {
                builder.Host.UseSystemd();
            }

            // 6. 原有的服务配置保持不变
            builder.Services.AddControllers(options =>
            {
                options.Filters.Add<ActionContextMiddleware>();
            })
            .AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                options.JsonSerializerOptions.NumberHandling =
                    System.Text.Json.Serialization.JsonNumberHandling.AllowReadingFromString;
            });

            // Configure JWT Authentication
            var jwtSettings = builder.Configuration.GetSection("JwtSettings").Get<JwtSettings>();
            var key = Encoding.ASCII.GetBytes(jwtSettings.SecretKey);

            builder.Services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.RequireHttpsMetadata = false;
                options.SaveToken = true;
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidIssuer = jwtSettings.Issuer,
                    ValidAudience = jwtSettings.Audience,
                    ClockSkew = TimeSpan.Zero
                };
            });

            builder.Services.AddCors(o => o.AddPolicy("any", p =>
            p.WithOrigins("http://127.0.0.1:5500 ", "http://127.0.0.1:803 ")
             .AllowAnyMethod()
             .AllowAnyHeader()
             .AllowCredentials()));

            builder.Services.AddSingleton(builder.Configuration);
            builder.Services.AddSignalR();

            // 注册各种服务（保持原有逻辑）
            builder.Services.AddSingleton<IMetricsStorageService, MetricsStorageService>();
            builder.Services.AddSingleton<IServiceManagementService, ServiceManagementService>();
            builder.Services.AddSingleton<IServiceLogService, ServiceLogService>();
            builder.Services.AddScoped<IBatchOperationService, BatchOperationService>();
            builder.Services.AddHttpClient<IDataCenterService, DataCenterService>();
            builder.Services.AddSingleton<IRecordedDataProgressService, RecordedDataProgressService>();

            // HostedService
            builder.Services.AddSingleton<MetricsCollectorService>();
            builder.Services.AddHostedService(sp => sp.GetRequiredService<MetricsCollectorService>());

            // DAU配置相关服务
            builder.Services.AddSingleton<IDAUConfigQueueService, DAUConfigQueueService>();
            builder.Services.AddSingleton<DAUConfigProcessorService>();
            builder.Services.AddHostedService(sp => sp.GetRequiredService<DAUConfigProcessorService>());

            // TCP服务
            builder.Services.AddSingleton<TcpHostedService>();
            builder.Services.AddHostedService(sp => sp.GetRequiredService<TcpHostedService>());

            // 机组配置服务
            builder.Services.AddSingleton<TurbineConfigDistributionService>();
            builder.Services.AddSingleton<TurbineConfigSyncService>();

            CMSFramework.BusinessEntity.ConfigInfo.SetConfiguration(
                builder.Services.BuildServiceProvider().GetRequiredService<IConfiguration>());

            var app = builder.Build();

            // 配置中间件
            app.UseCors("any");
            app.UseHttpsRedirection();
            app.UseStaticFiles();
            app.UseAuthentication();
            app.UseRouting();
            app.UseAuthorization();

            app.MapControllers();
            app.MapHub<ServerPerformanceHub>("/Hubs/ServerPerformanceHub");
            app.MapFallbackToFile("/index.html");

            // 7. 记录启动日志
            try
            {
                if (isWindowsService)
                {
                    app.Logger.LogInformation("Starting Windows Service: WRD_CMSConfigureWeb_Service");
                }
                else if (isLinuxService)
                {
                    app.Logger.LogInformation("Starting Linux systemd Service: WRD_CMSConfigureWeb_Service");
                }
                else
                {
                    app.Logger.LogInformation("Starting application in console mode");
                }

                app.Run();
            }
            catch (Exception ex)
            {
                app.Logger.LogError(ex, "Application terminated unexpectedly");
                throw;
            }
        }
    }
}