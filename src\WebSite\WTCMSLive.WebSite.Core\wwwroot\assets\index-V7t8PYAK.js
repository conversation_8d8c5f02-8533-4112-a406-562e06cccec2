import{b as i,bc as J,r as E,F as Q,bd as Y,be as Z,bf as k,_ as c,K as ee,L as te,M as le,bg as ne,H as K,bh as B,O as oe,k as ie,bi as se,as as ae,bj as F,j as re,I as G,bk as de}from"./index-sMW2Pm6g.js";import{u as ce,r as X,c as pe}from"./styleChecker-LI4Lr2UF.js";function I(e){return e!=null}const T=e=>{const{itemPrefixCls:t,component:l,span:o,labelStyle:n,contentStyle:s,bordered:d,label:r,content:a,colon:u}=e,p=l;return d?i(p,{class:[{[`${t}-item-label`]:I(r),[`${t}-item-content`]:I(a)}],colSpan:o},{default:()=>[I(r)&&i("span",{style:n},[r]),I(a)&&i("span",{style:s},[a])]}):i(p,{class:[`${t}-item`],colSpan:o},{default:()=>[i("div",{class:`${t}-item-container`},[(r||r===0)&&i("span",{class:[`${t}-item-label`,{[`${t}-item-no-colon`]:!u}],style:n},[r]),(a||a===0)&&i("span",{class:`${t}-item-content`,style:s},[a])])]})},ue=e=>{const t=(u,p,L)=>{let{colon:m,prefixCls:x,bordered:f}=p,{component:y,type:w,showLabel:M,showContent:P,labelStyle:g,contentStyle:S}=L;return u.map((b,h)=>{var $,v;const j=b.props||{},{prefixCls:_=x,span:R=1,labelStyle:A=j["label-style"],contentStyle:H=j["content-style"],label:W=(v=($=b.children)===null||$===void 0?void 0:$.label)===null||v===void 0?void 0:v.call($)}=j,N=Y(b),z=Z(b),D=k(b),{key:O}=b;return typeof y=="string"?i(T,{key:`${w}-${String(O)||h}`,class:z,style:D,labelStyle:c(c({},g),A),contentStyle:c(c({},S),H),span:R,colon:m,component:y,itemPrefixCls:_,bordered:f,label:M?W:null,content:P?N:null},null):[i(T,{key:`label-${String(O)||h}`,class:z,style:c(c(c({},g),D),A),span:1,colon:m,component:y[0],itemPrefixCls:_,bordered:f,label:W},null),i(T,{key:`content-${String(O)||h}`,class:z,style:c(c(c({},S),D),H),span:R*2-1,component:y[1],itemPrefixCls:_,bordered:f,content:N},null)]})},{prefixCls:l,vertical:o,row:n,index:s,bordered:d}=e,{labelStyle:r,contentStyle:a}=J(q,{labelStyle:E({}),contentStyle:E({})});return o?i(Q,null,[i("tr",{key:`label-${s}`,class:`${l}-row`},[t(n,e,{component:"th",type:"label",showLabel:!0,labelStyle:r.value,contentStyle:a.value})]),i("tr",{key:`content-${s}`,class:`${l}-row`},[t(n,e,{component:"td",type:"content",showContent:!0,labelStyle:r.value,contentStyle:a.value})])]):i("tr",{key:s,class:`${l}-row`},[t(n,e,{component:d?["th","td"]:"td",type:"item",showLabel:!0,showContent:!0,labelStyle:r.value,contentStyle:a.value})])},me=e=>{const{componentCls:t,descriptionsSmallPadding:l,descriptionsDefaultPadding:o,descriptionsMiddlePadding:n,descriptionsBg:s}=e;return{[`&${t}-bordered`]:{[`${t}-view`]:{border:`${e.lineWidth}px ${e.lineType} ${e.colorSplit}`,"> table":{tableLayout:"auto",borderCollapse:"collapse"}},[`${t}-item-label, ${t}-item-content`]:{padding:o,borderInlineEnd:`${e.lineWidth}px ${e.lineType} ${e.colorSplit}`,"&:last-child":{borderInlineEnd:"none"}},[`${t}-item-label`]:{backgroundColor:s,"&::after":{display:"none"}},[`${t}-row`]:{borderBottom:`${e.lineWidth}px ${e.lineType} ${e.colorSplit}`,"&:last-child":{borderBottom:"none"}},[`&${t}-middle`]:{[`${t}-item-label, ${t}-item-content`]:{padding:n}},[`&${t}-small`]:{[`${t}-item-label, ${t}-item-content`]:{padding:l}}}}},be=e=>{const{componentCls:t,descriptionsExtraColor:l,descriptionItemPaddingBottom:o,descriptionsItemLabelColonMarginRight:n,descriptionsItemLabelColonMarginLeft:s,descriptionsTitleMarginBottom:d}=e;return{[t]:c(c(c({},le(e)),me(e)),{"&-rtl":{direction:"rtl"},[`${t}-header`]:{display:"flex",alignItems:"center",marginBottom:d},[`${t}-title`]:c(c({},ne),{flex:"auto",color:e.colorText,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG,lineHeight:e.lineHeightLG}),[`${t}-extra`]:{marginInlineStart:"auto",color:l,fontSize:e.fontSize},[`${t}-view`]:{width:"100%",borderRadius:e.borderRadiusLG,table:{width:"100%",tableLayout:"fixed"}},[`${t}-row`]:{"> th, > td":{paddingBottom:o},"&:last-child":{borderBottom:"none"}},[`${t}-item-label`]:{color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,textAlign:"start","&::after":{content:'":"',position:"relative",top:-.5,marginInline:`${s}px ${n}px`},[`&${t}-item-no-colon::after`]:{content:'""'}},[`${t}-item-no-label`]:{"&::after":{margin:0,content:'""'}},[`${t}-item-content`]:{display:"table-cell",flex:1,color:e.colorText,fontSize:e.fontSize,lineHeight:e.lineHeight,wordBreak:"break-word",overflowWrap:"break-word"},[`${t}-item`]:{paddingBottom:0,verticalAlign:"top","&-container":{display:"flex",[`${t}-item-label`]:{display:"inline-flex",alignItems:"baseline"},[`${t}-item-content`]:{display:"inline-flex",alignItems:"baseline"}}},"&-middle":{[`${t}-row`]:{"> th, > td":{paddingBottom:e.paddingSM}}},"&-small":{[`${t}-row`]:{"> th, > td":{paddingBottom:e.paddingXS}}}})}},fe=ee("Descriptions",e=>{const t=e.colorFillAlter,l=e.fontSizeSM*e.lineHeightSM,o=e.colorText,n=`${e.paddingXS}px ${e.padding}px`,s=`${e.padding}px ${e.paddingLG}px`,d=`${e.paddingSM}px ${e.paddingLG}px`,r=e.padding,a=e.marginXS,u=e.marginXXS/2,p=te(e,{descriptionsBg:t,descriptionsTitleMarginBottom:l,descriptionsExtraColor:o,descriptionItemPaddingBottom:r,descriptionsSmallPadding:n,descriptionsDefaultPadding:s,descriptionsMiddlePadding:d,descriptionsItemLabelColonMarginRight:a,descriptionsItemLabelColonMarginLeft:u});return[be(p)]});B.any;const ye=()=>({prefixCls:String,label:B.any,labelStyle:{type:Object,default:void 0},contentStyle:{type:Object,default:void 0},span:{type:Number,default:1}}),ge=K({compatConfig:{MODE:3},name:"ADescriptionsItem",props:ye(),setup(e,t){let{slots:l}=t;return()=>{var o;return(o=l.default)===null||o===void 0?void 0:o.call(l)}}}),V={xxxl:3,xxl:3,xl:3,lg:3,md:3,sm:2,xs:1};function Se(e,t){if(typeof e=="number")return e;if(typeof e=="object")for(let l=0;l<X.length;l++){const o=X[l];if(t[o]&&e[o]!==void 0)return e[o]||V[o]}return 3}function U(e,t,l){let o=e;return(l===void 0||l>t)&&(o=pe(e,{span:t})),o}function $e(e,t){const l=de(e),o=[];let n=[],s=t;return l.forEach((d,r)=>{var a;const u=(a=d.props)===null||a===void 0?void 0:a.span,p=u||1;if(r===l.length-1){n.push(U(d,s,u)),o.push(n);return}p<s?(s-=p,n.push(d)):(n.push(U(d,s,p)),o.push(n),s=t,n=[])}),o}const ve=()=>({prefixCls:String,bordered:{type:Boolean,default:void 0},size:{type:String,default:"default"},title:B.any,extra:B.any,column:{type:[Number,Object],default:()=>V},layout:String,colon:{type:Boolean,default:void 0},labelStyle:{type:Object,default:void 0},contentStyle:{type:Object,default:void 0}}),q=Symbol("descriptionsContext"),C=K({compatConfig:{MODE:3},name:"ADescriptions",inheritAttrs:!1,props:ve(),slots:Object,Item:ge,setup(e,t){let{slots:l,attrs:o}=t;const{prefixCls:n,direction:s}=oe("descriptions",e);let d;const r=E({}),[a,u]=fe(n),p=ce();ie(()=>{d=p.value.subscribe(m=>{typeof e.column=="object"&&(r.value=m)})}),se(()=>{p.value.unsubscribe(d)}),ae(q,{labelStyle:F(e,"labelStyle"),contentStyle:F(e,"contentStyle")});const L=re(()=>Se(e.column,r.value));return()=>{var m,x,f;const{size:y,bordered:w=!1,layout:M="horizontal",colon:P=!0,title:g=(m=l.title)===null||m===void 0?void 0:m.call(l),extra:S=(x=l.extra)===null||x===void 0?void 0:x.call(l)}=e,b=(f=l.default)===null||f===void 0?void 0:f.call(l),h=$e(b,L.value);return a(i("div",G(G({},o),{},{class:[n.value,{[`${n.value}-${y}`]:y!=="default",[`${n.value}-bordered`]:!!w,[`${n.value}-rtl`]:s.value==="rtl"},o.class,u.value]}),[(g||S)&&i("div",{class:`${n.value}-header`},[g&&i("div",{class:`${n.value}-title`},[g]),S&&i("div",{class:`${n.value}-extra`},[S])]),i("div",{class:`${n.value}-view`},[i("table",null,[i("tbody",null,[h.map(($,v)=>i(ue,{key:v,index:v,colon:P,prefixCls:n.value,vertical:M==="vertical",bordered:w,row:$},null))])])])]))}}});C.install=function(e){return e.component(C.name,C),e.component(C.Item.name,C.Item),e};export{C as D,ge as a};
