using Microsoft.AspNetCore.SignalR;
using Renci.SshNet;
using System.Collections.Concurrent;
using System.Data.SQLite;
using WTCMSLive.BusinessModel;
using WTCMSLive.WebSite.Core.DTOs;
using RealtimePerf.Hubs;
using System.IO;

namespace WTCMSLive.WebSite.Core.Services
{
    /// <summary>
    /// 录波数据接收进度监控服务实现
    /// </summary>
    public class RecordedDataProgressService : IRecordedDataProgressService
    {
        private readonly ILogger<RecordedDataProgressService> _logger;
        private readonly IHubContext<ServerPerformanceHub> _hubContext;

        // 存储监控状态：Key = ParkID, Value = 监控信息
        private readonly ConcurrentDictionary<string, ParkMonitorInfo> _parkMonitors = new();

        // 存储连接与风场的映射：Key = ConnectionId, Value = ParkID列表
        private readonly ConcurrentDictionary<string, HashSet<string>> _connectionParkMapping = new();

        // 定时器用于定期检查进度
        private readonly Timer _progressCheckTimer;

        public RecordedDataProgressService(
            ILogger<RecordedDataProgressService> logger,
            IHubContext<ServerPerformanceHub> hubContext)
        {
            _logger = logger;
            _hubContext = hubContext;

            // 每30秒检查一次进度
            _progressCheckTimer = new Timer(CheckProgressPeriodically, null, TimeSpan.FromSeconds(10), TimeSpan.FromSeconds(10));
        }

        private string GetUnixDirectoryName(string path)
        {
            if (string.IsNullOrEmpty(path)) return "/";

            // 移除末尾的斜杠
            path = path.TrimEnd('/');

            // 找到最后一个斜杠的位置
            var lastSlashIndex = path.LastIndexOf('/');

            return lastSlashIndex >= 0 ? path.Substring(0, lastSlashIndex) : "/";
        }
        public async Task<RecordedDataMonitorResponseDTO> StartMonitoringAsync(RecordedDataMonitorRequestDTO request, string connectionId)
        {
            try
            {
                _logger.LogInformation("[RecordedDataProgressService]开始监控风场 {ParkID} 的录波数据进度", request.ParkID);

                var response = new RecordedDataMonitorResponseDTO();
                var progressList = new List<RecordedDataProgressDTO>();

                // 获取SFTP配置
                var sftpConfig = DauManagement.GetDAUSftpConfigByParkID(request.ParkID);
                if (sftpConfig == null)
                {
                    response.Success = false;
                    response.Message = $"未找到风场 {request.ParkID} 的SFTP配置";
                    return response;
                }
                sftpConfig.CvmPushPath = GetUnixDirectoryName(sftpConfig.CvmPushPath) + "/Record";
                // 为每个DAU初始化进度监控
                foreach (var dau in request.DAUs)
                {
                    var progress = new RecordedDataProgressDTO
                    {
                        ParkID = dau.WindParkID,
                        WindTurbineID = dau.WindTurbineID ?? "",
                        DauID = dau.DauID ?? "",
                        DauIP = dau.IP ?? "",
                        IsRecordingActive = true,
                        RecordingStartTime = DateTime.Now,
                        StatusDescription = "正在初始化监控..."
                    };

                    try
                    {
                        // 检查并读取Recorded文件
                        await UpdateDAUProgressAsync(progress, sftpConfig);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "[RecordedDataProgressService]初始化DAU {WindTurbineID}_{DauID} 进度失败",
                            dau.WindTurbineID, dau.DauID);
                        progress.ErrorMessage = ex.Message;
                        progress.StatusDescription = "初始化失败";
                    }

                    progressList.Add(progress);
                }

                // 存储监控信息
                var monitorInfo = new ParkMonitorInfo
                {
                    ParkID = request.ParkID,
                    SftpConfig = sftpConfig,
                    DAUProgressList = progressList,
                    ConnectionIds = new HashSet<string> { connectionId },
                    LastUpdateTime = DateTime.Now
                };

                _parkMonitors.AddOrUpdate(request.ParkID, monitorInfo, (key, existing) =>
                {
                    existing.ConnectionIds.Add(connectionId);
                    existing.DAUProgressList = progressList;
                    existing.LastUpdateTime = DateTime.Now;
                    return existing;
                });

                // 更新连接映射
                _connectionParkMapping.AddOrUpdate(connectionId, new HashSet<string> { request.ParkID }, (key, existing) =>
                {
                    existing.Add(request.ParkID);
                    return existing;
                });

                response.Success = true;
                response.Message = "监控启动成功";
                response.ProgressList = progressList;

                _logger.LogInformation("[RecordedDataProgressService]风场 {ParkID} 监控启动成功，DAU数量: {DAUCount}",
                    request.ParkID, progressList.Count);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[RecordedDataProgressService]启动监控失败: {ParkID}", request.ParkID);
                return new RecordedDataMonitorResponseDTO
                {
                    Success = false,
                    Message = $"启动监控失败: {ex.Message}"
                };
            }
        }

        public async Task<bool> StopMonitoringAsync(string parkID, string connectionId)
        {
            try
            {
                _logger.LogInformation("[RecordedDataProgressService]停止监控风场 {ParkID}", parkID);

                // 从连接映射中移除
                if (_connectionParkMapping.TryGetValue(connectionId, out var parkIds))
                {
                    parkIds.Remove(parkID);
                    if (parkIds.Count == 0)
                    {
                        _connectionParkMapping.TryRemove(connectionId, out _);
                    }
                }

                // 从风场监控中移除连接
                if (_parkMonitors.TryGetValue(parkID, out var monitorInfo))
                {
                    monitorInfo.ConnectionIds.Remove(connectionId);

                    // 如果没有连接了，移除整个监控
                    if (monitorInfo.ConnectionIds.Count == 0)
                    {
                        _parkMonitors.TryRemove(parkID, out _);
                        _logger.LogInformation("[RecordedDataProgressService]风场 {ParkID} 所有连接已断开，停止监控", parkID);
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[RecordedDataProgressService]停止监控失败: {ParkID}", parkID);
                return false;
            }
        }

        public async Task<List<RecordedDataProgressDTO>> GetCurrentProgressAsync(string parkID)
        {
            try
            {
                if (_parkMonitors.TryGetValue(parkID, out var monitorInfo))
                {
                    return monitorInfo.DAUProgressList.ToList();
                }

                return new List<RecordedDataProgressDTO>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[RecordedDataProgressService]获取当前进度失败: {ParkID}", parkID);
                return new List<RecordedDataProgressDTO>();
            }
        }

        public async Task CleanupConnectionAsync(string connectionId)
        {
            try
            {
                if (_connectionParkMapping.TryRemove(connectionId, out var parkIds))
                {
                    foreach (var parkID in parkIds)
                    {
                        await StopMonitoringAsync(parkID, connectionId);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[RecordedDataProgressService]清理连接失败: {ConnectionId}", connectionId);
            }
        }

        public async Task TriggerProgressUpdateAsync(string parkID)
        {
            try
            {
                if (_parkMonitors.TryGetValue(parkID, out var monitorInfo))
                {
                    await UpdateParkProgressAsync(monitorInfo);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[RecordedDataProgressService]触发进度更新失败: {ParkID}", parkID);
            }
        }

        /// <summary>
        /// 定期检查所有监控的进度
        /// </summary>
        private async void CheckProgressPeriodically(object? state)
        {
            try
            {
                var tasks = _parkMonitors.Values.Select(UpdateParkProgressAsync);
                await Task.WhenAll(tasks);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[RecordedDataProgressService]定期检查进度失败");
            }
        }

        /// <summary>
        /// 更新风场的进度信息
        /// </summary>
        private async Task UpdateParkProgressAsync(ParkMonitorInfo monitorInfo)
        {
            try
            {
                var hasUpdates = false;

                foreach (var progress in monitorInfo.DAUProgressList)
                {
                    var oldReceivedCount = progress.ReceivedFileCount;
                    await UpdateDAUProgressAsync(progress, monitorInfo.SftpConfig);

                    if (progress.ReceivedFileCount != oldReceivedCount)
                    {
                        hasUpdates = true;
                    }
                }

                if (hasUpdates)
                {
                    monitorInfo.LastUpdateTime = DateTime.Now;

                    // 通知所有连接的客户端
                    var groupName = $"Park_{monitorInfo.ParkID}";
                    await _hubContext.Clients.Group(groupName).SendAsync("ProgressUpdate", monitorInfo.DAUProgressList);

                    _logger.LogDebug("[RecordedDataProgressService]风场 {ParkID} 进度已更新", monitorInfo.ParkID);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[RecordedDataProgressService]更新风场进度失败: {ParkID}", monitorInfo.ParkID);
            }
        }

        /// <summary>
        /// 更新单个DAU的进度信息
        /// </summary>
        private async Task UpdateDAUProgressAsync(RecordedDataProgressDTO progress, CMSFramework.DAUEntities.DAUSftpConfig sftpConfig)
        {
            SftpClient? client = null;
            string? recordedFilePath = null;

            try
            {
                progress.LastUpdateTime = DateTime.Now;
                progress.StatusDescription = "正在检查进度...";

                // 创建SFTP连接，供后续操作共享使用,后续可使用连接池，先这样吧
                client = new SftpClient(sftpConfig.SftpAddress, int.Parse(sftpConfig.SftpPort), sftpConfig.SftpUsername, sftpConfig.SftpPassword);
                client.ConnectionInfo.Timeout = TimeSpan.FromSeconds(30);
                await Task.Run(() => client.Connect());

                if (!client.IsConnected)
                {
                    progress.StatusDescription = "SFTP连接失败";
                    return;
                }

                // 1. 查找并下载Recorded文件
                recordedFilePath = await FindAndDownloadRecordedFileAsync(progress, sftpConfig, client);
                if (string.IsNullOrEmpty(recordedFilePath))
                {
                    progress.StatusDescription = "未找到Recorded文件";
                    return;
                }

                // 2. 读取rcdata表获取预期文件列表
                var rcdataRecords = await ReadRcdataFromSqliteAsync(recordedFilePath);
                progress.TotalFileCount = rcdataRecords.Count;

                if (progress.TotalFileCount == 0)
                {
                    progress.StatusDescription = "Recorded文件中无数据记录";
                    return;
                }

                // 3. 检查SFTP上的文件，判断哪些已接收
                var receivedCount = await CheckReceivedFilesAsync(rcdataRecords, sftpConfig, progress.RecordingStartTime, client);
                progress.ReceivedFileCount = receivedCount;

                // 4. 更新状态描述
                if (progress.ReceivedFileCount >= progress.TotalFileCount)
                {
                    progress.StatusDescription = "数据接收完成";
                }
                else
                {
                    progress.StatusDescription = $"正在接收数据 ({progress.ReceivedFileCount}/{progress.TotalFileCount})";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[RecordedDataProgressService]更新DAU进度失败: {WindTurbineID}_{DauID}",
                    progress.WindTurbineID, progress.DauID);
                progress.ErrorMessage = ex.Message;
                progress.StatusDescription = "更新进度失败";
            }
            finally
            {
                // 清理SFTP连接
                try
                {
                    if (client?.IsConnected == true)
                    {
                        client.Disconnect();
                    }
                    client?.Dispose();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "[RecordedDataProgressService]关闭SFTP连接失败");
                }

                // 清理临时文件
                if (!string.IsNullOrEmpty(recordedFilePath) && File.Exists(recordedFilePath))
                {
                    try
                    {
                        File.Delete(recordedFilePath);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "[RecordedDataProgressService]删除临时文件失败: {FilePath}", recordedFilePath);
                    }
                }
            }
        }

        /// <summary>
        /// 查找并下载Recorded文件（使用已建立的SFTP连接）
        /// </summary>
        private async Task<string?> FindAndDownloadRecordedFileAsync(RecordedDataProgressDTO progress, CMSFramework.DAUEntities.DAUSftpConfig sftpConfig, SftpClient client)
        {
            try
            {
                // 构造Recorded文件名模式：WindTurbineID_DauID_*.rc
                var filePattern = $"{progress.ParkID}_{progress.WindTurbineID}_{progress.DauID}_";

                // 在SFTP根目录查找文件,需求变更，之前为根路径，现在tvm，cvm，bvm区分
                var files = await Task.Run(() => client.ListDirectory(sftpConfig.CvmPushPath));
                //var recordedFile = files.FirstOrDefault(f => f.IsRegularFile &&
                //    f.Name.StartsWith(filePattern) &&
                //    f.Name.EndsWith(".record") &&
                //    f.LastWriteTime >= (progress.RecordingStartTime ?? DateTime.MinValue));

                var recordedFile = files.FirstOrDefault(f => f.IsRegularFile &&
                    f.Name.StartsWith(filePattern) &&
                    f.Name.EndsWith(".record"));

                if (recordedFile == null)
                {
                    return null;
                }

                // 下载文件到临时目录
                var tempDir = Path.GetTempPath();
                var tempFilePath = Path.Combine(tempDir, $"recorded_{Guid.NewGuid()}_{recordedFile.Name}");

                using var fileStream = File.Create(tempFilePath);
                await Task.Run(() => client.DownloadFile(recordedFile.FullName, fileStream));

                return tempFilePath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[RecordedDataProgressService]查找并下载Recorded文件失败: {WindTurbineID}_{DauID}",
                    progress.WindTurbineID, progress.DauID);
                return null;
            }
        }

        /// <summary>
        /// 从SQLite文件中读取rcdata表数据
        /// </summary>
        private async Task<List<RcdataRecordDTO>> ReadRcdataFromSqliteAsync(string sqliteFilePath)
        {
            var records = new List<RcdataRecordDTO>();

            try
            {
                var connectionString = $"Data Source={sqliteFilePath};Read Only=True;";
                using var connection = new SQLiteConnection(connectionString);
                await connection.OpenAsync();

                // 检查rcdata表是否存在
                var tableExistsQuery = "SELECT name FROM sqlite_master WHERE type='table' AND name='wavedirectory';";
                using var tableExistsCmd = new SQLiteCommand(tableExistsQuery, connection);
                var tableExists = await tableExistsCmd.ExecuteScalarAsync();

                if (tableExists == null)
                {
                    _logger.LogWarning("[RecordedDataProgressService]rcdata表不存在于SQLite文件中: {FilePath}", sqliteFilePath);
                    return records;
                }

                // 读取rcdata表数据
                var dataQuery = "SELECT * FROM wavedirectory;";
                using var dataCmd = new SQLiteCommand(dataQuery, connection);
                using var dataReader = await dataCmd.ExecuteReaderAsync();

                while (await dataReader.ReadAsync())
                {
                    var record = new RcdataRecordDTO();

                    // 根据实际表结构读取字段，这里假设常见的字段名
                    for (int i = 0; i < dataReader.FieldCount; i++)
                    {
                        var columnName = dataReader.GetName(i).ToLower();
                        var value = dataReader.IsDBNull(i) ? null : dataReader.GetValue(i);

                        switch (columnName)
                        {
                            case "filename":
                            case "file_name":
                            case "pathname":
                                record.FileName = value?.ToString() ?? "";
                                break;
                            case "filepath":
                            case "file_path":
                                record.FilePath = value?.ToString() ?? "";
                                break;
                            case "collectiontime":
                            case "collection_time":
                            case "collecttime":
                            case "measdate":
                                if (value != null && DateTime.TryParse(value.ToString(), out var collectionTime))
                                {
                                    record.CollectionTime = collectionTime;
                                }
                                break;
                        }

                    }

                    if (!string.IsNullOrEmpty(record.FileName))
                    {
                        records.Add(record);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[RecordedDataProgressService]读取rcdata表失败: {FilePath}", sqliteFilePath);
            }

            return records;
        }

        /// <summary>
        /// 检查SFTP上已接收的文件数量（使用已建立的SFTP连接）
        /// </summary>
        private async Task<int> CheckReceivedFilesAsync(List<RcdataRecordDTO> rcdataRecords, CMSFramework.DAUEntities.DAUSftpConfig sftpConfig, DateTime? recordingStartTime, SftpClient client)
        {
            try
            {
                var receivedCount = 0;

                foreach (var record in rcdataRecords)
                {
                    try
                    {
                        // 构造完整的文件路径
                        //var fullPath = record.FilePath;
                        //if (!fullPath.StartsWith("/"))
                        //{
                        //    // sftpPath 更改为CvmPushPath，先占位，此逻辑需要优化，当前使用数据抓取方式，此逻辑暂时弃用
                        //    fullPath = $"{sftpConfig.CvmPushPath.TrimEnd('/')}/{record.FilePath.TrimStart('/')}";
                        //}

                        //var fullPath = Path.Combine(sftpConfig.CvmPushPath, record.FileName);
                        var fullPath =sftpConfig.CvmPushPath + "/" + record.FileName;

                        // 检查文件是否存在
                        if (client.Exists(fullPath))
                        {
                            // 获取文件信息
                            var fileInfo = client.GetAttributes(fullPath);

                            // 判断文件创建时间是否在录波开始时间之后
                            if (recordingStartTime.HasValue)
                            {
                                //if (fileInfo.LastWriteTime >= recordingStartTime.Value)
                                //{
                                //    receivedCount++;
                                //    record.IsReceived = true;
                                //    record.FileCreationTime = fileInfo.LastWriteTime;
                                //}

                                receivedCount++;
                                record.IsReceived = true;
                                record.FileCreationTime = fileInfo.LastWriteTime;
                            }
                            else
                            {
                                // 如果没有录波开始时间，只要文件存在就算接收
                                receivedCount++;
                                record.IsReceived = true;
                                record.FileCreationTime = fileInfo.LastWriteTime;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug(ex, "[RecordedDataProgressService]检查文件失败: {FilePath}", record.FilePath);
                        // 继续检查其他文件
                    }
                }

                return receivedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[RecordedDataProgressService]检查已接收文件失败");
                return 0;
            }
        }
    }

    /// <summary>
    /// 风场监控信息
    /// </summary>
    internal class ParkMonitorInfo
    {
        public string ParkID { get; set; } = string.Empty;
        public CMSFramework.DAUEntities.DAUSftpConfig SftpConfig { get; set; } = null!;
        public List<RecordedDataProgressDTO> DAUProgressList { get; set; } = new();
        public HashSet<string> ConnectionIds { get; set; } = new();
        public DateTime LastUpdateTime { get; set; }
    }
}