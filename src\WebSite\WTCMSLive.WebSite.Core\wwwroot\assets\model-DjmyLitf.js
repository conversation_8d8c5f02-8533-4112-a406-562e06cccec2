import{T as a,cA as s,cB as c,cC as n,cD as l,cE as d,cF as h,cG as u,cH as i,cI as p,cJ as w,cK as y,cL as m}from"./index-sMW2Pm6g.js";import{b as t}from"./tools-DC78Tda0.js";const P=a("model",{state:()=>({modelList:[],modelOptions:[]}),actions:{reset(){this.$reset()},async fetchModellist(){try{const r=await m();return r&&r.length>0&&(this.modelList=r,this.modelOptions=t(r,{label:"turbineModel",value:"turbineModel"},{nother:!0})),r}catch(r){throw console.error("获取机型失败:",r),r}},async fetchWTParamGetWtTower(r={}){try{const e=await y(r);return this.wtTower=e,e}catch(e){throw console.error(e),e}},async fetchWTParamGetWtNaceller(r={}){try{const e=await w(r);return this.wtNaceller=e,e}catch(e){throw console.error(e),e}},async fetchWTParamGetWtBlade(r={}){try{const e=await p(r);return this.wtBlade=e,e}catch(e){throw console.error(e),e}},async fetchAddOrUpdateWtTowerParameter(r){try{return await i(r)}catch(e){throw console.error(e),e}},async fetchAddOrUpdateWtNacelleParameter(r){try{return await u(r)}catch(e){throw console.error(e),e}},async fetchAddOrUpdateWtBladeParameter(r){try{return await h(r)}catch(e){throw console.error(e),e}},async fetchGetGearboxModels(){try{const r=await d();let e=t(r,{label:"key",value:"key"},{nother:!0});return this.gearboxModels=e,e}catch(r){throw console.error(r),r}},async fetchBatchDelTrubineModel(r){try{return await l(r)}catch(e){throw console.error(e),e}},async fetchGetModelStructureType(r){try{const e=await n(r);let o=t(e,{label:"value",value:"key",text:"value"},{nother:!0});return this.modelStructureType=o,o}catch(e){throw console.error(e),e}},async fetchAddWindModel(r){try{return await c(r)}catch(e){throw console.error(e),e}},async fetchUpdateTurbineModel(r){try{return await s(r)}catch(e){throw console.error(e),e}}}});export{P as u};
