import{f as u,d as o,a as c,u as m,o as p,b as _,g as i,t as l,v as f}from"./index-sMW2Pm6g.js";import{R as b}from"./index-BKrPWEEO.js";import{B as d}from"./index-Bi-LLAnN.js";const y={__name:"404",setup(k){const e=c(),s=m(),a=()=>{f(s,e)};return(t,B)=>{const r=d,n=b;return p(),u(n,{status:"404",title:"404","sub-title":t.$t("tip.404")},{extra:o(()=>[_(r,{type:"primary",onClick:a},{default:o(()=>[i(l(t.$t("button.backHome")),1)]),_:1})]),_:1},8,["sub-title"])}}};export{y as default};
