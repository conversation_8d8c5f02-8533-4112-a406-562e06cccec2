import{_ as ee,S as te}from"./index-5GdjZhcp.js";import{_ as ae}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{B as le}from"./index-Bi-LLAnN.js";import{F as ne}from"./index-ljzR8VKX.js";import{Y as j,r as B,w as P,b6 as se,c as _,o as m,b as R,d as w,i as $,q as h,f as F,g as N,F as q,b7 as E,t as z,p as W,m as oe}from"./index-sMW2Pm6g.js";import{_ as ie}from"./table-DF7YFrUK.js";import{a as re}from"./ActionButton-DMeJvyLo.js";const de="/icons/copy-up.png",ue=(f,I)=>{if(I!==3&&I!==4)throw new Error("递增位必须是 3 或 4");if(!f||f=="")return;const p=f.split(".").map(i=>parseInt(i,10));if(p.length!==4||p.some(i=>isNaN(i)||p.some(s=>s<0||s>255)))return;const k=I-1;p[k]+=1;for(let i=k;i>=0;i--)p[i]>255&&(p[i]=0,i>0?p[i-1]+=1:p[i]=255);return p.join(".")},ce=(f=0,I=1)=>{if(I<=0||!Number.isInteger(I))throw new Error("步长必须是正整数");if(typeof f=="number")return f+I;if(typeof f=="string"){const p=f.match(/(\d+)$/);if(p){const k=f.slice(0,p.index),i=p[1],g=parseInt(i,10)+I,O=String(g).padStart(i.length,"0");return O.length>i.length,k+O}else return f}throw new Error("起始值必须是数字或数字结尾的字符串")},fe={class:"tableBox"},pe={class:"tableBox"},be={key:0,class:"requiredStyle"},ve={key:1,class:"copy-up-icon",src:de,title:"默认同上"},me={key:2,class:"autoIncrementorBox"},ye=["title","onClick"],he={class:"autoIncrementorBoxContentBox"},ge={key:0,class:"actions"},_e={key:0,class:"addRow"},Ie={key:0,class:"footer"},Ce={class:W(["footer-btn"])},we={key:0,class:"btnitem"},ke={key:1,class:"btnitem"},Re={__name:"index",props:{tableColumns:Array,tableDatas:Array,recordKey:String,tableTitle:String,tableOperate:{type:Array,default:()=>[]},noCopyUpKeys:{type:Array,default:()=>[]},removeDuplicateKeys:{type:Array,default:()=>[]},noRepeatKeys:{type:Array,default:()=>[]},noCopyUpAll:{type:Boolean,default:()=>!1},addBtnDisabled:Boolean,size:String,noPagination:Boolean,noForm:Boolean},emits:["addRow","deleteRow","editRow","submit","hangeTableFormChange","update:modelValue"],setup(f,{expose:I,emit:p}){const k=[{value:3,label:"第3位"},{value:4,label:"第4位"}],i=j({}),s=f,g=j({autoIncrementorNames:{},autoIncrementorRules:{}}),O=e=>{g.autoIncrementorNames[e]=!g.autoIncrementorNames[e]},M=(e,a)=>{g.autoIncrementorRules[a]={value:e}},G=e=>e.isrequired||e.validateRules&&e.validateRules.length&&e.validateRules[0].required,A=e=>{var n,r;let a=e.filter(o=>!o.columnHidden);return(n=s.removeDuplicateKeys)!=null&&n.length&&a.forEach(o=>{s.removeDuplicateKeys.includes(o.dataIndex)&&(o.hasChangeEvent=!0)}),(r=s.tableOperate)!=null&&r.length&&a.push({title:"操作",key:"action",dataIndex:"action"}),a},V=B(null),L=B(A(s.tableColumns)),x=p;let K={key:Date.now()};for(let e=0;e<s.tableColumns.length;e++)if(s.tableColumns[e].initValue||s.tableColumns[e].initValue===0){K[s.tableColumns[e].dataIndex]=s.tableColumns[e].initValue;const a=`${s.tableColumns[e].dataIndex}[0]`;i[a]=s.tableColumns[e].initValue}const y=B(s.tableDatas.length?s.tableDatas:[K]),C=B({});P(()=>s.tableDatas,e=>{e.forEach((a,n)=>{s.tableColumns.forEach(r=>{if(!r.noEdit){const o=`${r.dataIndex}[${n}]`;i[o]=a[r.dataIndex]}})})},{immediate:!0});const H=()=>{let e=y.value.length,a={key:Date.now()};y.value.push(a),T(e)};P(()=>s.tableColumns,e=>{L.value=A(e)},{immediate:!0});const Y=(e,a,n,r)=>{const o=`${e}[${a}]`;i[o]=r,y.value[a][e]=r,V.value&&e&&n&&n.validate&&setTimeout(()=>{V.value.validateFields([o])},100)},J=(e,a,n)=>{var r,o;if((r=s.removeDuplicateKeys)!=null&&r.length&&s.removeDuplicateKeys.includes(n)&&(e&&e.value&&typeof e.value=="object"&&e.value!==null?C.value[n]=[...C.value[n]||[],e.value.value]:C.value[n]=[...C.value[n]||[],e.value],C.value[n]&&C.value[n].length>1))for(let c=0;c<L.value.length;c++){let t=L.value[c];if(t.dataIndex==n)for(let d=0;d<t.tableList.length;d++){if(d==a)continue;let l=i[`${t.dataIndex}[${d}]`],u=(o=t.selectOptions)==null?void 0:o.filter(b=>l&&l==b.value||!C.value[t.dataIndex].includes(b.value));t.tableList[d]={...t.tableList[d],selectOptions:u}}}e&&e.value&&typeof e.value=="object"&&e.value!==null?x("hangeTableFormChange",{value:{...e.value,dataIndex:e.dataIndex},dataIndex:n,index:a}):x("hangeTableFormChange",{value:e.value,dataIndex:n,index:a})},Q=e=>{x("deleteRow",e);let a=y.value.filter(n=>n.key!==e.key);a.forEach((n,r)=>{Object.keys(n).forEach(o=>{const c=`${o}[${r}]`;i[c]=n[o]})}),y.value=a},T=e=>{const a={};let n=L.value;n.forEach(t=>{var d,l,u;if(!t.noEdit){const b=`${t.dataIndex}[${e-1}]`;if(t.columnOperate&&g.autoIncrementorNames[t.dataIndex])switch(t.columnOperate.type){case"number":a[t.dataIndex]=ce(i[b],1);break;case"ip":let v=((d=g.autoIncrementorRules[t.dataIndex])==null?void 0:d.value)||4;a[t.dataIndex]=ue(i[b],v);break}else a[t.dataIndex]=i[b]}if((l=s.removeDuplicateKeys)!=null&&l.length){if(s.removeDuplicateKeys.includes(t.dataIndex)&&(t.tableList||(t.tableList=[]),t.tableList&&t.tableList.length&&e>=t.tableList.length&&!t.tableList[e]&&t.tableList.push({}),C.value[t.dataIndex]&&C.value[t.dataIndex].length)){let b=(u=t.selectOptions)==null?void 0:u.filter(v=>!C.value[t.dataIndex].includes(v.value));t.tableList[e]={...t.tableList[e-1],selectOptions:b}}}else t.tableList&&t.tableList.length&&(e>=t.tableList.length&&!t.tableList[e]&&t.tableList.push({}),t.tableList[e]={...t.tableList[e-1]})});const o={...y.value[e]};L.value=[...n],Object.keys(a).forEach(t=>{if(!s.noCopyUpAll&&!s.noCopyUpKeys.includes(t)&&t!=="key"){o[t]=a[t];const d=`${t}[${e}]`;i[d]=a[t]}});const c=[...y.value];c[e]=o,y.value=[...c]},X=()=>{x("cancel")},Z=e=>{var n,r;let a=!0;try{if((n=s.noRepeatKeys)!=null&&n.length){let o="",c={};for(let t=0;t<Object.keys(e).length;t++){const d=Object.keys(e)[t];let l=d.split("["),u=d;if(l.length>1&&(u=l[0]),s.noRepeatKeys.includes(u))if(!c[u])c[u]=[],c[u].push(e[d]);else{if(c[u].includes(e[d])){let b=(r=s.tableColumns.find(S=>S.dataIndex==u))==null?void 0:r.title;o=`第${l[1].split("]")[0]*1+1}行: ${b} 不能重复!`,oe.error(o),a=!1;break}c[u].push(e[d])}}}}catch(o){console.log(o)}a&&x("submit",e)};return I({getTableFieldsValue:()=>se(i),setTableFieldValue:e=>{const{formDataIndex:a,tableDataIndex:n,index:r,value:o}=e;i[a]=o;let c={...y.value[r]},t=[...y.value];c[n]=o,t[r]=c,y.value=[...t]},clearValidate:e=>{V.value.clearValidate(e)}}),(e,a)=>{const n=te,r=re,o=le,c=ie,t=ne;return m(),_("div",fe,[R(t,{ref_key:"formRef",ref:V,model:i,onFinish:Z},{default:w(()=>{var d;return[$("div",pe,[R(c,{bordered:"",columns:L.value,"data-source":y.value,pagination:!1,size:f.size||"middle"},{headerCell:w(({column:l})=>[G(l)?(m(),_("span",be,"*")):h("",!0),!s.noCopyUpKeys.includes(l.dataIndex)&&l.dataIndex!=="action"?(m(),_("img",ve)):h("",!0),N(" "+z(l.title)+" ",1),l.columnOperate?(m(),_("div",me,[$("span",{class:W(["title",g.autoIncrementorNames[l.dataIndex]?"activeTitle":"noActiveTitle"]),title:`${g.autoIncrementorNames[l.dataIndex]?"关闭":"开启"}递增(数字或以数字结尾)`,onClick:u=>O(l.dataIndex)},null,10,ye),l.columnOperate&&l.columnOperate.type=="ip"&&g.autoIncrementorNames[l.dataIndex]?(m(),F(r,{key:0,placement:"bottom",overlayClassName:"myPopover",trigger:"click"},{content:w(()=>{var u;return[$("div",he,[a[1]||(a[1]=$("span",null,"递增位：",-1)),R(n,{ref:"select",options:k,style:{width:"100px"},value:((u=g.autoIncrementorRules[l.dataIndex])==null?void 0:u.value)||4,onChange:b=>M(b,l.dataIndex)},null,8,["value","onChange"])])]}),default:w(()=>[a[2]||(a[2]=$("span",{class:"operate"},"IP递增位",-1))]),_:2,__:[2]},1024)):h("",!0)])):h("",!0),l.headerSlotName?E(e.$slots,l.headerSlotName,{key:3},void 0,!0):h("",!0)]),bodyCell:w(({column:l,record:u,text:b,index:v})=>{var S,U;return[l&&l.dataIndex&&l.dataIndex==="action"?(m(),_("div",ge,[(S=f.tableOperate)!=null&&S.includes("copyUp")&&v>0&&!f.noCopyUpAll?(m(),F(o,{key:0,onClick:D=>T(v)},{default:w(()=>a[3]||(a[3]=[N("同上",-1)])),_:2,__:[3]},1032,["onClick"])):h("",!0),(U=f.tableOperate)!=null&&U.includes("delete")?(m(),F(o,{key:1,onClick:D=>Q(u)},{default:w(()=>a[4]||(a[4]=[N("删除",-1)])),_:2,__:[4]},1032,["onClick"])):h("",!0)])):l&&!l.noEdit?(m(),_(q,{key:1},[$("div",null,[R(ee,{class:"formItem",notshowLabels:!0,itemProps:{...l,formItemWidth:l.columnWidth,dataIndex:`${l.dataIndex}[${v}]`,...l.tableList&&l.tableList[v]?l.tableList[v]:{}},modelValue:i[`${l.dataIndex}[${v}]`],"onUpdate:modelValue":D=>Y(l.dataIndex,v,{validate:l.inputType=="selectinput"},D),onOnchangeSelect:D=>J(D,v,l.dataIndex)},null,8,["itemProps","modelValue","onUpdate:modelValue","onOnchangeSelect"])]),l.afterContent?E(e.$slots,l.afterContentName||"afterContent",{key:0,column:l,record:u,index:v,text:b},void 0,!0):h("",!0)],64)):(m(),_(q,{key:2},[N(z(b),1)],64))]}),_:3},8,["columns","data-source","size"]),(d=f.tableOperate)!=null&&d.includes("noAdd")?h("",!0):(m(),_("div",_e,[R(o,{onClick:a[0]||(a[0]=l=>H())},{default:w(()=>a[5]||(a[5]=[N("添加一行",-1)])),_:1,__:[5]})]))]),f.noForm?h("",!0):(m(),_("div",Ie,[E(e.$slots,"footer",{},void 0,!0),$("div",Ce,[s.noCancle?h("",!0):(m(),_("div",we,[R(o,{onClick:X},{default:w(()=>a[6]||(a[6]=[N("取消",-1)])),_:1,__:[6]})])),s.noSubmit?h("",!0):(m(),_("div",ke,[R(o,{type:"primary","html-type":"submit"},{default:w(()=>a[7]||(a[7]=[N("确定",-1)])),_:1,__:[7]})]))])]))]}),_:3},8,["model"])])}}},Se=ae(Re,[["__scopeId","data-v-3a3d4745"]]);export{Se as W};
