{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Information"}}, "Kestrel": {"Endpoints": {"Http": {"Url": "http://*:803"}}}, "AllowedHosts": "*", "ProxyTo": "", "JwtSettings": {"SecretKey": "aB3x9Lq2mN7vP4zR8tY1uK5wQ6eJ0cF!@#XyZ%tH8*gD2$sF7&vL9qW", "Issuer": "WTCMSLive", "Audience": "WTCMSLiveUsers", "ExpirationInMinutes": 1440, "RefreshGracePeriodInMinutes": 10}, "DbType": {"checked": "sqlite", "example": "[sqlite, mysql, seabox]"}, "ConnectionStrings": {"Context": "data source=D:\\WindCMS\\DB\\GW2026\\gateway_website11\\wtlivedb.db", "ContextRt": "data source=D:\\WindCMS\\DB\\GW2026\\gateway_website11\\wtlivedb.db", "ContextTrend": "data source=D:\\WindCMS\\DB\\GW2026\\gateway_website\\wtlivedbtrend.db"}, "LogoUrl": "/Images/logo01.svg", "ViewModel": "CMS", "showComplexEigenValue": "False", "DaqServerType": "DAU", "serverXMConfigPath": "D:\\windCMS", "MetricsCollection": {"Enable": false, "DataStoragePath": "MetricsData", "RetentionDays": 30, "FileFormat": "csv"}, "DataCenter": {"Url": "http://**************:8011/", "TimeoutSeconds": 30}}