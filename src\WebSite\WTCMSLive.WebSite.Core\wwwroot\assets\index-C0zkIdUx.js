import{r as E,w as Ue,j as xe,b6 as ut,c as _,o as T,i as R,q as $,b as A,d as q,f as ue,g as U,F as Te,t as he,b7 as rt,p as We,b8 as dt,u as ct,Y as vt,e as Oe,s as B,b9 as pt,ba as bt,bb as mt,m as h,as as Ne}from"./index-sMW2Pm6g.js";import{_ as Dt,u as ft,C as ht,W as oe}from"./table-DF7YFrUK.js";import{_ as It,O as ie}from"./index-5GdjZhcp.js";import{_ as $e}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{B as Se}from"./index-Bi-LLAnN.js";import{W as gt}from"./index-LWsVge70.js";import{g as se,f as fe,b as yt}from"./tools-DC78Tda0.js";import{u as Lt}from"./measurementDefinition-CnogMNea.js";import{u as wt}from"./devTree-BcT4pWCP.js";import{u as Tt}from"./configModbus-pT2N9qql.js";import{S as kt}from"./ActionButton-DMeJvyLo.js";import{D as At,a as Rt}from"./index-V7t8PYAK.js";import{M as xt}from"./index-sJyM5xm7.js";import"./styleChecker-LI4Lr2UF.js";import"./index-DRy8se-f.js";import"./shallowequal-D09g54zQ.js";import"./index-CkAETRMb.js";import"./index-BLrmE7-A.js";import"./index-ljzR8VKX.js";const Ct={class:"tableBox"},Ft={key:0,class:"requiredStyle"},_t={key:0},Vt={key:1},qt={class:"addRow"},Et={key:0,class:"footer"},Ot={class:We(["footer-btn"])},Nt={key:0,class:"btnitem"},Mt={key:1,class:"btnitem"},Ut={__name:"TableBoxComponent",props:{tableColumns:Array,tableDatas:Array,recordKey:String,tableTitle:String,tableOperate:{type:Array,default:()=>[]},noCopyUpKeys:{type:Array,default:()=>[]},addBtnDisabled:Boolean,size:String,noPagination:Boolean,noForm:Boolean,noCopyUp:Boolean,modelValue:{type:Object,default:()=>({})},defaultEmptyTable:Boolean},emits:["addRow","deleteRow","editRow","submit","hangeTableFormChange","update:modelValue"],setup(n,{expose:ve,emit:re}){const i=n,x=E({...i.modelValue}),P=re,k=E(i.tableDatas&&i.tableDatas.length?i.tableDatas:[{key:Date.now()}]),pe=u=>u.isrequired||u.validateRules&&u.validateRules.length&&u.validateRules[0].required;Ue(()=>i.tableDatas,u=>{k.value=u&&u.length?u:i.defaultEmptyTable?[]:[{key:Date.now()}],!u||!u.length?x.value={}:u.forEach((d,D)=>{i.tableColumns.forEach(g=>{if(!g.noEdit){const l=`${g.dataIndex}[${D}]`;x.value[l]=d[g.dataIndex]}})})},{immediate:!0});const J=()=>{const u=i.noCopyUp?{key:Date.now()}:{...k.value[k.value.length-1],key:Date.now()},d=k.value.length;i.tableColumns.forEach(D=>{if(!D.noEdit){const g=`${D.dataIndex}[${d}]`;x.value[g]=u[D.dataIndex]}}),k.value.push(u)},L=(u,d,D)=>{const g=`${u}[${d}]`;x.value[g]=D,k.value[d][u]=D,P("update:modelValue",{...i.modelValue,...x.value,updateModelValue:{value:D,dataIndex:u,index:d}})},F=(u,d,D)=>{P("hangeTableFormChange",{value:u,dataIndex:D,index:d})&&P("hangeTableFormChange",{value:u,dataIndex:D,index:d})},w=u=>{P("deleteRow",u);let d=k.value.filter(D=>D.key!==u.key);d.forEach((D,g)=>{Object.keys(D).forEach(l=>{const f=`${l}[${g}]`;x.value[f]=D[l]})}),P("update:modelValue",{...x.value}),k.value=d},C=u=>{const d={};i.tableColumns.forEach(f=>{if(f.canEdit){const X=`${f.dataIndex}[${u-1}]`;d[f.dataIndex]=x.value[X]}});const g={...k.value[u]};Object.keys(d).forEach(f=>{if(!i.noCopyUpKeys.includes(f)&&f!=="key"){g[f]=d[f];const X=`${f}[${u}]`;x.value[X]=d[f]}});const l=[...k.value];l[u]=g,k.value=[...l],P("update:modelValue",{...i.modelValue,...x.value})},V=xe(()=>{var d;const u=i.tableColumns.filter(D=>!D.columnHidden);return(d=i.tableOperate)!=null&&d.length&&u.push({title:"操作",key:"action",dataIndex:"action"}),u});return ve({getFieldsValue:()=>ut(x.value),setFieldValues:u=>{x.value={...u}},setFieldValue:(u,d)=>{x.value[u]=d}}),(u,d)=>{const D=Se,g=Dt;return T(),_(Te,null,[R("div",Ct,[A(g,{bordered:"",columns:V.value,"data-source":k.value,pagination:!n.noPagination&&k.value.length>10,size:n.size||"middle"},{headerCell:q(({column:l})=>[pe(l)?(T(),_("span",Ft,"*")):$("",!0),U(" "+he(l.title),1)]),bodyCell:q(({column:l,record:f,text:X,index:K})=>{var p,Y;return[l.key==="action"?(T(),_("div",_t,[(p=n.tableOperate)!=null&&p.includes("copyUp")&&K>0?(T(),ue(D,{key:0,onClick:z=>C(K)},{default:q(()=>d[1]||(d[1]=[U("同上",-1)])),_:2,__:[1]},1032,["onClick"])):$("",!0),(Y=n.tableOperate)!=null&&Y.includes("delete")?(T(),ue(D,{key:1,onClick:z=>w(f)},{default:q(()=>d[2]||(d[2]=[U("删除",-1)])),_:2,__:[2]},1032,["onClick"])):$("",!0)])):l.noEdit?(T(),_(Te,{key:2},[U(he(X),1)],64)):(T(),_("div",Vt,[A(It,{class:"formItem",notshowLabels:!0,itemProps:{...l,formItemWidth:l.columnWidth,dataIndex:l.dataIndex+"["+K+"]"},modelValue:x.value[l.dataIndex+"["+K+"]"],"onUpdate:modelValue":z=>L(l.dataIndex,K,z),onOnchangeSelect:z=>l.hasChangeEvent?F(z,K,l.dataIndex):null},null,8,["itemProps","modelValue","onUpdate:modelValue","onOnchangeSelect"])]))]}),_:1},8,["columns","data-source","pagination","size"]),R("div",qt,[A(D,{onClick:d[0]||(d[0]=l=>J())},{default:q(()=>d[3]||(d[3]=[U("添加一行",-1)])),_:1,__:[3]})])]),n.noForm?$("",!0):(T(),_("div",Et,[rt(u.$slots,"footer",{},void 0),R("div",Ot,[i.noCancle?$("",!0):(T(),_("div",Nt,[A(D,{onClick:u.cancelForm},{default:q(()=>d[4]||(d[4]=[U("取消",-1)])),_:1,__:[4]},8,["onClick"])])),i.noSubmit?$("",!0):(T(),_("div",Mt,[A(D,{type:"primary","html-type":"submit"},{default:q(()=>d[5]||(d[5]=[U("确定",-1)])),_:1,__:[5]})]))])]))],64)}}},Me=$e(Ut,[["__scopeId","data-v-65f902f1"]]),I=320,Q=n=>{let ve=[{align:"center",formItemWidth:I,title:"测量定义名称",dataIndex:"measDefinitionName",isrequired:!0},{align:"center",title:"测量定义状态",formItemWidth:I,dataIndex:"isAvailable",inputType:"radio",isrequired:!0,selectOptions:[{value:"1",label:"启用"},{value:"0",label:"禁用"}]},{align:"center",title:"采集单元",dataIndex:"dauID",isrequired:!0,inputType:"select",selectOptions:[],disabled:n&&n.edit,hasChangeEvent:!0,formItemWidth:I},{align:"center",title:"采集间隔",dataIndex:"daqInterval",formItemWidth:I,validateRules:se({title:"采集间隔",type:"integer",required:!0})},{align:"center",title:"采集模式",formItemWidth:I,dataIndex:"modelType",inputType:"select",selectOptions:[{label:"主动",value:0},{label:"被动",value:1}]},{align:"center",title:"Modbus设备",formItemWidth:I,dataIndex:"modbusDeviceIDs",inputType:"checkbox",selectOptions:[],disabled:n&&n.edit},{align:"center",title:"采集间隔单位",dataIndex:"daqIntervalUnit",inputType:"select",formItemWidth:I,selectOptions:[],isrequired:!0}],re=[{align:"center",title:"波形定义名称",dataIndex:"waveDefinitionName",isrequired:!0,formItemWidth:I},{title:n&&n.isForm?"测量定义名称":"振动测量位置",dataIndex:n&&n.isForm?"measDefinitionName":"measLocationID",align:"center",disabled:!0,isrequired:!0,formItemWidth:I},{align:"center",title:"信号类型",dataIndex:"signalType",isrequired:!0,inputType:"select",selectOptions:[],formItemWidth:I,headerOperations:{filters:[],filterOptions:n&&n.waveTypeList&&n.waveTypeList.length>0?n.waveTypeList:[]},...n&&!n.isForm&&n.waveTypeList?{customRender:({text:L,record:F,index:w,column:C})=>{const V=n.waveTypeList.find(u=>u.value==F.signalType);return V?V.label:L}}:{}},{align:"center",title:"信号带宽(Hz)",dataIndex:"upperLimitFreqency",isrequired:!0,inputType:"selectinput",selectOptions:[],formItemWidth:I},{align:"center",title:"采样长度(s)",dataIndex:"waveDefParamID",formItemWidth:I,validateRules:se({title:"采样长度",type:"number",required:!0})}],i=[{align:"center",title:"波形定义名称",dataIndex:"waveDefinitionName",isrequired:!0,formItemWidth:I},{title:n&&n.isForm?"测量定义名称":"振动测量位置",dataIndex:n&&n.isForm?"measDefinitionName":"measLocationID",align:"center",disabled:!0,isrequired:!0,formItemWidth:I},{align:"center",title:"包络带宽(Hz)",dataIndex:"envBandWidth",isrequired:!0,inputType:"select",selectOptions:[],formItemWidth:I},{align:"center",title:"包络滤波器(Hz)",dataIndex:"envFiterFreq",isrequired:!0,inputType:"select",selectOptions:[],formItemWidth:I},{align:"center",title:"采样长度(s)",dataIndex:"sampleLength",formItemWidth:I,customRender:({text:L,record:F,index:w,column:C})=>F.waveDefParamID?F.waveDefParamID:"",validateRules:se({title:"采样长度",type:"number",required:!0})}],x=[{align:"center",title:"波形定义名称",dataIndex:"waveDefinitionName",isrequired:!0,formItemWidth:I},{title:n&&n.isForm?"测量定义名称":"振动测量位置",dataIndex:n&&n.isForm?"measDefinitionName":"measLocationID",align:"center",disabled:!0,isrequired:!0,formItemWidth:I},{align:"center",title:"信号类型",dataIndex:"signalType",isrequired:!0,inputType:"select",selectOptions:[],disabled:n&&n.edit,formItemWidth:I,headerOperations:{filters:[],filterOptions:n&&n.waveTypeList&&n.waveTypeList.length>0?n.waveTypeList:[]},...n&&!n.isForm&&n.waveTypeList?{customRender:({text:L,record:F,index:w,column:C})=>{const V=n.waveTypeList.find(u=>u.value==F.signalType);return V?V.label:L}}:{}},{align:"center",title:"信号带宽(Hz)",isrequired:!0,dataIndex:"upperLimitFreqency",inputType:"select",selectOptions:[],formItemWidth:I},{align:"center",title:"采样长度(s)",formItemWidth:I,dataIndex:"waveDefParamID",validateRules:se({title:"采样长度",type:"number",required:!0})}],P=[{align:"center",title:"工况类型",dataIndex:"measLocationID",isrequired:!0,inputType:"select",selectOptions:[],disabled:n&&n.edit,canEdit:!0,hasChangeEvent:!0,labelInValue:!0,customRender:({text:L,record:F,index:w,column:C})=>F.measLocName?F.measLocName:""},{title:"上限频率(Hz)",dataIndex:"parmaChannelNumber",align:"center",isrequired:!0,inputType:"select",selectOptions:[{label:"1",value:"1"},{label:"50",value:"50"},{label:"100",value:"100"},{label:"1K",value:"1000"}],canEdit:!0},{align:"center",title:"采样长度(s)",dataIndex:"param_Type_Name",canEdit:!0,validateRules:se({title:"采样长度",type:"number",required:!0})}],k=[{align:"center",title:"转速测量位置",dataIndex:"measLocationID",isrequired:!0,inputType:"select",selectOptions:[],hasChangeEvent:!0,labelInValue:!0,disabled:n&&n.edit,canEdit:!0,customRender:({text:L,record:F,index:w,column:C})=>F.measLocName?F.measLocName:""},{align:"center",title:"波形线数",dataIndex:"lineCounts",canEdit:!0}],pe=[{align:"left",title:"波形定义名称",dataIndex:"waveDefinitionName",afterContent:!0,isrequired:!0,columnWidth:180,width:240,formItemWidth:I},{title:"Modbus设备",dataIndex:"modbusDeviceID",align:"center",formItemWidth:I,inputType:"select",selectOptions:[],disabled:n&&n.edit,isrequired:!0,hasChangeEvent:!0,...n&&n.isForm?{}:{customRender:({text:L,record:F})=>F.modbusDeviceName||""}},{title:"Modbus测量位置",dataIndex:"measLocationID",labelInValue:!0,align:"center",formItemWidth:I,inputType:"select",selectOptions:[],isrequired:!0,tableList:[],disabled:n&&n.edit,...n&&n.isForm?{}:{customRender:({text:L,record:F})=>F.measLocationName||""}},{align:"center",title:"信号类型",dataIndex:"singleType",isrequired:!0,inputType:"select",labelInValue:!0,selectOptions:[],formItemWidth:I,...n&&n.isForm?{}:{customRender:({text:L,record:F})=>F.singleTypeName||""}},{align:"center",title:"采样频率(Hz)",dataIndex:"sampleRate",isrequired:!0,formItemWidth:I,columnWidth:100},{align:"center",title:"采样长度(s)",dataIndex:"sampleLength",isrequired:!0,formItemWidth:I,columnWidth:100}],J=[{align:"center",title:"触发规则名称",dataIndex:"ruleName",isrequired:!0,formItemWidth:I},{title:"测量定义名称",dataIndex:"measdName",align:"center",formItemWidth:I,isrequired:!0,disabled:!0},{align:"center",title:"触发规则",dataIndex:"triggerRule",formItemWidth:I,hidden:!!(n&&n.isForm)},{align:"center",title:"被触发测量定义",dataIndex:"triggerMeasdedName",formItemWidth:I,hidden:!!(n&&n.isForm)},{align:"center",title:"触发采集类型",dataIndex:"triggerRuleType",formItemWidth:I,inputType:"select",hasChangeEvent:!0,isrequired:!0,headerOperations:{filters:[{text:"工况",value:"工况"},{text:"时间",value:"时间"}]},selectOptions:[{label:"工况",value:"工况"},{label:"时间",value:"时间"}]}];return[ve,re,i,x,P,k,pe,J]},Wt=()=>[{align:"center",title:"下限频率",dataIndex:"underLimitValue",canEdit:!0,columnWidth:230,validateRules:se({title:"下限频率",type:"number",required:!0})},{title:"上限频率",dataIndex:"upperLimitValue",align:"center",canEdit:!0,columnWidth:230,validateRules:se({title:"上限频率",type:"number",required:!0})}],$t=()=>[{align:"center",title:"特征值类型",dataIndex:"type",canEdit:!0,isrequired:!0,inputType:"select",selectOptions:[],columnWidth:200},{title:"逻辑关系",dataIndex:"relationship",align:"center",canEdit:!0,inputType:"select",isrequired:!0,columnWidth:100,selectOptions:[{label:">",value:">"},{label:"<",value:"<"},{label:">=",value:">="},{label:"<=",value:"<="},{label:"=",value:"="},{label:"!=",value:"!="}]},{align:"center",title:"数值",dataIndex:"value",editable:!0,isrequired:!0,canEdit:!0,columnWidth:200}],Re=()=>[{align:"center",title:"选择采集单元",dataIndex:"collectionUnit",inputType:"select",isrequired:!0,selectOptions:[],hasChangeEvent:!0,labelInValue:!0,formItemWidth:I},{align:"center",hasLabelPosition:!0,dataIndex:"measLocIds",inputType:"checkboxGroup",defaultCheckAll:!0,labelInValue:!0,formItemWidth:"600",selectOptions:[],cancheckall:!0,defaultCheckAll:!0,isdisplay:!1},{title:"",hasLabelPosition:!0,dataIndex:"generalEVList",align:"center",inputType:"checkboxGroup",selectOptions:[],formItemWidth:"600",defaultCheckAll:!0},{align:"center",title:"",dataIndex:"isAll",inputType:"checkbox",selectOptions:[{value:"1",label:"将通带特征值配置和频带特征值配置应用到其他波形定义"}]},{title:"特征值配置",dataIndex:"evLists",align:"center",inputType:"select",width:"100%",mode:"multiple",selectOptions:[{value:"41",label:"均值"},{value:"38",label:"最大值"},{value:"39",label:"最小值"},{value:"3",label:"峰值"},{value:"4",label:"峰峰值"},{value:"2",label:"有效值"}],cancheckall:!0},{title:"",dataIndex:"generalEVList",hasLabelPosition:!0,align:"center",inputType:"checkboxGroup",selectOptions:[],formItemWidth:"600",defaultCheckAll:!0},{title:"时间间隔(分钟)",dataIndex:"timeInterval",align:"center",formItemWidth:I,validateRules:se({title:"时间间隔",type:"integer",required:!0})}],St={class:"btnGroups"},Pt={class:"clearfix"},Gt=["onClick"],Bt={key:0,class:"baseInfo"},Kt={class:"border"},zt={class:"tableItems"},jt={class:"tableItems"},Ht={class:"tableItems"},Jt={class:"tableItems"},Yt={class:"tableItems"},Qt={class:"tableItems"},Xt={class:"tableItems"},Zt={key:2,class:"nodata"},ea={key:0},ta={key:0,class:"modalContent"},aa={class:"otherFromContent"},la={class:"getMeasLocName"},ia={key:0,class:"modalPart labelCommonWidth"},sa={class:"modalPart"},na={class:"modalPart"},oa={key:1,class:"otherFromContent"},ua={class:"getMeasLocName"},ra={key:0,class:"checkboxGroup1"},da={key:2,class:"otherFromContent"},ca={key:0},va={key:3,class:"otherFromContent"},pa={class:"modalPart"},ba={class:"modalPart"},ma={key:0,class:"setNameBtn"},Da={__name:"index",setup(n){const ve=wt(),re=ct(),i=Lt(),{measdList:x,waveTypeList:P}=dt(i),k=Tt(),pe=ft(),J=E(!1),L=E(""),F=E(!1),w=E(""),C=E({}),V=E(),u=E(),d=E({}),D=E([]),g=E(Re()),l=E({}),f=E(""),X=E([]),K=E({}),p=E(re.params.id),Y=E(),z=E(!0),s=vt({table1Data:[],table2Data:[],table3Data:[],table4Data:[],table5Data:[],table6Data:[],table7Data:[],modal7TableColumns:$t(),batchApplyData:[],batchApplyKey:"",bathApplyResponse1:{},bathApplyResponse2:{},bathApplyResponse3:{},bathApplyResponse4:{},bathApplyResponse5:{},bathApplyResponse6:{},bathApplyResponse7:{}}),Pe=async e=>{Y.value&&await pe.fetchDevTreedDevicelist({windParkID:Y.value,useTobath:!0})},Ce=async()=>{(!i.measdDaqIntervalUnitType||!i.measdDaqIntervalUnitType.length)&&await i.fetchGetMeasdDaqIntervalUnitType()},Ge=xe(()=>f.value==="batchAdd"?"1200px":w.value==="0"||w.value==="6"?"600px":"700px"),Be=xe(()=>{switch(w.value){case"1":return"振动测量位置";case"3":return"电流电压测量位置";default:return""}}),de=async()=>{if(await i.fetchGetMeasdList({turbineID:p.value}),JSON.stringify(l.value)!=="{}"){let e=x.value.find(t=>t.measDefinitionID==l.value.measDefinitionID);l.value=e}else l.value=x.value[0]},ke=async e=>{if(J.value=!0,await i.fetchGetMeasdList({turbineID:p.value}),J.value=!1,x.value&&x.value.length){if(e&&e=="edit"&&JSON.stringify(l.value)!=="{}"){let t=x.value.find(a=>a.measDefinitionID==l.value.measDefinitionID);l.value=t}else l.value=x.value[0];if(Ie(l.value),e&&e=="edit")return;await Ae(),ge(),ye(),Le(),be(),me(),De(),we()}else l.value={},Ie({})},Ie=e=>{var a;K.value=e;let t="";i.measdDaqIntervalUnitType&&i.measdDaqIntervalUnitType.length&&(t=(a=i.measdDaqIntervalUnitType.find(c=>{var o;return c.value==((o=e.mdf_Ex)==null?void 0:o.daqIntervalUnit)}))==null?void 0:a.label),X.value=[{label:"测量定义名称",value:e.measDefinitionName},{label:"测量定义状态",value:e.isAvailable?"开启":"禁用"},{label:"采集单元",value:e.dauName},{label:"采集模式",value:e.mdf_Ex?e.mdf_Ex.modelType==1?"被动":"主动":""},{label:"采集间隔",value:e.mdf_Ex?`${e.mdf_Ex.daqInterval}${t}`:""}]},Ke=()=>{let e=ve.findAncestorsWithNodes(p.value);e&&e.length&&e.length>1&&(Y.value=e[e.length-2].id)};Ue(()=>re.params.id,async e=>{e&&(i.reset(),k.reset(),l.value={},Ie({}),p.value=e,Ke(),await Ce(),await Pe(),ke())},{immediate:!0});const Z=async(e,t)=>{let a=[...g.value],c=[...D.value];if(e){switch(w.value){case"0":console.log("dauID",e,t),e.dataIndex=="dauID"&&(e.value=="无"?(c[3].validateRules[0].required=!0,c[3].isdisplay=!0):(c[3].validateRules[0].required=!1,c[3].isdisplay=!1));break;case"1":case"2":case"3":let o=[];e.dataIndex=="collectionUnit"&&(e.value!=="无"?(w.value=="3"?(await Je(e.value.value),o=i.unusedVoltageCurrentMeasLocList):w.value=="1"?(await He(e.value.value),o=i.vibMeasLocList):w.value=="2"&&(await Ye(e.value.value),o=i.parameUnusedVibMeasLocList),a[1].selectOptions=o,a[1].isdisplay=!!(o&&o.length)):(a[1].selectOptions=[],a[1].isdisplay=!1));break;case"4":e.dataIndex&&e.dataIndex=="measLocationID"&&(c[3].tableList[e.index].selectOptions=[{label:e.value.label,value:"1"}]);break;case"5":e.dataIndex&&e.dataIndex=="measLocationID"&&(c[2].tableList[e.index].selectOptions=[{label:e.value.label,value:"1"}]);break;case"6":if(e.dataIndex&&e.dataIndex=="modbusDeviceID")if(await k.fetchGetModbusChannelList({turbineID:p.value,modbusDeviceID:e.value}),console.log(e),f.value=="batchAdd"){if(e.index>=c[2].tableList.length)for(let r=c[2].tableList.length;r<=e.index;r++)c[2].tableList.push({});c[2].tableList[e.index].selectOptions=k.modbusLocaOptions,u.value.setTableFieldValue({formDataIndex:`measLocationID[${e.index}]`,tableDataIndex:"measLocationID",index:e.index,value:k.modbusLocaOptions&&k.modbusLocaOptions.length?k.modbusLocaOptions[0]:""}),k.modbusLocaOptions&&k.modbusLocaOptions.length&&u.value.clearValidate(`measLocationID[${e.index}]`)}else c[2].selectOptions=k.modbusLocaOptions;break;case"7":e.dataIndex&&e.dataIndex=="triggerRuleType"&&(z.value=e.value=="工况");return}D.value=[...c],g.value=[...a]}},ze=async()=>{(!i.dauList||!i.dauList.length)&&await i.fetchGetDauList({turbineID:p.value})},je=async()=>{(!i.initUpperLimitFreqList||!i.initUpperLimitFreqList.length)&&await i.fetchGetInitUpperLimitFreqList()},He=async e=>{await i.fetchGetVibMeasLocList({turbineID:p.value,MeasDefinitionID:l.value.measDefinitionID,DAUID:e})},Je=async e=>{await i.fetchGetUnusedVoltageCurrentMeasLocList({turbineID:p.value,MeasDefinitionID:l.value.measDefinitionID,DAUID:e})},Ye=async e=>{await i.fetchGetParameUnusedVibMeasLocList({turbineID:p.value,MeasDefinitionID:l.value.measDefinitionID,DAUID:e})},Qe=async()=>{(!i.eigenValueTypeList||!i.eigenValueTypeList.length)&&await i.fetchGetEigenValueTypeList()},Xe=async()=>await i.fetchGetWorkConOptionList({turbineID:p.value,MeasDefId:l.value.measDefinitionID}),Ze=async()=>await i.fetchGetWorkCondSpdMeasdOptions({WindTurbineID:p.value,MeasDefinitionID:l.value.measDefinitionID}),et=async()=>{(!i.envelopeFilterList||!i.envelopeFilterList.length)&&await i.fetchGetShowEnvelopeFilterList()},tt=async()=>{(!i.upperLimitFreqList||!i.upperLimitFreqList.length)&&await i.fetchGetUpperLimitFreqList()},Ae=async()=>{(!i.waveTypeList||!i.waveTypeList.length)&&await i.fetchGetWaveTypeList()},Fe=()=>{var a;const e=(a=V.value)==null?void 0:a.getFieldsValue();let t="";w.value==1?t=(e.measDefinitionName||"")+(e.upperLimitFreqency||"")+"Hz"+(e.waveDefParamID||"")+" 秒":w.value==2?t="高频包络"+(e.envBandWidth||"")+"Hz"+(e.envFiterFreq||"")+"Hz"+(e.sampleLength||"")+" 秒":w.value==3&&(t=(e.measDefinitionName||"")+(e.upperLimitFreqency||"")+"Hz"+(e.waveDefParamID||"")+" 秒"),V.value.setFieldValue("waveDefinitionName",t),t&&t!==""&&V.value.clearValidate("waveDefinitionName")},at=e=>{var o;const{index:t}=e,a=(o=u.value)==null?void 0:o.getTableFieldsValue();if(!a||!Object.keys(a).length||t==null)return;let c="";c+=a[`measLocationID[${t}]`]&&a[`measLocationID[${t}]`].label?a[`measLocationID[${t}]`].label:"",c+=a[`singleType[${t}]`]&&a[`singleType[${t}]`].label?a[`singleType[${t}]`].label:"",u.value.setTableFieldValue({formDataIndex:`waveDefinitionName[${t}]`,tableDataIndex:"waveDefinitionName",index:t,value:c}),c&&c!==""&&u.value.clearValidate(`waveDefinitionName[${t}]`)},_e=()=>{F.value=!0},ee=e=>{const{title:t,operateType:a,tableKey:c}=e;let o=Q({isForm:!0,validateWaveLineCounts:qe})[c];f.value=a,w.value=c;let r=Re(),b={tableDatas:[]};switch(c){case"0":L.value="添加测量定义",b={isAvailable:"1",daqIntervalUnit:0};break;case"1":L.value="添加时域波形定义",b={measDefinitionName:l.value.measDefinitionName?l.value.measDefinitionName:"",collectionUnit:{label:"无",value:"无"}};break;case"3":L.value="添加电流电压波形定义",b={measDefinitionName:l.value.measDefinitionName?l.value.measDefinitionName:"",collectionUnit:{label:"无",value:"无"}};break;case"2":L.value="添加高频包络波形定义",b={measDefinitionName:l.value.measDefinitionName?l.value.measDefinitionName:"",collectionUnit:{label:"无",value:"无"}};break;case"4":case"5":o.push({title:"工况监视特征值",dataIndex:"evAvg",inputType:"checkbox",selectOptions:[],tableList:[{selectOptions:[]}]}),c=="4"?L.value="添加工况波形定义":L.value="添加转速波形定义";break;case"6":L.value="添加Modbus设备波形定义",o.push({...r[4],columnWidth:200,width:""});break;case"7":L.value="添加触发采集配置",b={measdName:l.value.measDefinitionName,triggerRuleType:"工况"};break}C.value={...b},D.value=o,g.value=[...r],Ve(c),_e()},lt=async e=>{switch(w.value){case"0":let t={...e,measDefinitionID:f.value=="edit"?C.value.measDefinitionID:"",windTurbineID:p.value,windParkID:Y.value,isAvailable:e.isAvailable&&e.isAvailable=="1",dauid:e.DauID&&e.DauID=="无"?"":e.DauID,modbusDeviceIDs:e.modbusDeviceIDs&&e.modbusDeviceIDs.length?e.modbusDeviceIDs.join(","):""},a={};f.value=="edit"?a=await i.fetchEditMeasDefinition(t):a=await i.fetchAddMeasDefinition(t),a&&a.code===1?(ke(f.value),W(),h.success("提交成功")):h.error("提交失败:"+a.msg);break;case"1":case"3":let c=fe(e),o="";c&&c.length&&c.forEach((ce,Ee)=>{Ee>0&&(o+=","),o+="FBE#"+ce.underLimitValue+"#"+ce.upperLimitValue});let r={...e,waveDefinitionID:f.value=="edit"?C.value.waveDefinitionID:"",measDefinitionID:l.value.measDefinitionID,windTurbineID:p.value,windParkID:Y.value,measLocIds:e.measLocIds&&e.measLocIds.length?e.measLocIds.join(","):"",messLocNames:"",dauid:e.collectionUnit&&e.collectionUnit.value?e.collectionUnit.value:"",isAll:!!(e.isAll&&e.isAll=="1"),FBEList:o,generalEVList:e.generalEVList&&e.generalEVList.length?e.generalEVList.join(","):"",timeWdfSampleLength:e.waveDefParamID,timeWdfUpFreq:e.upperLimitFreqency,isAddType:f.value=="edit"?"0":"1"},b={};w.value=="3"?b=await i.fetchMakeWaveDefinitionVoltageCurrent({sourceData:r,targetTurbineIds:s.batchApplyData}):b=await i.fetchMakeWaveDefinition({sourceData:r,targetTurbineIds:s.batchApplyData}),b&&b.code===1?(w.value=="3"?(Le(),s.bathApplyResponse3=b.batchResults||{}):(ge(),s.bathApplyResponse1=b.batchResults||{}),h.success("提交成功"),W()):h.error("提交失败:"+b.msg);break;case"2":let v={...e,waveDefinitionID:f.value=="edit"?C.value.waveDefinitionID:"",measDefinitionID:l.value.measDefinitionID,windTurbineID:p.value,windParkID:Y.value,measLocIds:e.measLocIds&&e.measLocIds.length?e.measLocIds.join(","):"",messLocNames:"",dauid:e.collectionUnit&&e.collectionUnit.value?e.collectionUnit.value:"",isAll:!!(e.isAll&&e.isAll=="1"),timeWdfSampleLength:e.waveDefParamID,isAddType:f.value=="edit"?"0":"1",FBEList:"",GeneralEVList:""};const m=await i.fetchMakeParamEnvDefinition({sourceData:v,targetTurbineIds:s.batchApplyData});m&&m.code===1?(ye(),s.bathApplyResponse2=m.batchResults||{},W(),h.success("提交成功")):h.error("提交失败:"+m.msg);break;case"4":let y={dauID:l.value.dauID,windTurbineID:p.value,measDefinitionID:l.value.measDefinitionID,evAvg:!!(e.evAvg&&e.evAvg.length),measLocId:e.measLocationID&&e.measLocationID.length>0?e.measLocationID[0].value:"",upperLimitFreqency:e.parmaChannelNumber,sampleLength:e.param_Type_Name},N=await i.fetchBatchEditWorkCondMeasd({sourceData:[y],targetTurbineIds:s.batchApplyData});N&&N.code===1?(be(),s.bathApplyResponse4=N.batchResults||{},h.success("提交成功"),W(),de()):h.error("提交失败:"+N.msg);break;case"5":let le={...e,upperLimitFreqency:e.lineCounts?e.lineCounts*1:0,dauID:l.value.dauID,windTurbineID:p.value,measDefinitionID:l.value.measDefinitionID,evAvg:!!(e.evAvg&&e.evAvg.length),measLocId:C.value.measLocId},S=await i.fetchBatchEditWorkCondSpdMeasd({sourceData:[le],targetTurbineIds:s.batchApplyData});S&&S.code===1?(me(),s.bathApplyResponse5=S.batchResults||{},h.success("提交成功"),W(),de()):h.error("提交失败:"+S.msg);break;case"6":let O={...e,windTurbineID:p.value,measDefinitionID:l.value.measDefinitionID,waveDefinitionID:C.value.waveDefinitionID||"",evLists:e.evLists?e.evLists:[],measLocationID:e.measLocationID&&e.measLocationID.value?e.measLocationID.value:"",singleType:e.singleType&&e.singleType.value?e.singleType.value:""},M={};f.value=="add"?M=await i.fetchMeasdAddModbusWave({sourceData:[O],targetTurbineIds:s.batchApplyData}):M=await i.fetchEditModbusWave({sourceData:O,targetTurbineIds:s.batchApplyData}),M&&M.code===1?(De(),s.bathApplyResponse6=M.batchResults||{},h.success("提交成功"),W()):h.error("提交失败:"+M.msg);break;case"7":if(console.log(e.generalEVList,l.value),l.value.mdf_Ex&&l.value.mdf_Ex.modelType==0&&(!e.generalEVList||!e.generalEVList.length)){h.warning("请选择被触发测量定义!");return}let j=fe(e),ne=[];j&&j.length&&j.forEach((ce,Ee)=>{ne.push(ce.type+","+ce.relationship+","+ce.value)});let G={isAddType:f.value=="edit"?"0":"1",turbineID:p.value,triggerRuleName:e.ruleName,triggerMeasDefName:l.value.measDefinitionName,conditionMonitoringLocIds:e.generalEVList&&e.generalEVList.length?e.generalEVList.join("#"):"",triggerData:ne,dauid:l.value.dauID||"",triggertime:e.timeInterval||""},H=await i.fetchAddMeasdTirggerAcq({sourceData:G,targetTurbineIds:s.batchApplyData});H&&H.code===1?(we(),s.bathApplyResponse7=H.batchResults||{},W(),h.success("提交成功")):h.error("提交失败:"+H.msg);break}},it=async e=>{switch(w.value){case"4":let t=fe(e);if(t&&t.length){let r=t.map((v,m)=>({dauID:l.value.dauID,windTurbineID:p.value,measDefinitionID:l.value.measDefinitionID,evAvg:!!(v.evAvg&&v.evAvg.length),measLocId:v.measLocationID,upperLimitFreqency:v.parmaChannelNumber,sampleLength:v.param_Type_Name})),b=await i.fetchBatchAddWorkCondMeasd({sourceData:r,targetTurbineIds:s.batchApplyData});b&&b.code===1?(be(),s.bathApplyResponse4=b.batchResults||{},h.success("提交成功"),W(),de()):h.error("提交失败:"+b.msg)}break;case"5":let a=fe(e);if(a&&a.length){let r=a.map((v,m)=>({dauID:l.value.dauID,windTurbineID:p.value,measDefinitionID:l.value.measDefinitionID,evAvg:!!(v.evAvg&&v.evAvg.length),measLocId:v.measLocationID,upperLimitFreqency:v.lineCounts,sampleLength:v.gearRatio})),b=await i.fetchBatchAddWorkCondSpdMeasd({sourceData:r,targetTurbineIds:s.batchApplyData});b&&b.code===1?(me(),s.bathApplyResponse5=b.batchResults||{},h.success("提交成功"),W(),de()):h.error("提交失败:"+b.msg)}break;case"6":console.log(e);let c={windTurbineID:p.value,measDefinitionID:l.value.measDefinitionID,waveDefinitionID:""},o=fe(e,c);if(o&&o.length){o.forEach((b,v)=>{b.evLists=b.evLists?b.evLists:[]});let r=await i.fetchMeasdAddModbusWave({sourceData:o,targetTurbineIds:s.batchApplyData});r&&r.code===1?(De(),s.bathApplyResponse6=r.batchResults||{},h.success("提交成功"),W()):h.error("提交失败:"+r.msg)}break}},te=e=>{const{tableKey:t,rowData:a}=e;w.value=t,f.value="edit";let c=Re();g.value=c;let o=Q({isForm:!0,edit:!0,validateWaveLineCounts:qe})[t],r={measDefinitionName:l.value.measDefinitionName?l.value.measDefinitionName:""};switch(t){case"0":L.value="编辑测量定义",a.dauID&&(o[3].isdisplay=!1,o[3].validateRules[0].required=!1);let b=[];a.modbusUnits&&a.modbusUnits.length&&a.modbusUnits.map(O=>{b.push(O.modbusDeviceID)}),r={...r,...a,...a.mdf_Ex,isAvailable:a.isAvailable?"1":"0",dauID:a.dauID?a.dauID:"无",modbusDeviceIDs:b};break;case"1":case"3":let v=[],m=[],y={},N=0;a.vibEigenValueConf&&a.vibEigenValueConf.length&&a.vibEigenValueConf.forEach((O,M)=>{O.evType==0?v.push(O.type):O.evType==1&&(m.push({key:M,underLimitValue:O.underLimitValue,upperLimitValue:O.upperLimitValue}),y[`underLimitValue[${N}]`]=O.underLimitValue,y[`upperLimitValue[${N}]`]=O.upperLimitValue,N++)}),r={...r,...a,generalEVList:v,tableDatas:m,...y},t=="1"?L.value="编辑时域波形定义":L.value="编辑电流电压波形定义";break;case"2":L.value="编辑高频包络波形定义",r={...r,...a,sampleLength:a.waveDefParamID};break;case"4":case"5":o.push({title:"工况监视特征值",dataIndex:"evAvg",inputType:"checkbox",selectOptions:[{label:a.measLocName,value:"1"}]}),r={...r,...a,measLocId:a.measLocationID,evAvg:a.evAvg?["1"]:[],measLocationID:[{label:a.measLocName,value:a.measLocationID}],upperLimitFreqency:a.parmaChannelNumber},t=="4"?L.value="编辑工况波形定义":L.value="编辑转速波形定义";break;case"6":L.value="编辑Modbus设备波形定义",r={...r,...a,evLists:a.eigenValues},D.value=[...o],a.modbusDeviceID&&a.modbusDeviceID!==""&&Z({dataIndex:"modbusDeviceID",value:a.modbusDeviceID});break;case"7":L.value="编辑触发采集配置";let le=[],S="";a.triggerRuleType&&(z.value=a.triggerRuleType=="工况",a.triggerRuleList&&a.triggerRuleList.length&&(a.triggerRuleType=="工况"?a.triggerRuleList.forEach((O,M)=>{let j=O.split(",");le.push({key:M,type:j[0],relationship:j[1],value:j[2]})}):S=a.triggerRuleList[0])),r={...r,...a,generalEVList:a.triggerMeasdId,timeInterval:S,tableDatas:le};break}t!=="6"&&(D.value=[...o]),Ve(t),C.value={...r},_e()},ae=async e=>{const{tableKey:t,selectedkeys:a,rowData:c,record:o}=e;if((!a||!a.length)&&t!=="0"){h.error("请选择要删除的行");return}switch(t){case"0":const r=await i.fetchDeleteMeasDef({windTurbineID:p.value,measDefId:c.measDefinitionID});r&&r.code===1?(ke(),h.success("删除成功")):h.error("删除失败:"+r.msg);break;case"1":const b=await i.fetchDeleteWaveChannel({sourceData:{windTurbineID:p.value,measDefId:l.value.measDefinitionID,waveId:a.join(",")},targetTurbineIds:s.batchApplyData});b&&b.code===1?(ge(),s.bathApplyResponse1=b.batchResults||{},h.success("删除成功")):h.error("删除失败:"+b.msg);break;case"2":const v=await i.fetchDeleteParamEnvChannel({sourceData:{windTurbineID:p.value,measDefId:l.value.measDefinitionID,waveId:a.join(",")},targetTurbineIds:s.batchApplyData});v&&v.code===1?(ye(),s.bathApplyResponse2=v.batchResults||{},h.success("删除成功")):h.error("删除失败:"+v.msg);break;case"3":const m=await i.fetchDeleteWaveChannelVoltageCurrenl({sourceData:{windTurbineID:p.value,measDefId:l.value.measDefinitionID,waveId:a.join(",")},targetTurbineIds:s.batchApplyData});m&&m.code===1?(Le(),s.bathApplyResponse3=m.batchResults||{},h.success("删除成功")):h.error("删除失败:"+m.msg);break;case"4":let y=[];a.forEach((G,H)=>{y.push({windTurbineID:p.value,dauID:l.value.dauID,measDefinitionID:l.value.measDefinitionID,measLocId:G})});let N=await i.fetchBatchDeleteWorkCondMeasd({sourceData:y,targetTurbineIds:s.batchApplyData});N&&N.code===1?(be(),s.bathApplyResponse4=N.batchResults||{},h.success("删除成功"),de()):h.error("删除失败:"+N.msg);break;case"5":let le=[];a.forEach((G,H)=>{le.push({windTurbineID:p.value,dauID:l.value.dauID,measDefinitionID:l.value.measDefinitionID,measLocId:G})});let S=await i.fetchBatchDeleteWorkCondSpdMeasd({sourceData:le,targetTurbineIds:s.batchApplyData});S&&S.code===1?(me(),s.bathApplyResponse5=S.batchResults||{},h.success("删除成功"),de()):h.error("删除失败:"+S.msg);break;case"6":let O=[];if(o)O.push({windTurbineID:p.value,measDefinitionID:o.measDefinitionID,waveDefinitionID:o.waveDefinitionID,measLocationID:o.measLocationID});else for(let G=0;G<a.length;G++){let H=a[G].split("&&");O.push({windTurbineID:p.value,measDefinitionID:l.value.measDefinitionID,waveDefinitionID:H[0],measLocationID:H[1]})}let M=await i.fetchBatchDeleteModbusWave({sourceData:O,targetTurbineIds:s.batchApplyData});M&&M.code===1?(De(),s.bathApplyResponse6=M.batchResults||{},h.success("删除成功")):h.error("删除失败:"+M.msg);break;case"7":let j=[];a.forEach((G,H)=>{j.push({turbineID:p.value,dauID:l.value.dauID,triggerRuleName:G,triggerMeasDefName:l.value.measDefinitionName})});let ne=await i.fetchDeleteTriggerGatherDispose({sourceData:j,targetTurbineIds:s.batchApplyData});ne&&ne.code===1?(we(),s.bathApplyResponse7=ne.batchResults||{},h.success("删除成功")):h.error("删除失败:"+ne.msg);break}},st=async()=>{(!k.modbusDeviceOptions||!k.modbusDeviceOptions.length)&&await k.fetchGetModbusDeviceList({turbineID:p.value})},Ve=async e=>{const t=D.value;if(!t||!t.length)return;const a=g.value;await ze();const c=[{label:"无",value:"无"},...i.dauList];switch(e){case"0":await st(),await Ce(),t[5].selectOptions=[...k.modbusDeviceOptions],t[2].selectOptions=c,t[6].selectOptions=[...i.measdDaqIntervalUnitType],D.value=[...t];break;case"1":case"3":await Ae(),await je(),await Qe(),t[2].selectOptions=i.waveTypeList,t[3].selectOptions=i.initUpperLimitFreqList,a[0].selectOptions=c,a[2].selectOptions=i.eigenValueTypeList,D.value=[...t],g.value=[...a];break;case"2":await et(),await tt(),t[2].selectOptions=i.upperLimitFreqList,t[3].selectOptions=i.envelopeFilterList,D.value=[...t],a[0].selectOptions=c,g.value=[...a];break;case"4":case"5":if(f.value=="batchAdd"){let o=[];e=="4"?o=await Xe():o=await Ze(),t[0].selectOptions=o,D.value=[...t]}break;case"6":if(l.value.modbusUnits&&l.value.modbusUnits.length){let o=yt(l.value.modbusUnits,{label:"modbusDeviceName",value:"modbusDeviceID"});t[1].selectOptions=o}else t[1].selectOptions=[];await Ae(),t[3].selectOptions=i.waveTypeList;break;case"7":if(l.value.superviseEvName&&l.value.superviseEvName!==""){let o=l.value.superviseEvName.split(","),r=[];o.forEach((b,v)=>{r.push({label:b,value:b})}),s.modal7TableColumns[0].selectOptions=r}else s.modal7TableColumns[0].selectOptions=[];if(l.value.mdf_Ex&&l.value.mdf_Ex.modelType==0){let o=[];i.measdList.forEach((r,b)=>{r.mdf_Ex&&r.mdf_Ex.modelType==1&&o.push({label:r.measDefinitionName,value:r.measDefinitionID})}),a[5].selectOptions=o}}},qe=async(e,t)=>{var o,r;const a=f.value=="batchAdd"?(o=u.value)==null?void 0:o.getTableFieldsValue():(r=V.value)==null?void 0:r.getFieldsValue();if(!a)return;let c=!0;if(f.value=="batchAdd"){let v=e.field.match(/\d+/)[0];c=a[`gearRatio[${v}]`]&&a[`gearRatio[${v}]`]%t==0}else c=a.gearRatio&&a.gearRatio%t==0;return c?Promise.resolve(c):Promise.reject(new Error("编码器线数必须是波形线数的整数倍！"))},W=()=>{F.value=!1,w.value=="7"&&(z.value=!0),C.value={},w.value="",L.value="",f.value="",D.value=[],g.value=[],d.value={}},nt=e=>{if(e.measDefinitionID!=l.value.measDefinitionID){if(J.value=!0,l.value=e,Ie(e),!x.value||!x.value.length){h.warning("无法点击！");return}ge(),ye(),Le(),be(),me(),De(),we(),J.value=!1}},ge=async()=>{s.table1Data=await i.fetchGetTimeWaveDefList({turbineID:p.value,MeasDefinitionID:l.value.measDefinitionID})},ye=async()=>{s.table2Data=await i.fetchGetTimeParamEnvDefList({turbineID:p.value,MeasDefinitionID:l.value.measDefinitionID})},Le=async()=>{s.table3Data=await i.fetchGetVoltageCurrentWaveDefList({turbineID:p.value,MeasDefinitionID:l.value.measDefinitionID})},be=async()=>{s.table4Data=await i.fetchGetWorkConListForMeasLocList({turbineID:p.value,MeasDefinitionID:l.value.measDefinitionID})},me=async()=>{s.table5Data=await i.fetchGetWorkCondSpdMeasdList({turbineID:p.value,MeasDefinitionID:l.value.measDefinitionID})},De=async()=>{s.table6Data=await i.fetchGetModbusWaveList({turbineID:p.value,measDefinitionID:l.value.measDefinitionID})},we=async()=>{s.table7Data=await i.fetchGetMeasdTriggerList({turbineID:p.value,MeasDefinitionID:l.value.measDefinitionID})},ot=async e=>{e.type&&e.type=="close"?(s.batchApplyData=[],s.batchApplyKey="",s[`bathApplyResponse${e.key}`]={}):(s.batchApplyData=e.turbines,s.batchApplyKey=e.key)};return Ne("deviceId",p),Ne("bathApplySubmit",ot),(e,t)=>{const a=Se,c=Rt,o=At,r=xt,b=kt;return T(),ue(b,{spinning:J.value,size:"large"},{default:q(()=>[R("h1",null,[t[18]||(t[18]=U(" 测量定义 ",-1)),A(a,{class:"addbtnOfPageTitle",type:"primary",onClick:t[0]||(t[0]=v=>ee({tableKey:"0",operateType:"add",title:"测量定义"}))},{default:q(()=>t[17]||(t[17]=[U("添加",-1)])),_:1,__:[17]})]),R("div",St,[R("ul",Pt,[(T(!0),_(Te,null,Oe(B(x),v=>(T(),_("li",{key:v.measDefinitionID,class:We({active:l.value.measDefinitionID===v.measDefinitionID}),onClick:m=>nt(v)},he(v.measDefinitionName),11,Gt))),128))])]),l.value&&l.value.measDefinitionID?(T(),_("div",Bt,[A(ht,{tableTitle:"测量定义信息",defaultCollapse:!0,batchApply:!1},pt({content:q(()=>[R("div",Kt,[A(o,{column:5,size:"small"},{default:q(()=>[(T(!0),_(Te,null,Oe(X.value,v=>(T(),ue(c,{label:v.label,key:v.label},{default:q(()=>[U(he(v.value),1)]),_:2},1032,["label"]))),128))]),_:1})])]),_:2},[l.value&&l.value.measDefinitionID?{name:"rightButtons",fn:q(()=>[A(a,{type:"primary",onClick:t[1]||(t[1]=v=>te({tableKey:"0",rowData:K.value,title:"测量定义"}))},{default:q(()=>t[19]||(t[19]=[U("编辑",-1)])),_:1,__:[19]}),A(a,{onClick:t[2]||(t[2]=v=>ae({tableKey:"0",rowData:K.value,title:"测量定义"}))},{default:q(()=>t[20]||(t[20]=[U("删除",-1)])),_:1,__:[20]})]),key:"0"}:void 0]),1024)])):$("",!0),l.value&&l.value.measDefinitionID?(T(),_("div",{class:"blockBorder",key:p.value},[R("div",zt,[A(oe,{"table-key":"1","table-title":"时域波形定义列表","table-columns":B(Q)({waveTypeList:B(P)})[1],borderLight:s.batchApplyKey=="1",bathApplyResponse:s.bathApplyResponse1,"table-operate":["edit","delete","add","batchDelete"],"record-key":"waveDefinitionID",onAddRow:ee,"table-datas":s.table1Data,onDeleteRow:ae,onEditRow:te,noPagination:!0},null,8,["table-columns","borderLight","bathApplyResponse","table-datas"])]),R("div",jt,[A(oe,{"table-key":"2","table-title":"高频包络波形定义列表","table-columns":B(Q)()[2],borderLight:s.batchApplyKey=="2",bathApplyResponse:s.bathApplyResponse2,"table-operate":["edit","delete","add","batchDelete"],"record-key":"waveDefinitionID","table-datas":s.table2Data,onAddRow:ee,onDeleteRow:ae,onEditRow:te,noPagination:!0},null,8,["table-columns","borderLight","bathApplyResponse","table-datas"])]),R("div",Ht,[A(oe,{"table-key":"3","table-title":"电流电压波形定义列表","table-columns":B(Q)({waveTypeList:B(P)})[3],borderLight:s.batchApplyKey=="3",bathApplyResponse:s.bathApplyResponse3,"table-operate":["edit","delete","add","batchDelete"],"record-key":"waveDefinitionID","table-datas":s.table3Data,onAddRow:ee,onDeleteRow:ae,onEditRow:te,noPagination:!0},null,8,["table-columns","borderLight","bathApplyResponse","table-datas"])]),R("div",Jt,[A(oe,{"table-key":"4","table-title":"工况波形定义列表","table-columns":B(Q)()[4],borderLight:s.batchApplyKey=="4",bathApplyResponse:s.bathApplyResponse4,"table-operate":["edit","delete","add","batchDelete","batchAdd"],"record-key":"measLocationID","table-datas":s.table4Data,onAddRow:ee,onDeleteRow:ae,onEditRow:te,noPagination:!0},null,8,["table-columns","borderLight","bathApplyResponse","table-datas"])]),R("div",Yt,[A(oe,{"table-key":"5","table-title":"转速波形定义列表","table-columns":B(Q)()[5],borderLight:s.batchApplyKey=="5",bathApplyResponse:s.bathApplyResponse5,"table-operate":["edit","delete","add","batchDelete","batchAdd"],"record-key":"measLocationID","table-datas":s.table5Data,onAddRow:ee,onDeleteRow:ae,onEditRow:te,noPagination:!0},null,8,["table-columns","borderLight","bathApplyResponse","table-datas"])]),R("div",Qt,[A(oe,{"table-key":"6","table-title":"Modbus设备波形定义列表","table-columns":B(Q)()[6],borderLight:s.batchApplyKey=="6",bathApplyResponse:s.bathApplyResponse6,"table-operate":["edit","delete","add","batchDelete","batchAdd"],recordKey:v=>`${v.waveDefinitionID}&&${v.measLocationID}`,"table-datas":s.table6Data,onAddRow:ee,onDeleteRow:ae,onEditRow:te,noPagination:!0},null,8,["table-columns","borderLight","bathApplyResponse","recordKey","table-datas"])]),R("div",Xt,[A(oe,{"table-key":"7","table-title":"触发采集配置列表","table-columns":B(Q)()[7],borderLight:s.batchApplyKey=="7",bathApplyResponse:s.bathApplyResponse7,"table-operate":["edit","delete","add","batchDelete"],"record-key":"ruleName","table-datas":s.table7Data,onAddRow:ee,onDeleteRow:ae,onEditRow:te,noPagination:!0},null,8,["table-columns","borderLight","bathApplyResponse","table-datas"])])])):(T(),_("div",Zt," 请先添加测量定义!")),A(r,{maskClosable:!1,destroyOnClose:!0,width:Ge.value,open:F.value,title:L.value,footer:"",onCancel:W},{default:q(()=>[f.value==="add"||f.value==="edit"?(T(),_("div",ea,[A(ie,{ref_key:"formModalRef",ref:V,model:d.value,"title-col":D.value,initFormData:C.value,actions:["submit","change"],onChange:Z,onSubmit:lt,onCancelForm:W},{otherInfo:q(({formModel:v})=>[w.value=="1"||w.value=="3"?(T(),_("div",ta,[R("div",aa,[R("div",la,[A(a,{onClick:t[3]||(t[3]=m=>Fe()),class:"autoName"},{default:q(()=>t[21]||(t[21]=[U("自动生成",-1)])),_:1,__:[21]})]),f.value==="add"?(T(),_("div",ia,[R("p",null,he(Be.value),1),A(ie,{modelValue:d.value,"onUpdate:modelValue":[t[4]||(t[4]=m=>d.value=m),t[5]||(t[5]=m=>{var y;return(y=V.value)==null?void 0:y.updateSlotFormData(m,["collectionUnit","measLocIds"])})],onlyFormList:!0,onChange:Z,"title-col":[g.value[0],g.value[1]],initFormData:C.value},null,8,["modelValue","title-col","initFormData"])])):$("",!0),R("div",sa,[t[22]||(t[22]=R("p",null,"通带特征值配置",-1)),A(ie,{modelValue:d.value,"onUpdate:modelValue":[t[6]||(t[6]=m=>d.value=m),t[7]||(t[7]=m=>{var y;return(y=V.value)==null?void 0:y.updateSlotFormData(m,["generalEVList"])})],onlyFormList:!0,"title-col":[{...g.value[2]}],initFormData:C.value},null,8,["modelValue","title-col","initFormData"])]),R("div",na,[t[23]||(t[23]=R("p",null,"频带特征值配置",-1)),A(Me,{modelValue:d.value,"onUpdate:modelValue":[t[8]||(t[8]=m=>d.value=m),t[9]||(t[9]=m=>{var y;return(y=V.value)==null?void 0:y.updateSlotFormData(m,["underLimitValue","upperLimitValue"])})],size:"default","table-columns":B(Wt)(),"table-operate":["delete"],"table-datas":C.value.tableDatas,noForm:!0,noCopyUp:!0,"noCopyUp-keys":["underLimitValue","upperLimitValue"],defaultEmptyTable:!0,onHangeTableFormChange:Z},null,8,["modelValue","table-columns","table-datas"]),A(ie,{onlyFormList:!0,titleCol:[{...g.value[3]}],initFormData:C.value},null,8,["titleCol","initFormData"])])])])):w.value=="2"?(T(),_("div",oa,[R("div",ua,[A(a,{onClick:t[10]||(t[10]=m=>Fe()),class:"autoName"},{default:q(()=>t[24]||(t[24]=[U("自动生成",-1)])),_:1,__:[24]})]),f.value=="add"?(T(),_("div",ra,[t[25]||(t[25]=R("p",null,"振动测量位置",-1)),A(ie,{onlyFormList:!0,onChange:Z,"title-col":[g.value[0],g.value[1]],initFormData:C.value,"onUpdate:modelValue":t[11]||(t[11]=m=>{var y;return(y=V.value)==null?void 0:y.updateSlotFormData(m,["measLocIds","collectionUnit"])})},null,8,["title-col","initFormData"])])):$("",!0)])):w.value=="6"?(T(),_("div",da,[f.value=="add"||f.value=="edit"?(T(),_("div",ca,[A(ie,{onlyFormList:!0,onChange:Z,"title-col":[g.value[4]],"onUpdate:modelValue":t[12]||(t[12]=m=>{var y;return(y=V.value)==null?void 0:y.updateSlotFormData(m,["evLists"])}),initFormData:C.value},null,8,["title-col","initFormData"])])):$("",!0)])):w.value=="7"?(T(),_("div",va,[R("div",pa,[t[26]||(t[26]=R("p",null,"触发规则",-1)),z.value?(T(),ue(Me,{key:0,modelValue:d.value,"onUpdate:modelValue":[t[13]||(t[13]=m=>d.value=m),t[14]||(t[14]=m=>{var y;return(y=V.value)==null?void 0:y.updateSlotFormData(m,["relationship","type","value"])})],size:"default","table-columns":s.modal7TableColumns,"table-operate":["delete"],"table-datas":C.value.tableDatas,noForm:!0,noCopyUp:!0,"noCopyUp-keys":["type","relationship","value"]},null,8,["modelValue","table-columns","table-datas"])):(T(),ue(ie,{key:1,onlyFormList:!0,"title-col":[g.value[6]],"onUpdate:modelValue":t[15]||(t[15]=m=>{var y;return(y=V.value)==null?void 0:y.updateSlotFormData(m,["timeInterval"])}),initFormData:C.value},null,8,["title-col","initFormData"]))]),bt(R("div",ba,[t[27]||(t[27]=R("p",null,"被触发测量定义",-1)),A(ie,{onlyFormList:!0,onChange:Z,"title-col":[g.value[5]],initFormData:C.value,"onUpdate:modelValue":t[16]||(t[16]=m=>{var y;return(y=V.value)==null?void 0:y.updateSlotFormData(m,["generalEVList"])})},null,8,["title-col","initFormData"])],512),[[mt,!!(l.value&&l.value.mdf_Ex&&l.value.mdf_Ex.modelType==0)]])])):$("",!0)]),_:1},8,["model","title-col","initFormData"])])):f.value==="batchAdd"?(T(),ue(gt,{key:1,ref_key:"tableModalRef",ref:u,size:"default","table-columns":D.value,"table-operate":["copyUp","delete"],"table-datas":[],onHangeTableFormChange:Z,"noCopyUp-keys":["parkNumber"],onSubmit:it,onCancel:W},{afterContent:q(({column:v,record:m,text:y,index:N})=>[v.dataIndex==="waveDefinitionName"?(T(),_("div",ma,[A(a,{onClick:le=>at({record:m,index:N}),class:"autoName"},{default:q(()=>t[28]||(t[28]=[U("自动",-1)])),_:2,__:[28]},1032,["onClick"])])):$("",!0)]),_:1},8,["table-columns"])):$("",!0)]),_:1},8,["width","open","title"])]),_:1},8,["spinning"])}}},Na=$e(Da,[["__scopeId","data-v-000aa76d"]]);export{Na as default};
