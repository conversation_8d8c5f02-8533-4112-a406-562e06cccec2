import{u as K,W as ee}from"./table-DF7YFrUK.js";import{O as te}from"./index-5GdjZhcp.js";import{r as i,u as ae,j as B,h as ne,w as re,x as oe,f as q,d as c,o as y,i as A,b as f,c as U,q as le,F as se,g as I,p as V,t as z,m as C}from"./index-sMW2Pm6g.js";import{u as ue}from"./jfDeviceManage-CqSCSr_n.js";import{g as ie}from"./tools-DC78Tda0.js";import{s as h,_ as de,p as ce,o as pe}from"./useWebSocket-nvkDFdXf.js";import{_ as fe}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{S as ve}from"./ActionButton-DMeJvyLo.js";import{B as me}from"./index-Bi-LLAnN.js";import{M as ge}from"./index-sJyM5xm7.js";import"./styleChecker-LI4Lr2UF.js";import"./index-DRy8se-f.js";import"./shallowequal-D09g54zQ.js";import"./index-CkAETRMb.js";import"./index-BLrmE7-A.js";import"./index-ljzR8VKX.js";const v=320,be={__name:"park",setup(Ie){const p=ue(),N=K(),S=i(!1),x=ae(),D=i(""),b=i(""),M=i({}),s=i([]);i([]);const _=i([]),T=i(x.params.id),d=i(!1),w=i([]),P=i([]),k=i(!1),J=B(()=>b.value==="useMeasurementDefinition"?"1200px":"600px"),j=B(()=>b.value==="otherConfig"?"part":"horizontal"),m=(t,a)=>[{title:"机组名称",dataIndex:"windTurbineName",labelInValue:!a,columnWidth:100,inputType:"select",selectOptions:N.deviceOptions,disabled:a,isrequired:!0,formItemWidth:v},{title:"采集器类型",dataIndex:"dauType",hasChangeEvent:!0,columnWidth:140,inputType:"select",isrequired:!0,selectOptions:p.dAUTypeList,isdisplay:!a,formItemWidth:v,headerOperations:{filters:[]}},{title:"DAU IP地址",dataIndex:"ip",columnWidth:150,formItemWidth:v,columnOperate:{type:"ip"},validateRules:ie({type:"ip",title:"IP地址",required:!0})},{title:"服务器地址",dataIndex:"port",columnWidth:70,formItemWidth:v},{title:"在离线状态",dataIndex:"dauOnOffStatus",otherColumn:!0,columnWidth:120,formItemWidth:v,headerOperations:{filters:[{text:"在线",value:2},{text:"离线",value:1}]}}],g=i(m()),W=async()=>{d.value=!0;let t=await p.fetchGetDAUList({WindParkId:T.value});s.value=t,d.value=!1,g.value=m()};ne(()=>{W()}),re(()=>x.params.id,t=>{p.reset(),T.value=t,W()});const E=async(t,a)=>{d.value=!0;let r=s.value.filter(e=>t.includes(`${e.windTurbineID}&&${e.dauID}`)),n=await p.fetchStartAcquisition(r);d.value=!1,n&&n.code===1?(g.value=[...m(),{title:"采集状态",dataIndex:"operateStatus",otherColumn:!0,columnWidth:120,formItemWidth:v}],s.value=s.value.map(e=>t.includes(`${e.windTurbineID}&&${e.dauID}`)?{...e,operateStatus:n.data[e.ip]}:e)):C.error("操作失败")},G=async(t,a)=>{d.value=!0;let r=s.value.filter(e=>t.includes(`${e.windTurbineID}&&${e.dauID}`)),n=await p.fetchStopAcquisition(r);d.value=!1,n&&n.code===1?(g.value=[...m(),{title:"停止采集",dataIndex:"operateStatus",otherColumn:!0,columnWidth:120,formItemWidth:v}],s.value=s.value.map(e=>t.includes(`${e.windTurbineID}&&${e.dauID}`)?{...e,operateStatus:n.data[e.ip]}:e)):C.error("操作失败")},H=async(t,a)=>{d.value=!0;let r=s.value.filter(e=>t.includes(`${e.windTurbineID}&&${e.dauID}`)),n=await p.fetchSetMeasureDefinition(r);d.value=!1,n&&n.code===1?(g.value=[...m(),{title:"测量定义下发",dataIndex:"operateStatus",otherColumn:!0,columnWidth:120,formItemWidth:v}],s.value=s.value.map(e=>t.includes(`${e.windTurbineID}&&${e.dauID}`)?{...e,operateStatus:n.data[e.ip]}:e)):C.error("操作失败")},R=async(t,a)=>{b.value=a,D.value="推送配置",w.value=t,g.value=[...m()],_.value=ce(),O()},Q=async(t,a)=>{b.value=a,D.value="高级参数配置",w.value=t,_.value=pe(),g.value=[...m()],O()},O=()=>{S.value=!0},F=t=>{S.value=!1,_.value=[],M.value={},b.value="",D.value=""},X=async t=>{d.value=!0;let r=s.value.filter(e=>w.value.includes(`${e.windTurbineID}&&${e.dauID}`)).map(e=>({...e,...t})),n=null;b.value==="pushConfig"?n=await p.fetchJfSetSFTPConfig(r):b.value==="otherConfig"&&(n=await p.fetchJfSetAdvancedParameters(r)),d.value=!1,n?(C.success("操作成功"),F()):C.error("操作失败")},Y=async t=>{const a=await h.startConnection("/Hubs/ServerPerformanceHub");k.value=a;let r=[];if(a){h.onReceiveMessage("ProgressMonitoringStarted",e=>{console.log("进度启动结果",e),s.value=t.map(u=>{if(e&&e.progressList&&e.progressList.length){let o=e.progressList.find(l=>l.dauID===u.dauID&&l.windTurbineID===u.windTurbineID);return{...u,process:o.progressPercentage}}return u})}),h.onReceiveMessage("ProgressUpdate",e=>{console.log("进度更新",e),s.value=t.map(u=>{if(e&&e.length){let o=e.find(l=>l.dauID===u.dauID&&l.windTurbineID===u.windTurbineID);return(o.progressPercentage==100||o.progressPercentage>100)&&!r.find(l=>l.dauID===u.dauID&&l.windTurbineID===u.windTurbineID)&&(r.push(u),r.length===t.length&&(C.success("获取录波数据成功！"),L())),{...u,process:Math.trunc(o.progressPercentage)}}return u})}),h.onReceiveMessage("CurrentProgressUpdate",e=>{console.log("当前进度响应",e)});const n={parkID:T.value,daus:t};h.sendMessage("StartProgressMonitoring",n)}else C.error("连接失败")},Z=async(t,a)=>{let r=s.value.filter(n=>t.includes(`${n.windTurbineID}&&${n.dauID}`));await p.fetchGetWaveFormData(r),g.value=[...m(),{title:"录波数据进度",dataIndex:"process",otherColumn:!0,columnWidth:120,formItemWidth:v}],Y(r),s.value=s.value.map(n=>t.includes(`${n.windTurbineID}&&${n.dauID}`)?{...n,process:0}:n),P.value=t},L=async()=>{k.value&&(await h.stopConnection(),k.value=!1)};return oe(()=>{L()}),(t,a)=>{const r=me,n=de,e=ge,u=ve;return y(),q(u,{spinning:d.value,size:"large"},{default:c(()=>[A("div",null,[f(ee,{ref:"table",size:"default","table-key":"0","table-title":"设备列表","table-columns":g.value,recordKey:o=>`${o.windTurbineID}&&${o.dauID}`,selectedkeys:P.value,"table-datas":s.value,noBatchApply:!0,selectedRows:!0},{rightButtons:c(({selectedRowKeys:o})=>[f(r,{type:"primary",onClick:l=>E(o,"startCollection"),disabled:!o.length},{default:c(()=>a[0]||(a[0]=[I(" 启动采集 ",-1)])),_:2,__:[0]},1032,["onClick","disabled"]),f(r,{onClick:l=>G(o,"stopCollection"),disabled:!o.length},{default:c(()=>a[1]||(a[1]=[I(" 停止采集 ",-1)])),_:2,__:[1]},1032,["onClick","disabled"]),f(r,{type:"primary",onClick:l=>H(o,"useMeasurementDefinition"),disabled:!o.length},{default:c(()=>a[2]||(a[2]=[I(" 测量定义下发 ",-1)])),_:2,__:[2]},1032,["onClick","disabled"]),f(r,{type:"primary",onClick:l=>R(o,"pushConfig"),disabled:!o.length},{default:c(()=>a[3]||(a[3]=[I(" 推送配置 ",-1)])),_:2,__:[3]},1032,["onClick","disabled"]),f(r,{type:"primary",onClick:l=>Z(o,"getObtainWaveform"),disabled:!o.length},{default:c(()=>a[4]||(a[4]=[I(" 获取录波数据 ",-1)])),_:2,__:[4]},1032,["onClick","disabled"]),f(r,{type:"primary",onClick:l=>Q(o,"otherConfig"),disabled:!o.length},{default:c(()=>a[5]||(a[5]=[I(" 高级参数配置 ",-1)])),_:2,__:[5]},1032,["onClick","disabled"])]),otherColumn:c(({record:o,text:l,column:$})=>[$.dataIndex==="dauOnOffStatus"?(y(),U(se,{key:0},[A("span",{class:V([l==2?"green":"gray","circle"])},null,2),I(" "+z(l==1?"离线":"在线"),1)],64)):$.dataIndex==="process"&&l!==""&&typeof l=="number"?(y(),q(n,{key:1,type:"circle",size:30,"stroke-color":{"0%":"#108ee9","100%":"#87d068"},percent:l},null,8,["percent"])):$.dataIndex==="operateStatus"?(y(),U("span",{key:2,class:V([l?"greenText":"redtext"])},z(l?"成功":"失败"),3)):le("",!0)]),_:1},8,["table-columns","recordKey","selectedkeys","table-datas"]),f(e,{maskClosable:!1,width:J.value,open:S.value,title:D.value,footer:"",destroyOnClose:!0,onCancel:F},{default:c(()=>[f(te,{titleCol:_.value,initFormData:M.value,formlayout:j.value,onSubmit:X},null,8,["titleCol","initFormData","formlayout"])]),_:1},8,["width","open","title"])])]),_:1},8,["spinning"])}}},Le=fe(be,[["__scopeId","data-v-93b40e6f"]]);export{Le as default};
