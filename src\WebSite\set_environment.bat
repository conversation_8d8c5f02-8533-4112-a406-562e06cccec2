@echo off
setlocal enabledelayedexpansion

echo ========================================
echo 设置 ASP.NET Core 环境变量
echo ========================================

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 错误: 需要管理员权限运行此脚本
    echo 请右键点击此脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo 当前环境变量设置:
echo ASPNETCORE_ENVIRONMENT = %ASPNETCORE_ENVIRONMENT%
echo.

echo 请选择要设置的环境:
echo 1. Development (开发环境)
echo 2. Production (生产环境)
echo 3. Staging (预发布环境)
echo 4. 查看当前设置
echo 5. 删除环境变量
echo 6. 退出
echo.

set /p choice=请输入选择 (1-6): 

if "%choice%"=="1" goto SET_DEV
if "%choice%"=="2" goto SET_PROD
if "%choice%"=="3" goto SET_STAGING
if "%choice%"=="4" goto SHOW_CURRENT
if "%choice%"=="5" goto DELETE_ENV
if "%choice%"=="6" goto EXIT

echo 无效选择
goto EXIT

:SET_DEV
echo 设置环境为 Development...
setx ASPNETCORE_ENVIRONMENT "Development" /M
echo 环境变量已设置为 Development
goto RESTART_INFO

:SET_PROD
echo 设置环境为 Production...
setx ASPNETCORE_ENVIRONMENT "Production" /M
echo 环境变量已设置为 Production
goto RESTART_INFO

:SET_STAGING
echo 设置环境为 Staging...
setx ASPNETCORE_ENVIRONMENT "Staging" /M
echo 环境变量已设置为 Staging
goto RESTART_INFO

:SHOW_CURRENT
echo ========================================
echo 当前系统环境变量:
reg query "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v ASPNETCORE_ENVIRONMENT 2>nul
if %errorLevel% neq 0 (
    echo ASPNETCORE_ENVIRONMENT 未设置
)
echo ========================================
goto EXIT

:DELETE_ENV
echo 删除 ASPNETCORE_ENVIRONMENT 环境变量...
reg delete "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v ASPNETCORE_ENVIRONMENT /f 2>nul
if %errorLevel% equ 0 (
    echo 环境变量已删除
) else (
    echo 环境变量不存在或删除失败
)
goto RESTART_INFO

:RESTART_INFO
echo.
echo ========================================
echo 重要提示:
echo 1. 环境变量更改需要重启服务才能生效
echo 2. 如果服务正在运行，请使用 manage_service.bat 重启服务
echo 3. 或者重启整个系统
echo ========================================
goto EXIT

:EXIT
pause
exit /b 0
